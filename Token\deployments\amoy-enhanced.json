{"network": "amoy", "chainId": "80002", "factoryAddress": "0x77b589fAd7fae8C155cb30940753dDC3A7Bc8F15", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0xc583800974e451d5eaf77410349b6fadc3e00bf459c1d7f76c3516877f4b5b4b", "timestamp": "2025-06-21T05:07:28.203Z", "contractType": "SecurityTokenFactoryEnhanced", "architecture": "Enhanced", "securityLevel": "HIGH", "features": {"emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "enumerationSupport": true, "gasOptimized": true, "sizeOptimized": true}, "securityAuditFixes": {"criticalIssues": "Fixed", "highPriorityIssues": "Fixed", "mediumPriorityIssues": "Fixed", "bestPractices": "Implemented"}}