const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Finding SecurityTokenCore admin...\n");

  const SECURITY_TOKEN_CORE_ADDRESS = "******************************************";

  try {
    // Get the SecurityTokenCore contract
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const tokenCore = SecurityTokenCore.attach(SECURITY_TOKEN_CORE_ADDRESS);

    console.log("📋 Contract address:", SECURITY_TOKEN_CORE_ADDRESS);

    // Get role constants
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();

    console.log("\n🔑 Role Constants:");
    console.log(`DEFAULT_ADMIN_ROLE: ${DEFAULT_ADMIN_ROLE}`);
    console.log(`MODULE_MANAGER_ROLE: ${MODULE_MANAGER_ROLE}`);

    // Try to find admin by checking recent events
    console.log("\n🔍 Checking for RoleGranted events...");
    
    try {
      const filter = tokenCore.filters.RoleGranted(DEFAULT_ADMIN_ROLE);
      const events = await tokenCore.queryFilter(filter, -10000); // Last 10k blocks
      
      if (events.length > 0) {
        console.log("✅ Found RoleGranted events:");
        events.forEach((event, index) => {
          console.log(`  ${index + 1}. Admin: ${event.args.account} (Block: ${event.blockNumber})`);
        });
      } else {
        console.log("⚠️ No RoleGranted events found in recent blocks");
      }
    } catch (eventError) {
      console.log("⚠️ Could not query events:", eventError.message);
    }

    // Check if current account has any roles
    const [deployer] = await ethers.getSigners();
    console.log(`\n👤 Current account: ${deployer.address}`);
    
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    
    console.log(`Has DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`Has MODULE_MANAGER_ROLE: ${hasModuleManager}`);

    // Check some common admin addresses
    const commonAddresses = [
      "******************************************", // Current deployer
      "******************************************", // Zero address
    ];

    console.log("\n🔍 Checking common addresses for admin roles:");
    for (const address of commonAddresses) {
      try {
        const hasAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, address);
        const hasModule = await tokenCore.hasRole(MODULE_MANAGER_ROLE, address);
        console.log(`${address}: Admin=${hasAdmin}, Module=${hasModule}`);
      } catch (error) {
        console.log(`${address}: Error checking roles`);
      }
    }

    // Try to get the role admin
    console.log("\n🔍 Checking role admin...");
    try {
      const roleAdmin = await tokenCore.getRoleAdmin(MODULE_MANAGER_ROLE);
      console.log(`MODULE_MANAGER_ROLE admin: ${roleAdmin}`);
      
      if (roleAdmin === DEFAULT_ADMIN_ROLE) {
        console.log("✅ DEFAULT_ADMIN_ROLE can grant MODULE_MANAGER_ROLE");
      }
    } catch (error) {
      console.log("⚠️ Could not get role admin:", error.message);
    }

    // Check token basic info
    console.log("\n📊 Token Information:");
    try {
      const name = await tokenCore.name();
      const symbol = await tokenCore.symbol();
      const version = await tokenCore.version();
      
      console.log(`Name: ${name || 'Not set'}`);
      console.log(`Symbol: ${symbol || 'Not set'}`);
      console.log(`Version: ${version}`);
    } catch (error) {
      console.log("⚠️ Could not get token info:", error.message);
    }

    console.log("\n💡 NEXT STEPS:");
    console.log("1. If you find an admin address above, use that account to register the module");
    console.log("2. If no admin found, the token might need to be redeployed with proper admin setup");
    console.log("3. Alternatively, deploy a new token with the current account as admin");

  } catch (error) {
    console.error("❌ Error finding admin:", error);
    throw error;
  }
}

// Execute
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
