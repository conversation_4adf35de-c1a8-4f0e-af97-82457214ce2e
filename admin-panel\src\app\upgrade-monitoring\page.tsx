'use client';

import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Import ABIs
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import UpgradeManagerABI from '@/contracts/UpgradeManager.json';

// Contract addresses
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  lastChecked: Date;
}

interface UpgradeMetrics {
  totalUpgrades: number;
  pendingUpgrades: number;
  emergencyUpgrades: number;
  averageUpgradeTime: number;
  lastUpgradeTime: Date | null;
  emergencyModeActive: boolean;
  emergencyModeTimeRemaining: number;
}

interface SecurityAlert {
  id: string;
  type: 'emergency' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
}

export default function UpgradeMonitoringPage() {
  const [provider, setProvider] = useState<ethers.JsonRpcProvider | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    message: 'System initializing...',
    lastChecked: new Date()
  });
  const [metrics, setMetrics] = useState<UpgradeMetrics>({
    totalUpgrades: 0,
    pendingUpgrades: 0,
    emergencyUpgrades: 0,
    averageUpgradeTime: 0,
    lastUpgradeTime: null,
    emergencyModeActive: false,
    emergencyModeTimeRemaining: 0
  });
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Initialize provider
  useEffect(() => {
    const initProvider = async () => {
      try {
        const rpcUrl = 'https://rpc-amoy.polygon.technology/';
        const provider = new ethers.JsonRpcProvider(rpcUrl);
        setProvider(provider);
        console.log('Provider initialized for monitoring');
      } catch (error) {
        console.error('Failed to initialize provider:', error);
        addAlert('emergency', 'Provider Error', 'Failed to connect to blockchain network');
      }
    };

    initProvider();
  }, []);

  // Add alert
  const addAlert = (type: SecurityAlert['type'], title: string, message: string) => {
    const alert: SecurityAlert = {
      id: Date.now().toString(),
      type,
      title,
      message,
      timestamp: new Date(),
      resolved: false
    };
    setAlerts(prev => [alert, ...prev.slice(0, 9)]); // Keep last 10 alerts
  };

  // Resolve alert
  const resolveAlert = (id: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === id ? { ...alert, resolved: true } : alert
    ));
  };

  // Check system health
  const checkSystemHealth = async () => {
    if (!provider) return;

    try {
      const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
      const tokenCore = new ethers.Contract(SECURITY_TOKEN_CORE_ADDRESS!, SecurityTokenCoreABI, provider);

      // Check if contracts are responsive
      const [emergencyModeActive, pendingUpgradeIds, tokenVersion] = await Promise.all([
        upgradeManager.isEmergencyModeActive(),
        upgradeManager.getPendingUpgradeIds(),
        tokenCore.version()
      ]);

      // Determine health status
      let status: SystemHealth['status'] = 'healthy';
      let message = 'All systems operational';

      if (emergencyModeActive) {
        status = 'warning';
        message = 'Emergency mode is active';
        addAlert('warning', 'Emergency Mode Active', 'The upgrade system is in emergency mode');
      }

      if (pendingUpgradeIds.length > 5) {
        status = 'warning';
        message = `High number of pending upgrades (${pendingUpgradeIds.length})`;
        addAlert('warning', 'High Pending Upgrades', `${pendingUpgradeIds.length} upgrades are pending`);
      }

      setSystemHealth({
        status,
        message,
        lastChecked: new Date()
      });

    } catch (error: any) {
      console.error('Health check failed:', error);
      setSystemHealth({
        status: 'critical',
        message: `Health check failed: ${error.message}`,
        lastChecked: new Date()
      });
      addAlert('emergency', 'Health Check Failed', error.message);
    }
  };

  // Update metrics
  const updateMetrics = async () => {
    if (!provider) return;

    try {
      const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
      
      const [
        emergencyModeActive,
        pendingUpgradeIds,
        emergencyModeActivatedAt,
        emergencyModeDuration
      ] = await Promise.all([
        upgradeManager.isEmergencyModeActive(),
        upgradeManager.getPendingUpgradeIds(),
        upgradeManager.emergencyModeActivatedAt(),
        upgradeManager.EMERGENCY_MODE_DURATION()
      ]);

      // Get upgrade history
      const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
      const history = await upgradeManager.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);

      // Calculate emergency mode time remaining
      let emergencyModeTimeRemaining = 0;
      if (emergencyModeActive && emergencyModeActivatedAt > 0) {
        const activatedAt = Number(emergencyModeActivatedAt);
        const duration = Number(emergencyModeDuration);
        const now = Math.floor(Date.now() / 1000);
        emergencyModeTimeRemaining = Math.max(0, (activatedAt + duration) - now);
      }

      // Count emergency upgrades
      const emergencyUpgrades = history.filter((record: any) => record.isEmergency).length;

      // Calculate average upgrade time (simplified)
      const averageUpgradeTime = history.length > 0 ? 48 * 60 * 60 : 0; // 48 hours default

      // Get last upgrade time
      let lastUpgradeTime = null;
      if (history.length > 0) {
        const lastUpgrade = history[history.length - 1];
        lastUpgradeTime = new Date(Number(lastUpgrade.timestamp) * 1000);
      }

      setMetrics({
        totalUpgrades: history.length,
        pendingUpgrades: pendingUpgradeIds.length,
        emergencyUpgrades,
        averageUpgradeTime,
        lastUpgradeTime,
        emergencyModeActive,
        emergencyModeTimeRemaining
      });

      setLastUpdate(new Date());

    } catch (error: any) {
      console.error('Failed to update metrics:', error);
      addAlert('warning', 'Metrics Update Failed', error.message);
    }
  };

  // Start/stop monitoring
  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };

  // Monitoring loop
  useEffect(() => {
    if (!isMonitoring || !provider) return;

    const interval = setInterval(async () => {
      await Promise.all([
        checkSystemHealth(),
        updateMetrics()
      ]);
    }, 30000); // Check every 30 seconds

    // Initial check
    checkSystemHealth();
    updateMetrics();

    return () => clearInterval(interval);
  }, [isMonitoring, provider]);

  // Format time remaining
  const formatTimeRemaining = (seconds: number) => {
    if (seconds <= 0) return 'Expired';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours}h ${minutes}m ${secs}s`;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Upgrade System Monitoring</h1>
          <p className="text-gray-600">
            Real-time monitoring of the modular upgrade system
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={toggleMonitoring}
            className={`font-bold py-2 px-4 rounded ${
              isMonitoring 
                ? 'bg-red-600 hover:bg-red-700 text-white' 
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {isMonitoring ? '⏹️ Stop Monitoring' : '▶️ Start Monitoring'}
          </button>
        </div>
      </div>

      {/* System Health Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`bg-white rounded-lg shadow p-6 border-l-4 ${
          systemHealth.status === 'healthy' ? 'border-green-500' :
          systemHealth.status === 'warning' ? 'border-yellow-500' :
          'border-red-500'
        }`}>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              systemHealth.status === 'healthy' ? 'bg-green-500' :
              systemHealth.status === 'warning' ? 'bg-yellow-500' :
              'bg-red-500'
            }`}></div>
            <div>
              <h3 className="text-lg font-semibold">System Health</h3>
              <p className="text-sm text-gray-600">{systemHealth.message}</p>
              <p className="text-xs text-gray-400">
                Last checked: {systemHealth.lastChecked.toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Monitoring Status</h3>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`}></div>
            <span className={isMonitoring ? 'text-green-600' : 'text-gray-600'}>
              {isMonitoring ? 'Active' : 'Inactive'}
            </span>
          </div>
          {isMonitoring && (
            <p className="text-xs text-gray-400 mt-2">
              Last update: {lastUpdate.toLocaleTimeString()}
            </p>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Emergency Mode</h3>
          <div className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              metrics.emergencyModeActive ? 'bg-red-500 animate-pulse' : 'bg-gray-400'
            }`}></div>
            <span className={metrics.emergencyModeActive ? 'text-red-600' : 'text-gray-600'}>
              {metrics.emergencyModeActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          {metrics.emergencyModeActive && metrics.emergencyModeTimeRemaining > 0 && (
            <p className="text-xs text-red-600 mt-2">
              Time remaining: {formatTimeRemaining(metrics.emergencyModeTimeRemaining)}
            </p>
          )}
        </div>
      </div>

      {/* Metrics Dashboard */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-blue-600">{metrics.totalUpgrades}</div>
          <div className="text-sm text-gray-600">Total Upgrades</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-yellow-600">{metrics.pendingUpgrades}</div>
          <div className="text-sm text-gray-600">Pending Upgrades</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-red-600">{metrics.emergencyUpgrades}</div>
          <div className="text-sm text-gray-600">Emergency Upgrades</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-green-600">
            {Math.round(metrics.averageUpgradeTime / 3600)}h
          </div>
          <div className="text-sm text-gray-600">Avg Upgrade Time</div>
        </div>
      </div>

      {/* Security Alerts */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Security Alerts</h2>
          <span className="text-sm text-gray-500">
            {alerts.filter(a => !a.resolved).length} active alerts
          </span>
        </div>
        
        {alerts.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No alerts</p>
        ) : (
          <div className="space-y-3">
            {alerts.slice(0, 5).map((alert) => (
              <div key={alert.id} className={`border rounded-lg p-3 ${
                alert.resolved ? 'bg-gray-50 opacity-60' : 
                alert.type === 'emergency' ? 'bg-red-50 border-red-200' :
                alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                'bg-blue-50 border-blue-200'
              }`}>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className={`w-2 h-2 rounded-full ${
                        alert.type === 'emergency' ? 'bg-red-500' :
                        alert.type === 'warning' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`}></span>
                      <span className="font-medium">{alert.title}</span>
                      {alert.resolved && (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                          Resolved
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {alert.timestamp.toLocaleString()}
                    </p>
                  </div>
                  {!alert.resolved && (
                    <button
                      onClick={() => resolveAlert(alert.id)}
                      className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Last Upgrade Info */}
      {metrics.lastUpgradeTime && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Last Upgrade</h2>
          <div className="text-sm text-gray-600">
            <p>Time: {metrics.lastUpgradeTime.toLocaleString()}</p>
            <p>Time ago: {Math.round((Date.now() - metrics.lastUpgradeTime.getTime()) / (1000 * 60 * 60 * 24))} days ago</p>
          </div>
        </div>
      )}
    </div>
  );
}
