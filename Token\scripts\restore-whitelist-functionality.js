const { ethers } = require("hardhat");

// Token proxy address to upgrade
const PROXY_ADDRESS = "******************************************";

// Addresses that need to be re-whitelisted
const ADDRESSES_TO_WHITELIST = [
  "******************************************", // Your main wallet (60 tokens)
  "******************************************", // Address that received force transfer (2 tokens)
];

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔧 RESTORING WHITELIST FUNCTIONALITY");
  console.log("Token Address:", PROXY_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Deploy new SecurityTokenCore implementation with direct whitelist storage
    console.log("\n📦 Deploying SecurityTokenCore with direct whitelist functionality...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    
    const newImplAddress = await newImplementation.getAddress();
    console.log("New implementation deployed at:", newImplAddress);

    // Get the current contract
    const currentContract = await ethers.getContractAt("SecurityTokenCore", PROXY_ADDRESS);

    // Upgrade the proxy to the new implementation
    console.log("\n🔄 Upgrading to implementation with direct whitelist...");
    const upgradeData = "0x"; // No initialization data needed
    const tx = await currentContract.upgradeToAndCall(newImplAddress, upgradeData);
    await tx.wait();
    
    console.log("✅ Upgrade successful! Transaction:", tx.hash);

    // Test the new whitelist functionality
    console.log("\n🧪 Testing new whitelist functionality...");
    
    // Check current status of addresses
    console.log("Current whitelist status:");
    for (const address of ADDRESSES_TO_WHITELIST) {
      const isWhitelisted = await currentContract.isWhitelisted(address);
      const isVerified = await currentContract.isVerified(address);
      const balance = await currentContract.balanceOf(address);
      const decimals = await currentContract.decimals();
      
      console.log(`  ${address}:`);
      console.log(`    Whitelisted: ${isWhitelisted}`);
      console.log(`    Verified: ${isVerified}`);
      console.log(`    Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
    }

    // Re-whitelist the addresses
    console.log("\n✅ Re-whitelisting addresses...");
    
    for (const address of ADDRESSES_TO_WHITELIST) {
      try {
        console.log(`Whitelisting ${address}...`);
        const whitelistTx = await currentContract.addToWhitelist(address);
        await whitelistTx.wait();
        console.log(`✅ Successfully whitelisted ${address}`);
      } catch (whitelistError) {
        console.log(`❌ Failed to whitelist ${address}:`, whitelistError.message);
      }
    }

    // Verify the whitelist restoration
    console.log("\n🔍 Verifying whitelist restoration:");
    
    let successfullyWhitelisted = 0;
    for (const address of ADDRESSES_TO_WHITELIST) {
      const isWhitelisted = await currentContract.isWhitelisted(address);
      const isVerified = await currentContract.isVerified(address);
      
      console.log(`  ${address}:`);
      console.log(`    Whitelisted: ${isWhitelisted}`);
      console.log(`    Verified: ${isVerified}`);
      
      if (isWhitelisted && isVerified) {
        successfullyWhitelisted++;
        console.log(`    ✅ Status: COMPLIANT`);
      } else {
        console.log(`    ❌ Status: NON-COMPLIANT`);
      }
    }

    console.log(`\n📊 Whitelist restoration summary:`);
    console.log(`  Total addresses: ${ADDRESSES_TO_WHITELIST.length}`);
    console.log(`  Successfully whitelisted: ${successfullyWhitelisted}`);
    console.log(`  Failed: ${ADDRESSES_TO_WHITELIST.length - successfullyWhitelisted}`);

    // Test transfer functionality
    console.log("\n🧪 Testing transfer functionality...");
    
    if (successfullyWhitelisted >= 2) {
      try {
        const decimals = await currentContract.decimals();
        const testAmount = ethers.parseUnits("1", decimals);
        
        // Test if transfers work between whitelisted addresses
        await currentContract.transfer.staticCall(ADDRESSES_TO_WHITELIST[1], testAmount);
        console.log("✅ Transfers between whitelisted addresses work!");
      } catch (transferError) {
        console.log("❌ Transfer test failed:", transferError.message);
      }
    }

    // Test force transfer functionality
    console.log("\n🔄 Testing force transfer functionality...");
    
    try {
      const decimals = await currentContract.decimals();
      const testAmount = ethers.parseUnits("1", decimals);
      
      // Test force transfer between whitelisted addresses (should work)
      await currentContract.forcedTransfer.staticCall(
        ADDRESSES_TO_WHITELIST[0], 
        ADDRESSES_TO_WHITELIST[1], 
        testAmount
      );
      console.log("✅ Force transfers between whitelisted addresses work!");
      
      // Test force transfer to non-whitelisted address (should fail)
      const randomAddress = ethers.Wallet.createRandom().address;
      try {
        await currentContract.forcedTransfer.staticCall(
          ADDRESSES_TO_WHITELIST[0], 
          randomAddress, 
          testAmount
        );
        console.log("❌ SECURITY ISSUE: Force transfer to non-whitelisted address works!");
      } catch (securityError) {
        console.log("✅ Security confirmed: Force transfer to non-whitelisted address fails");
      }
      
    } catch (forceError) {
      console.log("❌ Force transfer test failed:", forceError.message);
    }

    console.log("\n🎉 WHITELIST FUNCTIONALITY RESTORED!");
    console.log("📋 Summary:");
    console.log("  ✅ Direct whitelist storage implemented");
    console.log("  ✅ Existing token holders re-whitelisted");
    console.log("  ✅ Transfer functionality restored");
    console.log("  ✅ Force transfer security maintained");
    console.log("  ✅ Admin panel whitelist will now show addresses");

    console.log(`\n🔗 View on Polygonscan: https://amoy.polygonscan.com/address/${PROXY_ADDRESS}`);

  } catch (error) {
    console.error("❌ Whitelist restoration failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
