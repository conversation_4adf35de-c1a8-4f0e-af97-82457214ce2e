import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function mint(address to, uint256 amount) external",
  "function balanceOf(address account) external view returns (uint256)",
  "function totalSupply() external view returns (uint256)",
  "function maxSupply() external view returns (uint256)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, toAddress, amount } = await request.json();

    if (!tokenAddress || !toAddress || !amount) {
      return NextResponse.json(
        { error: 'Token address, recipient address, and amount are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(toAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Validate amount
    const amountNum = parseInt(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount value' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Get current values for comparison
    const currentBalance = await tokenContract.balanceOf(toAddress);
    const currentTotalSupply = await tokenContract.totalSupply();
    const maxSupply = await tokenContract.maxSupply();
    
    console.log('Current balance:', currentBalance.toString());
    console.log('Current total supply:', currentTotalSupply.toString());
    console.log('Max supply:', maxSupply.toString());
    console.log('Amount to mint:', amount);

    // Convert to proper units (assuming 0 decimals based on our token)
    const amountBN = ethers.parseUnits(amount, 0);

    // Check if minting would exceed max supply
    if (currentTotalSupply + amountBN > maxSupply) {
      return NextResponse.json(
        { error: 'Minting would exceed maximum supply' },
        { status: 400 }
      );
    }

    // Multiple strategies to handle Amoy testnet issues
    const strategies = [
      // Strategy 1: Standard contract call
      async () => {
        const tx = await tokenContract.mint(toAddress, amountBN, {
          gasLimit: 400000,
          gasPrice: ethers.parseUnits('30', 'gwei')
        });
        return tx.wait();
      },
      // Strategy 2: Raw transaction approach
      async () => {
        const iface = new ethers.Interface(SECURITY_TOKEN_CORE_ABI);
        const data = iface.encodeFunctionData('mint', [toAddress, amountBN]);
        const nonce = await signer.getNonce();

        const tx = await signer.sendTransaction({
          to: tokenAddress,
          data: data,
          gasLimit: 400000,
          gasPrice: ethers.parseUnits('30', 'gwei'),
          nonce: nonce
        });
        return tx.wait();
      }
    ];

    let lastError;
    let receipt;

    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`Attempting mint strategy ${i + 1}...`);
        receipt = await strategies[i]();
        console.log(`Mint successful with strategy ${i + 1}. Tx hash:`, receipt.hash);
        break; // Success, exit loop
      } catch (error: any) {
        console.error(`Strategy ${i + 1} failed:`, error);
        lastError = error;

        // If this is not the last strategy, wait and try the next one
        if (i < strategies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // If all strategies failed
    if (!receipt) {
      throw lastError || new Error('All mint strategies failed');
    }

    // Verify minting
    const newBalance = await tokenContract.balanceOf(toAddress);
    const newTotalSupply = await tokenContract.totalSupply();

    console.log('Tokens minted successfully:', receipt.hash);

    return NextResponse.json({
      success: true,
      message: 'Tokens minted successfully',
      txHash: receipt.hash,
      toAddress,
      amount: amount,
      oldBalance: currentBalance.toString(),
      newBalance: newBalance.toString(),
      oldTotalSupply: currentTotalSupply.toString(),
      newTotalSupply: newTotalSupply.toString(),
      tokenAddress,
      strategy: 'Enhanced with fallback strategies'
    });

  } catch (error: any) {
    console.error('Error minting tokens:', error);
    return NextResponse.json(
      { error: `Failed to mint tokens: ${error.message}` },
      { status: 500 }
    );
  }
}
