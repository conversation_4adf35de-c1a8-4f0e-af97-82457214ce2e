const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying KYC Claims Module...\n");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  // Existing contract addresses
  const CLAIM_REGISTRY_ADDRESS = "******************************************";
  const MODULAR_FACTORY_ADDRESS = "******************************************";

  console.log("📋 Using existing contracts:");
  console.log(`- ClaimRegistry: ${CLAIM_REGISTRY_ADDRESS}`);
  console.log(`- ModularTokenFactory: ${MODULAR_FACTORY_ADDRESS}\n`);

  try {
    // Deploy KYCClaimsModule
    console.log("📦 Deploying KYCClaimsModule...");
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    
    const kycModule = await KYCClaimsModule.deploy();
    await kycModule.waitForDeployment();
    
    const kycModuleAddress = await kycModule.getAddress();
    console.log("✅ KYCClaimsModule deployed to:", kycModuleAddress);

    // Initialize the module
    console.log("\n🔧 Initializing KYCClaimsModule...");
    const initTx = await kycModule.initialize(
      CLAIM_REGISTRY_ADDRESS,
      deployer.address
    );
    await initTx.wait();
    console.log("✅ KYCClaimsModule initialized");

    // Verify initialization
    console.log("\n🔍 Verifying initialization...");
    const claimRegistry = await kycModule.claimRegistry();
    const hasAdminRole = await kycModule.hasRole(await kycModule.DEFAULT_ADMIN_ROLE(), deployer.address);
    const hasModuleAdminRole = await kycModule.hasRole(await kycModule.MODULE_ADMIN_ROLE(), deployer.address);
    const hasClaimIssuerRole = await kycModule.hasRole(await kycModule.CLAIM_ISSUER_ROLE(), deployer.address);
    
    console.log("✅ Claim registry address:", claimRegistry);
    console.log("✅ Has admin role:", hasAdminRole);
    console.log("✅ Has module admin role:", hasModuleAdminRole);
    console.log("✅ Has claim issuer role:", hasClaimIssuerRole);

    // Test the module with a sample token configuration
    console.log("\n🧪 Testing module configuration...");
    
    // Get a test token address (we'll use the FreshTest token we deployed earlier)
    const TEST_TOKEN_ADDRESS = "0x663b561d089Aa42ED19E2D1e0E16E5B66f9CcbCF";
    
    // Configure the test token with KYC and claims
    const requiredClaims = [
      10101010000001, // KYC_VERIFICATION
      10101010000004  // GENERAL_QUALIFICATION
    ];
    
    const configTx = await kycModule.configureTokenClaims(
      TEST_TOKEN_ADDRESS,
      requiredClaims,
      true,  // KYC enabled
      true   // Claims enabled
    );
    await configTx.wait();
    console.log("✅ Test token configured with KYC and claims");

    // Test KYC approval for a user
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    console.log(`\n👤 Testing KYC approval for user: ${TEST_USER}`);
    
    const kycTx = await kycModule.approveKYC(TEST_TOKEN_ADDRESS, TEST_USER);
    await kycTx.wait();
    console.log("✅ KYC approved for test user");

    // Check verification status
    const status = await kycModule.getVerificationStatus(TEST_TOKEN_ADDRESS, TEST_USER);
    console.log("✅ Verification status:", {
      kycApproved: status[0],
      whitelisted: status[1],
      eligible: status[2],
      method: status[3]
    });

    // Test issuing a custom claim
    console.log("\n🏷️ Testing custom claim issuance...");
    const customClaimData = ethers.AbiCoder.defaultAbiCoder().encode(
      ['string', 'uint256'],
      ['ACCREDITED_INVESTOR', Math.floor(Date.now() / 1000)]
    );
    
    const claimTx = await kycModule.issueCustomClaim(
      TEST_USER,
      10101010000002, // ACCREDITED_INVESTOR
      customClaimData,
      "https://example.com/accreditation",
      0 // Never expires
    );
    const claimReceipt = await claimTx.wait();
    console.log("✅ Custom claim issued");

    // Get the claim ID from the event
    const claimEvent = claimReceipt.logs.find(log => {
      try {
        const parsed = kycModule.interface.parseLog(log);
        return parsed.name === 'ClaimIssued';
      } catch {
        return false;
      }
    });

    if (claimEvent) {
      const parsedEvent = kycModule.interface.parseLog(claimEvent);
      console.log("✅ Claim ID:", parsedEvent.args.claimId);
    }

    console.log("\n🎉 KYC Claims Module deployment completed successfully!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(60));
    console.log(`KYCClaimsModule: ${kycModuleAddress}`);
    console.log(`ClaimRegistry: ${CLAIM_REGISTRY_ADDRESS}`);
    console.log(`ModularTokenFactory: ${MODULAR_FACTORY_ADDRESS}`);
    console.log(`Test Token: ${TEST_TOKEN_ADDRESS}`);
    console.log(`Test User: ${TEST_USER}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log(`Deployer: ${deployer.address}`);
    console.log("=" .repeat(60));

    // Save deployment info
    const deploymentInfo = {
      network: (await deployer.provider.getNetwork()).name,
      chainId: (await deployer.provider.getNetwork()).chainId,
      contracts: {
        KYCClaimsModule: kycModuleAddress,
        ClaimRegistry: CLAIM_REGISTRY_ADDRESS,
        ModularTokenFactory: MODULAR_FACTORY_ADDRESS
      },
      testConfiguration: {
        testToken: TEST_TOKEN_ADDRESS,
        testUser: TEST_USER,
        requiredClaims: requiredClaims,
        kycEnabled: true,
        claimsEnabled: true
      },
      deployer: deployer.address,
      deploymentTime: new Date().toISOString()
    };

    console.log("\n💾 Deployment info:");
    console.log(JSON.stringify(deploymentInfo, null, 2));

    return {
      kycModuleAddress,
      claimRegistryAddress: CLAIM_REGISTRY_ADDRESS,
      factoryAddress: MODULAR_FACTORY_ADDRESS,
      deploymentInfo
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
