import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function pause() external",
  "function unpause() external", 
  "function paused() external view returns (bool)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, action } = await request.json();

    if (!tokenAddress) {
      return NextResponse.json({ error: 'Token address is required' }, { status: 400 });
    }

    if (!action || !['pause', 'unpause'].includes(action)) {
      return NextResponse.json({ error: 'Action must be "pause" or "unpause"' }, { status: 400 });
    }

    // Get environment variables
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
    const rpcUrl = process.env.AMOY_RPC_URL || process.env.NEXT_PUBLIC_AMOY_RPC_URL;

    if (!privateKey) {
      return NextResponse.json({ error: 'Admin private key not configured' }, { status: 500 });
    }

    if (!rpcUrl) {
      return NextResponse.json({ error: 'RPC URL not configured' }, { status: 500 });
    }

    console.log(`Attempting to ${action} token at address:`, tokenAddress);

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Check current pause status
    const currentlyPaused = await tokenContract.paused();
    console.log('Current pause status:', currentlyPaused);

    // Validate action makes sense
    if (action === 'pause' && currentlyPaused) {
      return NextResponse.json({ 
        success: true, 
        message: 'Token is already paused',
        alreadyInDesiredState: true 
      });
    }

    if (action === 'unpause' && !currentlyPaused) {
      return NextResponse.json({ 
        success: true, 
        message: 'Token is already unpaused',
        alreadyInDesiredState: true 
      });
    }

    // Multiple strategies to handle Amoy testnet issues
    const strategies = [
      // Strategy 1: Standard contract call
      async () => {
        const tx = action === 'pause' 
          ? await tokenContract.pause({ gasLimit: 500000, gasPrice: ethers.parseUnits('30', 'gwei') })
          : await tokenContract.unpause({ gasLimit: 500000, gasPrice: ethers.parseUnits('30', 'gwei') });
        return tx.wait();
      },
      // Strategy 2: Raw transaction (like the working script)
      async () => {
        const functionSelector = action === 'pause' ? '0x8456cb59' : '0x3f4ba83a';
        const nonce = await signer.getNonce();
        
        const tx = await signer.sendTransaction({
          to: tokenAddress,
          data: functionSelector,
          gasLimit: 500000,
          gasPrice: ethers.parseUnits('30', 'gwei'),
          nonce: nonce
        });
        return tx.wait();
      }
    ];

    let lastError;
    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`Attempting ${action} strategy ${i + 1}...`);
        const receipt = await strategies[i]();
        console.log(`${action} successful with strategy ${i + 1}. Tx hash:`, receipt.hash);

        // Verify the action worked
        const newPauseStatus = await tokenContract.paused();
        const expectedStatus = action === 'pause';
        
        if (newPauseStatus === expectedStatus) {
          return NextResponse.json({
            success: true,
            message: `Token ${action}d successfully`,
            transactionHash: receipt.hash,
            blockNumber: receipt.blockNumber,
            strategy: i + 1
          });
        } else {
          throw new Error(`Transaction succeeded but pause status didn't change as expected`);
        }
      } catch (error: any) {
        console.error(`Strategy ${i + 1} failed:`, error);
        lastError = error;
        
        // If this is not the last strategy, wait and try the next one
        if (i < strategies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // All strategies failed
    console.error('All strategies failed. Last error:', lastError);
    
    return NextResponse.json({
      error: `Failed to ${action} token after trying multiple methods`,
      details: lastError?.message || 'Unknown error',
      suggestion: 'The Amoy testnet may be experiencing issues. Please try again in a few minutes.'
    }, { status: 500 });

  } catch (error: any) {
    console.error('Toggle pause API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message
    }, { status: 500 });
  }
}
