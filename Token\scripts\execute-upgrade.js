const { ethers } = require("hardhat");

/**
 * <PERSON><PERSON><PERSON> to execute a scheduled upgrade after timelock delay
 * Usage: 
 * - Set environment variables: UPGRADE_ID, UPGRADE_MANAGER_ADDRESS
 * - Or pass them as command line arguments
 */

async function main() {
  console.log("⏰ Execute Scheduled Upgrade Script");
  console.log("===================================");

  const [deployer] = await ethers.getSigners();
  console.log("Executing with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Get parameters from environment or command line
  const upgradeId = process.env.UPGRADE_ID || process.argv[2];
  const upgradeManagerAddress = process.env.UPGRADE_MANAGER_ADDRESS || process.argv[3];

  if (!upgradeId || !upgradeManagerAddress) {
    console.error("❌ Missing required parameters:");
    console.error("Usage: npx hardhat run scripts/execute-upgrade.js --network <network>");
    console.error("Environment variables required:");
    console.error("- UPGRADE_ID: The upgrade identifier from the scheduled upgrade");
    console.error("- UPGRADE_MANAGER_ADDRESS: Address of the UpgradeManager contract");
    process.exit(1);
  }

  console.log("\n📋 Execution Parameters:");
  console.log("Upgrade ID:", upgradeId);
  console.log("UpgradeManager:", upgradeManagerAddress);

  try {
    // Connect to UpgradeManager
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    const upgradeManager = UpgradeManager.attach(upgradeManagerAddress);

    // Get upgrade details
    console.log("\n🔍 Checking upgrade details...");
    const pendingUpgrade = await upgradeManager.pendingUpgrades(upgradeId);
    
    if (pendingUpgrade.proxy === ethers.ZeroAddress) {
      throw new Error("Upgrade not found or invalid upgrade ID");
    }

    console.log("Module ID:", pendingUpgrade.moduleId);
    console.log("Proxy Address:", pendingUpgrade.proxy);
    console.log("New Implementation:", pendingUpgrade.newImplementation);
    console.log("Execute Time:", new Date(Number(pendingUpgrade.executeTime) * 1000).toISOString());
    console.log("Description:", pendingUpgrade.description);
    console.log("Executed:", pendingUpgrade.executed);
    console.log("Cancelled:", pendingUpgrade.cancelled);

    // Check if already executed
    if (pendingUpgrade.executed) {
      console.log("✅ Upgrade has already been executed");
      return;
    }

    // Check if cancelled
    if (pendingUpgrade.cancelled) {
      console.log("❌ Upgrade has been cancelled");
      return;
    }

    // Check timelock
    const currentTime = Math.floor(Date.now() / 1000);
    const executeTime = Number(pendingUpgrade.executeTime);
    
    if (currentTime < executeTime) {
      const timeRemaining = executeTime - currentTime;
      const hours = Math.floor(timeRemaining / 3600);
      const minutes = Math.floor((timeRemaining % 3600) / 60);
      
      console.log(`⏰ Timelock not yet expired. Time remaining: ${hours}h ${minutes}m`);
      console.log("Current time:", new Date(currentTime * 1000).toISOString());
      console.log("Execute time:", new Date(executeTime * 1000).toISOString());
      return;
    }

    console.log("✅ Timelock has expired. Proceeding with upgrade execution...");

    // Check current implementation before upgrade
    try {
      const currentImpl = await deployer.provider.call({
        to: pendingUpgrade.proxy,
        data: "0x5c60da1b" // implementation() selector
      });
      const currentImplementation = ethers.getAddress("0x" + currentImpl.slice(-40));
      console.log("Current Implementation:", currentImplementation);
    } catch (error) {
      console.log("Could not retrieve current implementation:", error.message);
    }

    // Execute the upgrade
    console.log("\n🚀 Executing upgrade...");
    const executeTx = await upgradeManager.executeUpgrade(upgradeId);
    
    console.log("Transaction hash:", executeTx.hash);
    console.log("⏳ Waiting for confirmation...");
    
    const receipt = await executeTx.wait();
    console.log("✅ Upgrade executed successfully!");
    console.log("Gas used:", receipt.gasUsed.toString());

    // Check for events
    const upgradeExecutedEvent = receipt.logs.find(log => {
      try {
        const parsed = upgradeManager.interface.parseLog(log);
        return parsed.name === "UpgradeExecuted";
      } catch {
        return false;
      }
    });

    if (upgradeExecutedEvent) {
      const parsedEvent = upgradeManager.interface.parseLog(upgradeExecutedEvent);
      console.log("\n📋 Upgrade Event Details:");
      console.log("Module ID:", parsedEvent.args.moduleId);
      console.log("Proxy:", parsedEvent.args.proxy);
      console.log("Old Implementation:", parsedEvent.args.oldImplementation);
      console.log("New Implementation:", parsedEvent.args.newImplementation);
      console.log("Version:", parsedEvent.args.version);
    }

    // Verify the upgrade was successful
    console.log("\n🔍 Verifying upgrade...");
    const updatedPendingUpgrade = await upgradeManager.pendingUpgrades(upgradeId);
    console.log("Upgrade marked as executed:", updatedPendingUpgrade.executed);

    // Check new implementation
    try {
      const newImpl = await deployer.provider.call({
        to: pendingUpgrade.proxy,
        data: "0x5c60da1b" // implementation() selector
      });
      const newImplementation = ethers.getAddress("0x" + newImpl.slice(-40));
      console.log("New Implementation:", newImplementation);
      
      if (newImplementation.toLowerCase() === pendingUpgrade.newImplementation.toLowerCase()) {
        console.log("✅ Implementation successfully updated");
      } else {
        console.log("⚠️ Warning: Implementation address doesn't match expected");
      }
    } catch (error) {
      console.log("Could not verify new implementation:", error.message);
    }

    // Get upgrade history
    console.log("\n📚 Recent Upgrade History:");
    try {
      const history = await upgradeManager.getUpgradeHistory(pendingUpgrade.moduleId);
      const recentHistory = history.slice(-3); // Last 3 upgrades
      
      if (recentHistory.length === 0) {
        console.log("No upgrade history found");
      } else {
        recentHistory.forEach((record, index) => {
          console.log(`${index + 1}. ${new Date(Number(record.timestamp) * 1000).toISOString()}`);
          console.log(`   From: ${record.oldImplementation}`);
          console.log(`   To: ${record.newImplementation}`);
          console.log(`   Emergency: ${record.isEmergency}`);
          console.log(`   Executor: ${record.executor}`);
          console.log(`   Description: ${record.description}`);
        });
      }
    } catch (error) {
      console.log("Could not retrieve upgrade history:", error.message);
    }

    console.log("\n✅ Upgrade execution completed successfully!");

  } catch (error) {
    console.error("\n❌ Upgrade execution failed:");
    console.error(error.message);
    
    if (error.data) {
      try {
        const decodedError = upgradeManager.interface.parseError(error.data);
        console.error("Decoded error:", decodedError);
      } catch {
        console.error("Raw error data:", error.data);
      }
    }
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
