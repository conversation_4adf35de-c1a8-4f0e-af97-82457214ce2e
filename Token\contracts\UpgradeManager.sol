// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
// Remove IERC1967 import as we'll use a simpler approach
import "@openzeppelin/contracts/utils/Address.sol";
import "./interfaces/IModularToken.sol";

/**
 * @title UpgradeManager
 * @dev Manages upgrades for all modules in the modular token system
 * Provides timelock, emergency upgrades, and coordinated multi-module upgrades
 */
contract UpgradeManager is
    Initializable,
    AccessControlUpgradeable,
    UUPSUpgradeable,
    ReentrancyGuardUpgradeable
{
    using Address for address;

    // ============================================================================
    // CONSTANTS & ROLES
    // ============================================================================
    
    bytes32 public constant UPGRADE_MANAGER_ROLE = keccak256("UPGRADE_MANAGER_ROLE");
    bytes32 public constant EMERGENCY_UPGRADE_ROLE = keccak256("EMERGENCY_UPGRADE_ROLE");
    bytes32 public constant TIMELOCK_ADMIN_ROLE = keccak256("TIMELOCK_ADMIN_ROLE");
    
    uint256 public constant UPGRADE_DELAY = 2 days;
    uint256 public constant EMERGENCY_MODE_DURATION = 7 days;
    uint256 public constant MAX_MODULES_PER_COORDINATED_UPGRADE = 10;

    // ============================================================================
    // STATE VARIABLES
    // ============================================================================
    
    // Timelock state
    struct PendingUpgrade {
        bytes32 moduleId;
        address proxy;
        address newImplementation;
        uint256 executeTime;
        bool executed;
        bool cancelled;
        string description;
    }
    
    mapping(bytes32 => PendingUpgrade) public pendingUpgrades;
    bytes32[] public pendingUpgradeIds;
    
    // Emergency mode state
    bool public emergencyMode;
    uint256 public emergencyModeExpiry;
    address public emergencyModeActivator;
    
    // Module registry
    mapping(bytes32 => address) public moduleProxies;
    mapping(address => bytes32) public proxyToModuleId;
    bytes32[] public registeredModules;
    
    // Upgrade history
    struct UpgradeRecord {
        address oldImplementation;
        address newImplementation;
        uint256 timestamp;
        address executor;
        string version;
        bool isEmergency;
        string description;
    }
    
    mapping(bytes32 => UpgradeRecord[]) public upgradeHistory;
    
    // Version tracking
    mapping(bytes32 => uint256) public currentVersionIndex;
    mapping(bytes32 => string) public currentVersion;

    // ============================================================================
    // EVENTS
    // ============================================================================
    
    event ModuleRegistered(bytes32 indexed moduleId, address indexed proxy);
    event ModuleUnregistered(bytes32 indexed moduleId);
    
    event UpgradeScheduled(
        bytes32 indexed upgradeId,
        bytes32 indexed moduleId,
        address indexed proxy,
        address newImplementation,
        uint256 executeTime,
        string description
    );
    
    event UpgradeExecuted(
        bytes32 indexed upgradeId,
        bytes32 indexed moduleId,
        address indexed proxy,
        address oldImplementation,
        address newImplementation,
        string version
    );
    
    event UpgradeCancelled(bytes32 indexed upgradeId, bytes32 indexed moduleId);
    
    event EmergencyModeActivated(address indexed activator, uint256 expiry);
    event EmergencyModeDeactivated(address indexed deactivator);
    event EmergencyUpgradeExecuted(
        bytes32 indexed moduleId,
        address indexed proxy,
        address oldImplementation,
        address newImplementation
    );
    
    event CoordinatedUpgradeScheduled(bytes32[] upgradeIds, string description);
    event CoordinatedUpgradeExecuted(bytes32[] upgradeIds);

    // ============================================================================
    // MODIFIERS
    // ============================================================================
    
    modifier onlyUpgradeManager() {
        require(hasRole(UPGRADE_MANAGER_ROLE, msg.sender), "UpgradeManager: caller is not upgrade manager");
        _;
    }
    
    modifier onlyEmergencyUpgrade() {
        require(hasRole(EMERGENCY_UPGRADE_ROLE, msg.sender), "UpgradeManager: caller is not emergency upgrader");
        _;
    }
    
    modifier onlyTimelockAdmin() {
        require(hasRole(TIMELOCK_ADMIN_ROLE, msg.sender), "UpgradeManager: caller is not timelock admin");
        _;
    }
    
    modifier onlyInEmergencyMode() {
        require(
            emergencyMode && block.timestamp < emergencyModeExpiry,
            "UpgradeManager: emergency mode not active"
        );
        _;
    }

    // ============================================================================
    // INITIALIZATION
    // ============================================================================
    
    /**
     * @dev Initialize the UpgradeManager
     * @param admin The address to be granted admin roles
     */
    function initialize(address admin) public initializer {
        require(admin != address(0), "UpgradeManager: admin cannot be zero address");
        
        __AccessControl_init();
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(UPGRADE_MANAGER_ROLE, admin);
        _grantRole(EMERGENCY_UPGRADE_ROLE, admin);
        _grantRole(TIMELOCK_ADMIN_ROLE, admin);
    }

    // ============================================================================
    // MODULE REGISTRY
    // ============================================================================
    
    /**
     * @dev Register a module proxy for upgrade management
     * @param moduleId The identifier for the module
     * @param proxy The address of the module proxy
     */
    function registerModule(bytes32 moduleId, address proxy) external onlyUpgradeManager {
        require(proxy != address(0), "UpgradeManager: proxy cannot be zero address");
        require(moduleProxies[moduleId] == address(0), "UpgradeManager: module already registered");
        require(proxy.code.length > 0, "UpgradeManager: proxy must be a contract");
        
        moduleProxies[moduleId] = proxy;
        proxyToModuleId[proxy] = moduleId;
        registeredModules.push(moduleId);
        
        emit ModuleRegistered(moduleId, proxy);
    }
    
    /**
     * @dev Unregister a module proxy
     * @param moduleId The identifier for the module
     */
    function unregisterModule(bytes32 moduleId) external onlyUpgradeManager {
        address proxy = moduleProxies[moduleId];
        require(proxy != address(0), "UpgradeManager: module not registered");
        
        delete moduleProxies[moduleId];
        delete proxyToModuleId[proxy];
        
        // Remove from registeredModules array
        for (uint256 i = 0; i < registeredModules.length; i++) {
            if (registeredModules[i] == moduleId) {
                registeredModules[i] = registeredModules[registeredModules.length - 1];
                registeredModules.pop();
                break;
            }
        }
        
        emit ModuleUnregistered(moduleId);
    }

    // ============================================================================
    // TIMELOCK UPGRADES
    // ============================================================================
    
    /**
     * @dev Schedule an upgrade with timelock delay
     * @param moduleId The identifier for the module to upgrade
     * @param newImplementation The address of the new implementation
     * @param description Description of the upgrade
     * @return upgradeId The unique identifier for this upgrade
     */
    function scheduleUpgrade(
        bytes32 moduleId,
        address newImplementation,
        string calldata description
    ) external onlyUpgradeManager returns (bytes32 upgradeId) {
        address proxy = moduleProxies[moduleId];
        require(proxy != address(0), "UpgradeManager: module not registered");
        require(newImplementation != address(0), "UpgradeManager: implementation cannot be zero address");
        require(newImplementation.code.length > 0, "UpgradeManager: implementation must be a contract");
        
        upgradeId = keccak256(abi.encodePacked(moduleId, newImplementation, block.timestamp, msg.sender));
        
        pendingUpgrades[upgradeId] = PendingUpgrade({
            moduleId: moduleId,
            proxy: proxy,
            newImplementation: newImplementation,
            executeTime: block.timestamp + UPGRADE_DELAY,
            executed: false,
            cancelled: false,
            description: description
        });
        
        pendingUpgradeIds.push(upgradeId);
        
        emit UpgradeScheduled(
            upgradeId,
            moduleId,
            proxy,
            newImplementation,
            block.timestamp + UPGRADE_DELAY,
            description
        );
    }
    
    /**
     * @dev Execute a scheduled upgrade after timelock delay
     * @param upgradeId The unique identifier for the upgrade
     */
    function executeUpgrade(bytes32 upgradeId) external onlyUpgradeManager nonReentrant {
        PendingUpgrade storage upgrade = pendingUpgrades[upgradeId];
        require(upgrade.proxy != address(0), "UpgradeManager: upgrade not found");
        require(block.timestamp >= upgrade.executeTime, "UpgradeManager: timelock not expired");
        require(!upgrade.executed, "UpgradeManager: already executed");
        require(!upgrade.cancelled, "UpgradeManager: upgrade cancelled");
        
        upgrade.executed = true;
        
        _performUpgrade(upgrade.moduleId, upgrade.proxy, upgrade.newImplementation, false, upgrade.description);
    }
    
    /**
     * @dev Cancel a pending upgrade
     * @param upgradeId The unique identifier for the upgrade
     */
    function cancelUpgrade(bytes32 upgradeId) external onlyTimelockAdmin {
        PendingUpgrade storage upgrade = pendingUpgrades[upgradeId];
        require(upgrade.proxy != address(0), "UpgradeManager: upgrade not found");
        require(!upgrade.executed, "UpgradeManager: already executed");
        require(!upgrade.cancelled, "UpgradeManager: already cancelled");
        
        upgrade.cancelled = true;
        
        emit UpgradeCancelled(upgradeId, upgrade.moduleId);
    }

    // ============================================================================
    // EMERGENCY UPGRADES
    // ============================================================================
    
    /**
     * @dev Activate emergency mode to allow immediate upgrades
     */
    function activateEmergencyMode() external onlyEmergencyUpgrade {
        emergencyMode = true;
        emergencyModeExpiry = block.timestamp + EMERGENCY_MODE_DURATION;
        emergencyModeActivator = msg.sender;
        
        emit EmergencyModeActivated(msg.sender, emergencyModeExpiry);
    }
    
    /**
     * @dev Deactivate emergency mode
     */
    function deactivateEmergencyMode() external onlyEmergencyUpgrade {
        emergencyMode = false;
        emergencyModeExpiry = 0;
        
        emit EmergencyModeDeactivated(msg.sender);
    }
    
    /**
     * @dev Execute an emergency upgrade without timelock
     * @param moduleId The identifier for the module to upgrade
     * @param newImplementation The address of the new implementation
     * @param description Description of the emergency upgrade
     */
    function emergencyUpgrade(
        bytes32 moduleId,
        address newImplementation,
        string calldata description
    ) external onlyEmergencyUpgrade onlyInEmergencyMode nonReentrant {
        address proxy = moduleProxies[moduleId];
        require(proxy != address(0), "UpgradeManager: module not registered");
        require(newImplementation != address(0), "UpgradeManager: implementation cannot be zero address");
        require(newImplementation.code.length > 0, "UpgradeManager: implementation must be a contract");
        
        _performUpgrade(moduleId, proxy, newImplementation, true, description);
    }

    // ============================================================================
    // INTERNAL FUNCTIONS
    // ============================================================================
    
    /**
     * @dev Internal function to perform the actual upgrade
     */
    function _performUpgrade(
        bytes32 moduleId,
        address proxy,
        address newImplementation,
        bool isEmergency,
        string memory description
    ) internal {
        // Get current implementation - we'll use the last recorded implementation
        // or try to query it from the proxy
        address oldImplementation = address(0);

        // Try to get from upgrade history first
        UpgradeRecord[] storage history = upgradeHistory[moduleId];
        if (history.length > 0) {
            oldImplementation = history[history.length - 1].newImplementation;
        } else {
            // For first upgrade, try to get current implementation
            oldImplementation = _getImplementation(proxy);
        }

        // Perform the upgrade
        (bool success, ) = proxy.call(
            abi.encodeWithSignature("upgradeToAndCall(address,bytes)", newImplementation, "")
        );
        require(success, "UpgradeManager: upgrade failed");

        // Record the upgrade
        upgradeHistory[moduleId].push(UpgradeRecord({
            oldImplementation: oldImplementation,
            newImplementation: newImplementation,
            timestamp: block.timestamp,
            executor: msg.sender,
            version: currentVersion[moduleId],
            isEmergency: isEmergency,
            description: description
        }));

        currentVersionIndex[moduleId]++;

        if (isEmergency) {
            emit EmergencyUpgradeExecuted(moduleId, proxy, oldImplementation, newImplementation);
        } else {
            emit UpgradeExecuted(
                keccak256(abi.encodePacked(moduleId, newImplementation, block.timestamp)),
                moduleId,
                proxy,
                oldImplementation,
                newImplementation,
                currentVersion[moduleId]
            );
        }
    }
    
    /**
     * @dev Get the implementation address of a proxy using low-level call
     * @param proxy The proxy address
     * @return implementation The implementation address
     */
    function _getImplementation(address proxy) internal view returns (address implementation) {
        // Try to call implementation() function (selector: 0x5c60da1b)
        (bool success, bytes memory data) = proxy.staticcall(hex"5c60da1b");

        if (success && data.length >= 32) {
            implementation = abi.decode(data, (address));
        } else {
            // Fallback: assume it's not a proxy or return zero address
            implementation = address(0);
        }
    }

    /**
     * @dev Function to authorize an upgrade
     * @param newImplementation The address of the new implementation
     */
    function _authorizeUpgrade(address newImplementation)
        internal
        override
        onlyRole(DEFAULT_ADMIN_ROLE)
    {}

    // ============================================================================
    // VIEW FUNCTIONS
    // ============================================================================
    
    /**
     * @dev Get all registered modules
     */
    function getRegisteredModules() external view returns (bytes32[] memory) {
        return registeredModules;
    }
    
    /**
     * @dev Get upgrade history for a module
     */
    function getUpgradeHistory(bytes32 moduleId) external view returns (UpgradeRecord[] memory) {
        return upgradeHistory[moduleId];
    }
    
    /**
     * @dev Get all pending upgrade IDs
     */
    function getPendingUpgradeIds() external view returns (bytes32[] memory) {
        return pendingUpgradeIds;
    }
    
    /**
     * @dev Check if emergency mode is active
     */
    function isEmergencyModeActive() external view returns (bool) {
        return emergencyMode && block.timestamp < emergencyModeExpiry;
    }
}
