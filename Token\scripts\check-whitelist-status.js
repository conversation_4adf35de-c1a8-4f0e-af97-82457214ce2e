const { ethers } = require("hardhat");

// Token address to check
const TOKEN_ADDRESS = "******************************************";

// Known addresses that should be whitelisted
const KNOWN_ADDRESSES = [
  "******************************************", // Your main wallet
  "******************************************", // Address that received force transfer
];

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 CHECKING WHITELIST STATUS AFTER SECURITY FIX");
  console.log("Token Address:", TOKEN_ADDRESS);
  console.log("Deployer:", deployer.address);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    console.log("\n📋 CHECKING KNOWN ADDRESSES:");
    
    for (const address of KNOWN_ADDRESSES) {
      console.log(`\nAddress: ${address}`);
      
      try {
        const isWhitelisted = await tokenContract.isWhitelisted(address);
        const isVerified = await tokenContract.isVerified(address);
        const balance = await tokenContract.balanceOf(address);
        const decimals = await tokenContract.decimals();
        
        console.log(`  Whitelisted: ${isWhitelisted}`);
        console.log(`  Verified: ${isVerified}`);
        console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
        
        if (balance > 0 && (!isWhitelisted || !isVerified)) {
          console.log(`  ⚠️ WARNING: Address has tokens but is not properly whitelisted/verified!`);
        }
      } catch (error) {
        console.log(`  ❌ Error checking address: ${error.message}`);
      }
    }

    console.log("\n🔍 CHECKING CONTRACT MODULES:");
    
    // Check if we can add addresses to whitelist
    console.log("Testing addToWhitelist function...");
    
    try {
      // Try to add deployer to whitelist
      await tokenContract.addToWhitelist.staticCall(deployer.address);
      console.log("✅ addToWhitelist function is available");
    } catch (addError) {
      console.log("❌ addToWhitelist failed:", addError.message);
      
      if (addError.message.includes("module not registered")) {
        console.log("💡 Issue: IdentityManager module is not registered");
        console.log("💡 Solution: We need to implement direct whitelist management");
      }
    }

    console.log("\n🔧 PROPOSED SOLUTION:");
    console.log("Since modules aren't registered, we need to:");
    console.log("1. Add direct whitelist storage to the contract");
    console.log("2. Implement direct addToWhitelist/removeFromWhitelist functions");
    console.log("3. Re-whitelist the addresses that should be whitelisted");
    console.log("4. Update the admin panel to work with the new system");

    // Check what addresses currently have tokens
    console.log("\n📊 CHECKING TOKEN HOLDERS:");
    
    // We can't easily enumerate all token holders, but we can check known addresses
    let totalHolders = 0;
    let totalTokensHeld = 0n;
    const decimals = await tokenContract.decimals();
    
    for (const address of KNOWN_ADDRESSES) {
      try {
        const balance = await tokenContract.balanceOf(address);
        if (balance > 0) {
          totalHolders++;
          totalTokensHeld += balance;
          console.log(`  ${address}: ${ethers.formatUnits(balance, decimals)} tokens`);
        }
      } catch (error) {
        console.log(`  Error checking ${address}: ${error.message}`);
      }
    }
    
    console.log(`\nTotal known holders: ${totalHolders}`);
    console.log(`Total tokens held: ${ethers.formatUnits(totalTokensHeld, decimals)}`);

    if (totalHolders > 0) {
      console.log("\n⚠️ CRITICAL: Token holders exist but whitelist is empty!");
      console.log("This means existing token holders cannot transfer their tokens.");
      console.log("We need to restore proper whitelist functionality immediately.");
    }

  } catch (error) {
    console.error("❌ Whitelist check failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
