import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenCoreArtifact from '../../../../../contracts/SecurityTokenCore.json';

// Extract ABI from artifact
const SecurityTokenABI = SecurityTokenCoreArtifact.abi;

// Load private key from environment variable
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/', // Default to Amoy
};

// Network chain IDs
const CHAIN_IDS = {
  amoy: 80002,
  polygon: 137,
  unknown: 80002, // Default to Amoy
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      tokenAddress, 
      fromAddress,
      toAddress,
      amount,
      network = 'amoy' 
    } = body;
    
    if (!tokenAddress || !fromAddress || !toAddress || !amount) {
      return NextResponse.json(
        { error: 'Token address, from address, to address, and amount are required' },
        { status: 400 }
      );
    }
    
    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials.'
        },
        { status: 422 }
      );
    }
    
    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(fromAddress) || !ethers.isAddress(toAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format provided' },
        { status: 400 }
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;
    const rpcUrl = RPC_URLS[actualNetwork as keyof typeof RPC_URLS] || RPC_URLS.amoy;
    const chainId = CHAIN_IDS[actualNetwork as keyof typeof CHAIN_IDS] || CHAIN_IDS.amoy;

    // Connect to the token contract first to get decimals
    const provider = new ethers.JsonRpcProvider(rpcUrl, {
      chainId,
      name: actualNetwork
    });

    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);
    const tokenContract = new ethers.Contract(tokenAddress, SecurityTokenABI, wallet);

    // Get token decimals
    const decimals = await tokenContract.decimals();
    console.log(`Token decimals: ${decimals}`);

    // Validate amount
    let parsedAmount;
    try {
      parsedAmount = ethers.parseUnits(amount.toString(), decimals);
      console.log(`Parsed amount: ${parsedAmount.toString()}`);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid amount format' },
        { status: 400 }
      );
    }
    
    console.log(`Using network: ${actualNetwork}, RPC URL: ${rpcUrl}, Chain ID: ${chainId}`);

    // Ensure the network is connected
    const network_details = await provider.getNetwork();
    console.log(`Connected to network: ${network_details.name} (Chain ID: ${network_details.chainId})`);
    console.log(`Wallet address: ${wallet.address}`);
    
    // Check if the API signer has required role for force transfers
    console.log(`Checking permissions for force transfer...`);

    try {
      const TRANSFER_MANAGER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_MANAGER_ROLE"));
      const DEFAULT_ADMIN_ROLE = await tokenContract.DEFAULT_ADMIN_ROLE();

      const hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, wallet.address);
      const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, wallet.address);

      console.log(`Wallet address: ${wallet.address}`);
      console.log(`Has TRANSFER_MANAGER_ROLE: ${hasTransferManagerRole}`);
      console.log(`Has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);

      if (!hasTransferManagerRole && !hasAdminRole) {
        return NextResponse.json({
          success: false,
          error: "The API wallet doesn't have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE required for force transfers",
          details: `Please grant TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE to ${wallet.address} on the token contract`,
          walletAddress: wallet.address,
          hasTransferManagerRole,
          hasAdminRole
        }, { status: 403 });
      }

      // Check sender balance before force transfer
      const senderBalance = await tokenContract.balanceOf(fromAddress);
      console.log(`Sender balance: ${ethers.formatUnits(senderBalance, decimals)} tokens`);

      if (senderBalance < parsedAmount) {
        return NextResponse.json({
          success: false,
          error: `Insufficient balance in source address`,
          details: `Source address ${fromAddress} has ${ethers.formatUnits(senderBalance, decimals)} tokens, but trying to transfer ${amount}`,
          senderBalance: ethers.formatUnits(senderBalance, decimals),
          requestedAmount: amount
        }, { status: 400 });
      }

      // Execute the actual force transfer
      console.log(`Executing force transfer of ${amount} tokens from ${fromAddress} to ${toAddress}...`);

      const tx = await tokenContract.forcedTransfer(fromAddress, toAddress, parsedAmount);
      console.log(`Force transfer transaction hash: ${tx.hash}`);

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      // Get updated balances
      const newFromBalance = await tokenContract.balanceOf(fromAddress);
      const newToBalance = await tokenContract.balanceOf(toAddress);

      return NextResponse.json({
        success: true,
        action: 'forcedTransfer',
        txHash: tx.hash,
        blockNumber: receipt.blockNumber,
        from: fromAddress,
        to: toAddress,
        amount: amount,
        newFromBalance: ethers.formatUnits(newFromBalance, decimals),
        newToBalance: ethers.formatUnits(newToBalance, decimals)
      });

    } catch (txError: any) {
      console.error('Force transfer transaction error:', txError);

      let errorMessage = 'Force transfer failed';
      if (txError.message.includes('insufficient funds')) {
        errorMessage = 'Insufficient gas funds in admin wallet';
      } else if (txError.message.includes('execution reverted')) {
        errorMessage = 'Transaction reverted - check permissions and balances';
      } else if (txError.reason) {
        errorMessage = `Contract error: ${txError.reason}`;
      } else if (txError.message) {
        errorMessage = txError.message;
      }

      return NextResponse.json({
        success: false,
        error: errorMessage,
        details: txError.message
      }, { status: 500 });
    }
    
  } catch (error: any) {
    console.error('Error forcing token transfer:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
} 