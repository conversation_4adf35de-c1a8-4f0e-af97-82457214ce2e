import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function isWhitelisted(address account) external view returns (bool)",
  "function isVerified(address account) external view returns (bool)",
  "function balanceOf(address account) external view returns (uint256)",
  "function decimals() external view returns (uint8)",
  "event AddressWhitelisted(address indexed account)",
  "event AddressRemovedFromWhitelist(address indexed account)"
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Missing tokenAddress parameter' },
        { status: 400 }
      );
    }

    // Validate address
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;

    if (!rpcUrl) {
      return NextResponse.json(
        { error: 'Server configuration error: Missing RPC URL' },
        { status: 500 }
      );
    }

    // Setup provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);

    console.log('Fetching whitelist for token:', tokenAddress);

    // Method 1: Get addresses from events
    let whitelistedAddresses = new Set<string>();
    
    try {
      // Get AddressWhitelisted events
      const whitelistFilter = tokenContract.filters.AddressWhitelisted();
      const whitelistEvents = await tokenContract.queryFilter(whitelistFilter, -10000); // Last 10k blocks
      
      console.log(`Found ${whitelistEvents.length} AddressWhitelisted events`);
      
      for (const event of whitelistEvents) {
        if (event.args && event.args.account) {
          whitelistedAddresses.add(event.args.account.toLowerCase());
        }
      }

      // Get AddressRemovedFromWhitelist events and remove those addresses
      const removeFilter = tokenContract.filters.AddressRemovedFromWhitelist();
      const removeEvents = await tokenContract.queryFilter(removeFilter, -10000);
      
      console.log(`Found ${removeEvents.length} AddressRemovedFromWhitelist events`);
      
      for (const event of removeEvents) {
        if (event.args && event.args.account) {
          whitelistedAddresses.delete(event.args.account.toLowerCase());
        }
      }

    } catch (eventError) {
      console.log('Error fetching events, using fallback method:', eventError.message);
    }

    // Method 2: Fallback - check known addresses that might be whitelisted
    const knownAddresses = [
      '******************************************', // Main wallet
      '******************************************', // Force transfer recipient
      '******************************************'  // Test address
    ];

    // If no events found, check known addresses
    if (whitelistedAddresses.size === 0) {
      console.log('No events found, checking known addresses...');
      
      for (const address of knownAddresses) {
        try {
          const isWhitelisted = await tokenContract.isWhitelisted(address);
          if (isWhitelisted) {
            whitelistedAddresses.add(address.toLowerCase());
            console.log(`Found whitelisted address: ${address}`);
          }
        } catch (error) {
          console.log(`Error checking ${address}:`, error.message);
        }
      }
    }

    // Convert Set to Array and get details for each address
    const addressList = Array.from(whitelistedAddresses);
    console.log(`Processing ${addressList.length} whitelisted addresses`);

    const decimals = await tokenContract.decimals();
    
    const addressDetails = await Promise.all(
      addressList.map(async (address) => {
        try {
          const [isWhitelisted, isVerified, balance] = await Promise.all([
            tokenContract.isWhitelisted(address),
            tokenContract.isVerified(address),
            tokenContract.balanceOf(address)
          ]);

          // Double-check that address is still whitelisted
          if (!isWhitelisted) {
            console.log(`Address ${address} no longer whitelisted, skipping`);
            return null;
          }

          const balanceFormatted = decimals === 0 ? balance.toString() : ethers.formatUnits(balance, decimals);

          return {
            address: address,
            balance: balanceFormatted,
            isWhitelisted,
            isVerified,
            frozenTokens: '0' // Default, can be enhanced later
          };
        } catch (error) {
          console.error(`Error processing address ${address}:`, error.message);
          return null;
        }
      })
    );

    // Filter out null results
    const validAddresses = addressDetails.filter(addr => addr !== null);

    console.log(`Returning ${validAddresses.length} valid whitelisted addresses`);

    return NextResponse.json({
      success: true,
      tokenAddress,
      whitelistedAddresses: validAddresses,
      totalAddresses: validAddresses.length,
      method: whitelistedAddresses.size > 0 ? 'events' : 'fallback'
    });

  } catch (error: any) {
    console.error('Whitelist API error:', error);
    
    let errorMessage = 'Failed to fetch whitelist';
    if (error.message.includes('network')) {
      errorMessage = 'Network connection error';
    } else if (error.message.includes('contract')) {
      errorMessage = 'Contract interaction error';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json(
      { 
        error: errorMessage,
        details: error.message 
      },
      { status: 500 }
    );
  }
}
