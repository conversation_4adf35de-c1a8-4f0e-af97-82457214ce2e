const { ethers } = require("hardhat");

// Token address to analyze
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 Deep analysis of contract functions:", TOKEN_ADDRESS);

  try {
    // Get the contract bytecode
    const contractCode = await deployer.provider.getCode(TOKEN_ADDRESS);
    console.log("Contract bytecode length:", contractCode.length);

    // Try to find any function that might update metadata
    console.log("\n📋 Searching for potential update functions...");
    
    // Common function selectors for update operations
    const potentialSelectors = {
      // Standard ERC20 admin functions
      "mint": "0x40c10f19",
      "burn": "0x42966c68", 
      "pause": "0x8456cb59",
      "unpause": "0x3f4ba83a",
      
      // Metadata update variations
      "setTokenPrice": "0x",
      "setMetadata": "0x",
      "updateMetadata": "0x062f601b",
      "updateTokenMetadata": "0x062f601b",
      "updatePrice": "0x",
      "setPrice": "0x",
      
      // Role-based functions
      "grantRole": "0x2f2ff15d",
      "revokeRole": "0xd547741f",
      "renounceRole": "0x36568abe",
      
      // Proxy functions
      "upgradeTo": "0x3659cfe6",
      "upgradeToAndCall": "0x4f1ef286",
      
      // Common admin functions
      "transferOwnership": "0xf2fde38b",
      "setAdmin": "0x",
      "updateAdmin": "0x"
    };

    console.log("Checking for known function selectors in bytecode:");
    for (const [funcName, selector] of Object.entries(potentialSelectors)) {
      if (selector && selector !== "0x") {
        const exists = contractCode.includes(selector.slice(2));
        if (exists) {
          console.log(`✅ ${funcName}: ${selector} FOUND`);
        }
      }
    }

    // Try different ABI combinations to see what works
    console.log("\n📋 Testing different function signatures...");

    // Test 1: Try admin functions that might exist
    const adminABI = [
      "function grantRole(bytes32 role, address account) external",
      "function hasRole(bytes32 role, address account) view returns (bool)",
      "function mint(address to, uint256 amount) external",
      "function pause() external",
      "function unpause() external"
    ];

    const adminContract = new ethers.Contract(TOKEN_ADDRESS, adminABI, deployer);
    
    try {
      // Test mint function
      await adminContract.mint.staticCall(deployer.address, 1);
      console.log("✅ mint() function exists and works");
    } catch (mintError) {
      console.log("❌ mint() function failed:", mintError.message);
    }

    try {
      // Test pause function
      await adminContract.pause.staticCall();
      console.log("✅ pause() function exists");
    } catch (pauseError) {
      console.log("❌ pause() function failed:", pauseError.message);
    }

    // Test 2: Check if this might be a different contract type entirely
    console.log("\n📋 Checking contract type and inheritance...");
    
    // Try to call supportsInterface if it exists
    const interfaceABI = [
      "function supportsInterface(bytes4 interfaceId) view returns (bool)"
    ];
    
    const interfaceContract = new ethers.Contract(TOKEN_ADDRESS, interfaceABI, deployer);
    
    try {
      // ERC165 interface
      const supportsERC165 = await interfaceContract.supportsInterface("0x01ffc9a7");
      console.log("✅ Supports ERC165:", supportsERC165);
      
      // AccessControl interface
      const supportsAccessControl = await interfaceContract.supportsInterface("0x7965db0b");
      console.log("✅ Supports AccessControl:", supportsAccessControl);
      
    } catch (interfaceError) {
      console.log("❌ supportsInterface not available:", interfaceError.message);
    }

    // Test 3: Try to find the actual implementation if this is a proxy
    console.log("\n📋 Checking proxy implementation...");
    
    try {
      const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
      const implementationAddress = await deployer.provider.getStorage(TOKEN_ADDRESS, implementationSlot);
      const cleanImplementationAddress = "0x" + implementationAddress.slice(-40);
      
      console.log("Implementation address:", cleanImplementationAddress);
      
      // Get implementation bytecode
      const implCode = await deployer.provider.getCode(cleanImplementationAddress);
      console.log("Implementation bytecode length:", implCode.length);
      
      // Check if implementation has the update functions
      const hasUpdateTokenMetadata = implCode.includes("062f601b");
      const hasUpdateTokenPrice = implCode.includes("fba187d9");
      
      console.log("Implementation has updateTokenMetadata:", hasUpdateTokenMetadata);
      console.log("Implementation has updateTokenPrice:", hasUpdateTokenPrice);
      
    } catch (proxyError) {
      console.log("❌ Error checking proxy implementation:", proxyError.message);
    }

    // Test 4: Try to call any function that might modify state
    console.log("\n📋 Testing state modification functions...");
    
    // Check if we can find any working update function
    const testABIs = [
      // Try different metadata function signatures
      "function setTokenMetadata(string, string, string) external",
      "function updateToken(string, string, string) external", 
      "function modifyMetadata(string, string, string) external",
      "function changeTokenPrice(string) external",
      "function setTokenPrice(string) external",
      
      // Try owner-based functions
      "function setMetadata(string, string, string) external",
      "function updateTokenInfo(string, string, string) external"
    ];

    for (const abi of testABIs) {
      try {
        const testContract = new ethers.Contract(TOKEN_ADDRESS, [abi], deployer);
        const funcName = abi.split('function ')[1].split('(')[0];
        
        if (funcName.includes('setTokenPrice') || funcName.includes('changeTokenPrice')) {
          await testContract[funcName].staticCall("Test Price");
        } else {
          await testContract[funcName].staticCall("Test Price", "Test Tiers", "Test Details");
        }
        
        console.log(`✅ FOUND WORKING FUNCTION: ${funcName}`);
        break;
      } catch (testError) {
        // Silent fail - just testing
      }
    }

  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
