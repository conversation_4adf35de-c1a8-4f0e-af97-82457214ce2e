import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function updateMaxSupply(uint256 newMaxSupply) external",
  "function maxSupply() external view returns (uint256)",
  "function totalSupply() external view returns (uint256)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, newMaxSupply } = await request.json();

    if (!tokenAddress || !newMaxSupply) {
      return NextResponse.json(
        { error: 'Token address and new max supply are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Validate max supply
    const maxSupplyNum = parseInt(newMaxSupply);
    if (isNaN(maxSupplyNum) || maxSupplyNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid max supply value' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Get current values for comparison
    const currentMaxSupply = await tokenContract.maxSupply();
    const currentTotalSupply = await tokenContract.totalSupply();
    
    console.log('Current max supply:', currentMaxSupply.toString());
    console.log('Current total supply:', currentTotalSupply.toString());
    console.log('New max supply:', newMaxSupply);

    // Convert to proper units (assuming 0 decimals based on our token)
    const newMaxSupplyBN = ethers.parseUnits(newMaxSupply, 0);

    // Update max supply
    const tx = await tokenContract.updateMaxSupply(newMaxSupplyBN);
    await tx.wait();

    // Verify update
    const updatedMaxSupply = await tokenContract.maxSupply();

    console.log('Max supply updated successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'Max supply updated successfully',
      txHash: tx.hash,
      oldMaxSupply: currentMaxSupply.toString(),
      newMaxSupply: updatedMaxSupply.toString(),
      currentTotalSupply: currentTotalSupply.toString(),
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error updating max supply:', error);
    return NextResponse.json(
      { error: `Failed to update max supply: ${error.message}` },
      { status: 500 }
    );
  }
}
