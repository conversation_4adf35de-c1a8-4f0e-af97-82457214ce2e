const { ethers } = require("hardhat");

// Token address to check
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 Checking admin roles for token:", TOKEN_ADDRESS);
  console.log("Current deployer:", deployer.address);

  try {
    // Get the SecurityTokenCore contract
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const tokenCore = SecurityTokenCore.attach(TOKEN_ADDRESS);

    // Get role constants
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    const TRANSFER_MANAGER_ROLE = await tokenCore.TRANSFER_MANAGER_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();

    console.log("\n🔑 Role Constants:");
    console.log(`DEFAULT_ADMIN_ROLE: ${DEFAULT_ADMIN_ROLE}`);
    console.log(`AGENT_ROLE: ${AGENT_ROLE}`);
    console.log(`TRANSFER_MANAGER_ROLE: ${TRANSFER_MANAGER_ROLE}`);
    console.log(`MODULE_MANAGER_ROLE: ${MODULE_MANAGER_ROLE}`);

    // Check current deployer's roles
    console.log(`\n👤 Current deployer (${deployer.address}) roles:`);
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    const hasTransferManager = await tokenCore.hasRole(TRANSFER_MANAGER_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    
    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);
    console.log(`✅ TRANSFER_MANAGER_ROLE: ${hasTransferManager}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);

    // Check common addresses
    const commonAddresses = [
      "******************************************", // Client wallet
      "******************************************", // Another common address
    ];

    console.log("\n🔍 Checking common addresses for admin roles:");
    for (const address of commonAddresses) {
      try {
        const hasAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, address);
        const hasAgent = await tokenCore.hasRole(AGENT_ROLE, address);
        console.log(`${address}: Admin=${hasAdmin}, Agent=${hasAgent}`);
      } catch (error) {
        console.log(`${address}: Error checking roles - ${error.message}`);
      }
    }

    // Try to find admin by checking recent events
    console.log("\n🔍 Checking for RoleGranted events...");
    
    try {
      const filter = tokenCore.filters.RoleGranted(DEFAULT_ADMIN_ROLE);
      const events = await tokenCore.queryFilter(filter, -10000); // Last 10k blocks
      
      if (events.length > 0) {
        console.log("✅ Found RoleGranted events for DEFAULT_ADMIN_ROLE:");
        events.forEach((event, index) => {
          console.log(`  ${index + 1}. Admin: ${event.args.account} (Block: ${event.blockNumber})`);
        });
      } else {
        console.log("⚠️ No RoleGranted events found in recent blocks");
      }
    } catch (eventError) {
      console.log("⚠️ Could not query events:", eventError.message);
    }

    // Get token info
    console.log("\n📋 Token Information:");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const totalSupply = await tokenCore.totalSupply();
    const maxSupply = await tokenCore.maxSupply();
    
    console.log(`Name: ${name}`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Total Supply: ${totalSupply.toString()}`);
    console.log(`Max Supply: ${maxSupply.toString()}`);

    // Get metadata
    const metadata = await tokenCore.getTokenMetadata();
    console.log("\n💰 Token Metadata:");
    console.log(`Price: "${metadata[0]}"`);
    console.log(`Bonus Tiers: "${metadata[1]}"`);
    console.log(`Details: "${metadata[2]}"`);
    console.log(`Image URL: "${metadata[3]}"`);

  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
