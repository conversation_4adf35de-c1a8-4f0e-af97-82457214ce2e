const { ethers } = require("hardhat");

async function main() {
  console.log("🔑 Granting DEPLOYER_ROLE to user address...\n");

  // Get the deployer account (admin)
  const [admin] = await ethers.getSigners();
  console.log("Admin account:", admin.address);

  // User address that needs deployer role
  const USER_ADDRESS = "******************************************";
  const FACTORY_ADDRESS = "******************************************";

  console.log("User address:", USER_ADDRESS);
  console.log("Factory address:", FACTORY_ADDRESS);

  try {
    // Get the factory contract
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(FACTORY_ADDRESS);

    // Get role constants
    const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();

    console.log("\n📋 Role Information:");
    console.log("DEFAULT_ADMIN_ROLE:", DEFAULT_ADMIN_ROLE);
    console.log("DEPLOYER_ROLE:", DEPLOYER_ROLE);

    // Check current roles
    const adminHasAdminRole = await factory.hasRole(DEFAULT_ADMIN_ROLE, admin.address);
    const userHasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, USER_ADDRESS);

    console.log("\n🔍 Current Role Status:");
    console.log(`Admin (${admin.address}) has admin role:`, adminHasAdminRole);
    console.log(`User (${USER_ADDRESS}) has deployer role:`, userHasDeployerRole);

    if (!adminHasAdminRole) {
      console.log("❌ Admin does not have admin role. Cannot grant deployer role.");
      return;
    }

    if (userHasDeployerRole) {
      console.log("✅ User already has deployer role!");
      return;
    }

    // Grant deployer role to user
    console.log("\n🎯 Granting DEPLOYER_ROLE to user...");
    const tx = await factory.grantRole(DEPLOYER_ROLE, USER_ADDRESS);
    console.log("Transaction sent:", tx.hash);

    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed!");

    // Verify the role was granted
    const userHasRoleAfter = await factory.hasRole(DEPLOYER_ROLE, USER_ADDRESS);
    console.log("✅ User now has deployer role:", userHasRoleAfter);

    console.log("\n🎉 DEPLOYER_ROLE successfully granted!");
    console.log(`User ${USER_ADDRESS} can now deploy modular tokens.`);

  } catch (error) {
    console.error("❌ Error granting deployer role:", error);
    
    // Try to decode the error
    if (error.data) {
      console.log("Error data:", error.data);
      
      // Common error signatures
      const errorSignatures = {
        "0xd6bda275": "AccessControlUnauthorizedAccount(address,bytes32)",
        "0x50c83a81": "AccessControlBadConfirmation()",
        "0xab143c06": "ReentrancyGuardReentrantCall()"
      };
      
      const errorSig = error.data.slice(0, 10);
      if (errorSignatures[errorSig]) {
        console.log("Decoded error:", errorSignatures[errorSig]);
      }
    }
    
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
