const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function main() {
  console.log('🔑 Granting DEPLOYER_ROLE to user address...\n');

  // Configuration
  const FACTORY_ADDRESS = process.env.NEXT_PUBLIC_AMOY_MODULAR_TOKEN_FACTORY_ADDRESS;
  const USER_ADDRESS = "******************************************";
  const ADMIN_PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
  const RPC_URL = process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology/";

  if (!FACTORY_ADDRESS) {
    console.error('❌ FACTORY_ADDRESS not found in environment variables');
    return;
  }

  if (!ADMIN_PRIVATE_KEY) {
    console.error('❌ CONTRACT_ADMIN_PRIVATE_KEY not found in environment variables');
    return;
  }

  console.log('Factory address:', FACTORY_ADDRESS);
  console.log('User address:', USER_ADDRESS);
  console.log('RPC URL:', RPC_URL);

  try {
    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const adminWallet = new ethers.Wallet(ADMIN_PRIVATE_KEY, provider);
    
    console.log('Admin wallet address:', adminWallet.address);
    
    // Load the ModularTokenFactory ABI
    const abiPath = path.join(__dirname, 'src/contracts/ModularTokenFactory.json');
    const factoryABI = JSON.parse(fs.readFileSync(abiPath, 'utf8'));

    // Create factory contract instance
    const factory = new ethers.Contract(FACTORY_ADDRESS, factoryABI, adminWallet);

    // Get role constants
    const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();

    console.log('\n📋 Role Information:');
    console.log('DEFAULT_ADMIN_ROLE:', DEFAULT_ADMIN_ROLE);
    console.log('DEPLOYER_ROLE:', DEPLOYER_ROLE);

    // Check current roles
    const adminHasAdminRole = await factory.hasRole(DEFAULT_ADMIN_ROLE, adminWallet.address);
    const userHasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, USER_ADDRESS);

    console.log('\n🔍 Current Role Status:');
    console.log('Admin has admin role:', adminHasAdminRole);
    console.log('User has deployer role:', userHasDeployerRole);

    if (!adminHasAdminRole) {
      console.error('❌ Admin wallet does not have DEFAULT_ADMIN_ROLE. Cannot grant roles.');
      return;
    }

    if (userHasDeployerRole) {
      console.log('✅ User already has DEPLOYER_ROLE. No action needed.');
      return;
    }

    // Grant deployer role to user
    console.log('\n🎯 Granting DEPLOYER_ROLE to user...');
    
    // Estimate gas first
    try {
      const gasEstimate = await factory.grantRole.estimateGas(DEPLOYER_ROLE, USER_ADDRESS);
      console.log('Gas estimate:', gasEstimate.toString());
    } catch (gasError) {
      console.warn('Gas estimation failed:', gasError.message);
    }

    const tx = await factory.grantRole(DEPLOYER_ROLE, USER_ADDRESS, {
      gasLimit: 200000, // Fixed gas limit
      maxFeePerGas: ethers.parseUnits('120', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
    });
    
    console.log('Transaction sent:', tx.hash);

    const receipt = await tx.wait();
    console.log('✅ Transaction confirmed in block:', receipt.blockNumber);

    // Verify the role was granted
    const userHasRoleAfter = await factory.hasRole(DEPLOYER_ROLE, USER_ADDRESS);
    console.log('✅ User now has deployer role:', userHasRoleAfter);

    if (userHasRoleAfter) {
      console.log('\n🎉 DEPLOYER_ROLE successfully granted!');
      console.log(`User ${USER_ADDRESS} can now deploy modular tokens.`);
    } else {
      console.log('❌ Role grant verification failed');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.data) {
      console.log('Error data:', error.data);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
