// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title ISecurityTokenModule
 * @dev Base interface for all security token modules
 */
interface ISecurityTokenModule {
    /**
     * @dev Returns the unique identifier for this module
     * @return bytes32 The module identifier
     */
    function moduleId() external pure returns (bytes32);
    
    /**
     * @dev Returns the version of this module
     * @return string The module version
     */
    function version() external pure returns (string memory);
}
