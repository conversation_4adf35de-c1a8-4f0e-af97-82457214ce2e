const { ethers, upgrades } = require("hardhat");

// Token proxy address to upgrade
const PROXY_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚀 Upgrading token contract:", PROXY_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Get the current implementation
    console.log("\n📋 Current contract state:");
    const currentImplementation = await upgrades.erc1967.getImplementationAddress(PROXY_ADDRESS);
    console.log("Current implementation:", currentImplementation);

    // Test current functionality
    const currentContract = await ethers.getContractAt("SecurityTokenCore", PROXY_ADDRESS);
    
    try {
      const metadata = await currentContract.getTokenMetadata();
      console.log("✅ Current metadata accessible");
      console.log(`   Price: "${metadata[0]}"`);
      console.log(`   Tiers: "${metadata[1]}"`);
      console.log(`   Details: "${metadata[2]}"`);
    } catch (metadataError) {
      console.log("❌ Current metadata not accessible:", metadataError.message);
    }

    // Check if user can upgrade
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    const hasAdminRole = await currentContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    if (!hasAdminRole) {
      console.log("❌ Cannot upgrade - deployer doesn't have DEFAULT_ADMIN_ROLE");
      return;
    }
    
    console.log("✅ Deployer has admin role - can proceed with upgrade");

    // Force import the existing proxy first
    console.log("\n🔧 Force importing existing proxy...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");

    try {
      await upgrades.forceImport(PROXY_ADDRESS, SecurityTokenCore);
      console.log("✅ Proxy imported successfully");
    } catch (importError) {
      console.log("⚠️ Import warning:", importError.message);
      // Continue anyway - might already be imported
    }

    // Deploy new implementation
    console.log("\n🔧 Deploying new SecurityTokenCore implementation...");

    // Upgrade the proxy to the new implementation
    console.log("📦 Upgrading proxy to new implementation...");
    const upgradedContract = await upgrades.upgradeProxy(PROXY_ADDRESS, SecurityTokenCore);
    await upgradedContract.waitForDeployment();

    console.log("✅ Upgrade completed!");

    // Get new implementation address
    const newImplementation = await upgrades.erc1967.getImplementationAddress(PROXY_ADDRESS);
    console.log("New implementation:", newImplementation);

    // Test new functionality
    console.log("\n🧪 Testing new functionality...");
    
    try {
      // Test if updateTokenPrice now exists
      await upgradedContract.updateTokenPrice.staticCall("Test Price");
      console.log("✅ updateTokenPrice function now available!");
    } catch (updateError) {
      console.log("❌ updateTokenPrice still not available:", updateError.message);
    }

    try {
      // Test if updateTokenMetadata now exists
      await upgradedContract.updateTokenMetadata.staticCall("Test Price", "Test Tiers", "Test Details");
      console.log("✅ updateTokenMetadata function now available!");
    } catch (metadataUpdateError) {
      console.log("❌ updateTokenMetadata still not available:", metadataUpdateError.message);
    }

    // Verify metadata is still intact
    try {
      const newMetadata = await upgradedContract.getTokenMetadata();
      console.log("✅ Metadata preserved after upgrade:");
      console.log(`   Price: "${newMetadata[0]}"`);
      console.log(`   Tiers: "${newMetadata[1]}"`);
      console.log(`   Details: "${newMetadata[2]}"`);
    } catch (newMetadataError) {
      console.log("❌ Error reading metadata after upgrade:", newMetadataError.message);
    }

    // Test a real update
    console.log("\n🎯 Testing real price update...");
    try {
      const tx = await upgradedContract.updateTokenPrice("7 USD - UPGRADED!");
      await tx.wait();
      console.log("✅ Price update successful! Transaction:", tx.hash);
      
      // Verify the update
      const updatedMetadata = await upgradedContract.getTokenMetadata();
      console.log(`✅ New price: "${updatedMetadata[0]}"`);
      
    } catch (realUpdateError) {
      console.log("❌ Real price update failed:", realUpdateError.message);
    }

    console.log("\n🎉 Upgrade process completed!");
    console.log(`🔗 View on Polygonscan: https://amoy.polygonscan.com/address/${PROXY_ADDRESS}`);

  } catch (error) {
    console.error("❌ Upgrade failed:", error.message);
    
    if (error.message.includes('Ownable: caller is not the owner')) {
      console.log("💡 Tip: Make sure the deployer wallet has admin role on the contract");
    }
    
    if (error.message.includes('ERC1967Upgrade: upgrade breaks storage layout')) {
      console.log("💡 Tip: The new implementation might have storage layout conflicts");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
