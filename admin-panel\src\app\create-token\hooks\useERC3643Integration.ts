import { ethers } from 'ethers';

// Contract ABIs for ERC-3643 integration
const IdentityRegistryABI = [
  "function registerIdentity(address investor, uint16 country) external",
  "function addToWhitelist(address investor) external",
  "function approveKyc(address investor) external",
  "function isVerified(address investor) external view returns (bool)",
  "function isWhitelisted(address investor) external view returns (bool)",
  "function isKycApproved(address investor) external view returns (bool)"
];

const ClaimRegistryABI = [
  "function issueClaim(address subject, uint256 topic, bytes calldata signature, bytes calldata data, string calldata uri, uint256 validUntil) external",
  "function hasValidClaim(address subject, uint256 topic) external view returns (bool)"
];

const ComplianceABI = [
  "function created(address to, uint256 value) external",
  "function canTransfer(address from, address to, uint256 value) external view returns (bool)"
];

// Tokeny Claim Topics (following Tokeny standard)
const CLAIM_TOPICS = {
  KYC: 1,
  AML: 2,
  IDENTITY: 3,
  QUALIFICATION: 4,
  ACCREDITATION: 5,
  RESIDENCE: 6,
  TOKEN_ISSUER: 7 // Custom claim for token issuers
};

// Tokeny-style claim data format
// Format: YYYYMMDDHHMMSS (timestamp) + country code + additional data
// Example: 10101010000648 = timestamp + country + verification level
function generateTokenyClaim(country: string, claimType: string): string {
  const now = new Date();
  const timestamp = now.getFullYear().toString().slice(-2) + // YY
                   (now.getMonth() + 1).toString().padStart(2, '0') + // MM
                   now.getDate().toString().padStart(2, '0') + // DD
                   now.getHours().toString().padStart(2, '0') + // HH
                   now.getMinutes().toString().padStart(2, '0') + // MM
                   now.getSeconds().toString().padStart(2, '0'); // SS

  const countryCode = getCountryCode(country).toString().padStart(3, '0');

  // Additional data based on claim type
  let additionalData = '';
  switch (claimType) {
    case 'KYC':
      additionalData = '001'; // KYC level 1
      break;
    case 'QUALIFICATION':
      additionalData = '002'; // Qualified investor
      break;
    case 'TOKEN_ISSUER':
      additionalData = '003'; // Token issuer
      break;
    default:
      additionalData = '000';
  }

  return timestamp + countryCode + additionalData;
}

// Country code mapping (ISO-3166 numeric)
const COUNTRY_CODES: { [key: string]: number } = {
  'US': 840, 'USA': 840, 'United States': 840,
  'CA': 124, 'Canada': 124,
  'GB': 826, 'UK': 826, 'United Kingdom': 826,
  'DE': 276, 'Germany': 276,
  'FR': 250, 'France': 250,
  'IT': 380, 'Italy': 380,
  'ES': 724, 'Spain': 724,
  'NL': 528, 'Netherlands': 528,
  'CH': 756, 'Switzerland': 756,
  'AU': 36, 'Australia': 36,
  'JP': 392, 'Japan': 392,
  'SG': 702, 'Singapore': 702,
};

function getCountryCode(country: string): number {
  return COUNTRY_CODES[country] || COUNTRY_CODES[country.toUpperCase()] || 840; // Default to USA
}

// Global cache to prevent duplicate operations
const operationCache = new Map<string, Promise<any>>();

/**
 * Hook for ERC-3643 integration during token deployment
 */
export function useERC3643Integration() {

  /**
   * Setup ERC-3643 compliance for a newly deployed token
   */
  const setupERC3643Compliance = async (
    tokenAddress: string,
    ownerAddress: string,
    signer: ethers.Signer,
    tokenData: {
      name: string;
      symbol: string;
      tokenType: string;
      country?: string;
      selectedClaims?: string[];
    }
  ) => {
    // Create a unique operation key to prevent duplicates
    const operationKey = `${tokenAddress}-${ownerAddress}-${JSON.stringify(tokenData.selectedClaims)}`;

    // Check if this operation is already in progress
    if (operationCache.has(operationKey)) {
      console.log("🔄 ERC-3643 compliance setup already in progress, returning cached promise");
      return operationCache.get(operationKey)!;
    }

    console.log("🏛️ Setting up ERC-3643 compliance for token:", tokenAddress);

    const results = {
      identityRegistered: false,
      whitelisted: false,
      kycApproved: false,
      claimsIssued: [],
      complianceNotified: false,
      errors: []
    };

    // Create the operation promise
    const operationPromise = (async () => {

      try {
        // Get contract addresses from environment
        const identityRegistryAddress = process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS;
        const claimRegistryAddress = process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS;
        const complianceAddress = process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS;

        if (!identityRegistryAddress || !claimRegistryAddress || !complianceAddress) {
          console.warn("⚠️ ERC-3643 contract addresses not configured, skipping compliance setup");
          return results;
        }

      // Connect to contracts
      const identityRegistry = new ethers.Contract(identityRegistryAddress, IdentityRegistryABI, signer);
      const claimRegistry = new ethers.Contract(claimRegistryAddress, ClaimRegistryABI, signer);
      const compliance = new ethers.Contract(complianceAddress, ComplianceABI, signer);

        // Pre-check: Get current status to avoid unnecessary transactions
        console.log("🔍 Checking current compliance status...");
        const [isVerified, isWhitelisted, isKycApproved] = await Promise.all([
          identityRegistry.isVerified(ownerAddress).catch(() => false),
          identityRegistry.isWhitelisted(ownerAddress).catch(() => false),
          identityRegistry.isKycApproved(ownerAddress).catch(() => false)
        ]);

        console.log("📊 Current status:", {
          isVerified,
          isWhitelisted,
          isKycApproved
        });

      // Step 1: Register identity if not already registered
      try {
        if (!isVerified) {
          console.log("📝 Registering identity for token owner...");
          const countryCode = getCountryCode(tokenData.country || 'US');
          const tx1 = await identityRegistry.registerIdentity(ownerAddress, countryCode);
          await tx1.wait();
          results.identityRegistered = true;
          console.log("✅ Identity registered successfully");
        } else {
          console.log("✅ Identity already registered");
          results.identityRegistered = true;
        }
      } catch (error) {
        console.error("❌ Failed to register identity:", error);
        results.errors.push(`Identity registration failed: ${error.message}`);
      }

      // Step 2: Add to whitelist if not already whitelisted
      try {
        if (!isWhitelisted) {
          console.log("📋 Adding to whitelist...");
          const tx2 = await identityRegistry.addToWhitelist(ownerAddress);
          await tx2.wait();
          results.whitelisted = true;
          console.log("✅ Added to whitelist successfully");
        } else {
          console.log("✅ Already whitelisted");
          results.whitelisted = true;
        }
      } catch (error) {
        console.error("❌ Failed to add to whitelist:", error);
        results.errors.push(`Whitelist addition failed: ${error.message}`);
      }

      // Step 3: Approve KYC if not already approved
      try {
        if (!isKycApproved) {
          console.log("🔍 Approving KYC...");
          const tx3 = await identityRegistry.approveKyc(ownerAddress);
          await tx3.wait();
          results.kycApproved = true;
          console.log("✅ KYC approved successfully");
        } else {
          console.log("✅ KYC already approved");
          results.kycApproved = true;
        }
      } catch (error) {
        console.error("❌ Failed to approve KYC:", error);
        results.errors.push(`KYC approval failed: ${error.message}`);
      }

      // Step 4: Issue selected Tokeny-style claims for token issuer
      try {
        const selectedClaims = tokenData.selectedClaims || ['KYC', 'QUALIFICATION', 'TOKEN_ISSUER'];
        console.log("📜 Issuing selected Tokeny-style claims:", selectedClaims);

        // Pre-check existing claims to avoid duplicates
        const existingClaims = new Map<string, boolean>();
        for (const claimType of selectedClaims) {
          const claimTopic = CLAIM_TOPICS[claimType as keyof typeof CLAIM_TOPICS];
          if (claimTopic) {
            try {
              const hasExistingClaim = await claimRegistry.hasValidClaim(ownerAddress, claimTopic);
              existingClaims.set(claimType, hasExistingClaim);
            } catch (error) {
              existingClaims.set(claimType, false);
            }
          }
        }

        console.log("📊 Existing claims status:", Object.fromEntries(existingClaims));

        // Issue claims based on user selection
        for (const claimType of selectedClaims) {
          try {
            const claimValue = generateTokenyClaim(tokenData.country || 'US', claimType);
            const claimTopic = CLAIM_TOPICS[claimType as keyof typeof CLAIM_TOPICS];

            if (!claimTopic) {
              console.warn(`⚠️ Unknown claim type: ${claimType}`);
              continue;
            }

            console.log(`🔢 Generated ${claimType} claim: ${claimValue}`);

            // Check if claim already exists (using pre-checked values)
            const hasExistingClaim = existingClaims.get(claimType) || false;
            if (hasExistingClaim) {
              console.log(`✅ ${claimType} claim already exists`);
              continue;
            }

            // Prepare claim data based on type
            let claimData: string;
            let claimUri: string;

            switch (claimType) {
              case 'KYC':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "KYC_APPROVED", Math.floor(Date.now() / 1000)]
                );
                claimUri = `KYC:${claimValue}`;
                break;

              case 'AML':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "AML_VERIFIED", Math.floor(Date.now() / 1000)]
                );
                claimUri = `AML:${claimValue}`;
                break;

              case 'IDENTITY':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "IDENTITY_VERIFIED", Math.floor(Date.now() / 1000)]
                );
                claimUri = `IDENTITY:${claimValue}`;
                break;

              case 'QUALIFICATION':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "QUALIFIED_INVESTOR", Math.floor(Date.now() / 1000)]
                );
                claimUri = `QUALIFICATION:${claimValue}`;
                break;

              case 'ACCREDITATION':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "ACCREDITED_INVESTOR", Math.floor(Date.now() / 1000)]
                );
                claimUri = `ACCREDITATION:${claimValue}`;
                break;

              case 'RESIDENCE':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "uint256"],
                  [claimValue, "RESIDENCE_VERIFIED", Math.floor(Date.now() / 1000)]
                );
                claimUri = `RESIDENCE:${claimValue}`;
                break;

              case 'TOKEN_ISSUER':
                claimData = ethers.AbiCoder.defaultAbiCoder().encode(
                  ["string", "string", "string", "string", "uint256"],
                  [claimValue, tokenData.name, tokenData.symbol, tokenData.tokenType, Math.floor(Date.now() / 1000)]
                );
                claimUri = `TOKEN_ISSUER:${claimValue}:${tokenData.symbol}`;
                break;

              default:
                console.warn(`⚠️ Unsupported claim type: ${claimType}`);
                continue;
            }

            // Issue the claim
            const tx = await claimRegistry.issueClaim(
              ownerAddress,
              claimTopic,
              "0x", // empty signature
              claimData,
              claimUri,
              0 // never expires
            );
            await tx.wait();

            results.claimsIssued.push(`${claimType}:${claimValue}`);
            console.log(`✅ ${claimType} claim issued: ${claimValue}`);

          } catch (claimError) {
            console.error(`❌ Failed to issue ${claimType} claim:`, claimError);
            results.errors.push(`${claimType} claim issuance failed: ${claimError.message}`);
          }
        }

        console.log(`🎉 Claims issuance completed! Issued ${results.claimsIssued.length} claims`);

      } catch (error) {
        console.error("❌ Failed to issue claims:", error);
        results.errors.push(`Claims issuance failed: ${error.message}`);
      }

      // Step 5: Notify compliance contract (if needed)
      try {
        console.log("⚖️ Notifying compliance contract...");
        // This would typically be called when tokens are minted, but we can prepare it
        const canTransfer = await compliance.canTransfer(ownerAddress, ownerAddress, 1);
        console.log("✅ Compliance check passed:", canTransfer);
        results.complianceNotified = true;
      } catch (error) {
        console.error("❌ Compliance notification failed:", error);
        results.errors.push(`Compliance notification failed: ${error.message}`);
      }

      console.log("🎉 ERC-3643 compliance setup completed!");
      console.log("Results:", {
        identityRegistered: results.identityRegistered,
        whitelisted: results.whitelisted,
        kycApproved: results.kycApproved,
        claimsIssued: results.claimsIssued,
        complianceNotified: results.complianceNotified,
        errorCount: results.errors.length
      });

    } catch (error) {
      console.error("❌ ERC-3643 setup failed:", error);
      results.errors.push(`General setup failed: ${error.message}`);
    } finally {
      // Clean up cache entry
      operationCache.delete(operationKey);
    }

      return results;
    })();

    // Cache the operation promise
    operationCache.set(operationKey, operationPromise);

    return operationPromise;
  };

  /**
   * Check if ERC-3643 contracts are available
   */
  const isERC3643Available = () => {
    return !!(
      process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS &&
      process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS &&
      process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS
    );
  };

  /**
   * Get ERC-3643 contract addresses
   */
  const getERC3643Addresses = () => {
    return {
      identityRegistry: process.env.NEXT_PUBLIC_IDENTITY_REGISTRY_ADDRESS,
      claimRegistry: process.env.NEXT_PUBLIC_CLAIM_REGISTRY_ADDRESS,
      compliance: process.env.NEXT_PUBLIC_COMPLIANCE_ADDRESS
    };
  };

  return {
    setupERC3643Compliance,
    isERC3643Available,
    getERC3643Addresses
  };
}
