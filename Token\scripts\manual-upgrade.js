const { ethers } = require("hardhat");

// Token proxy address to upgrade
const PROXY_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔧 Manual upgrade approach for:", PROXY_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Deploy a fresh SecurityTokenCore implementation
    console.log("\n📦 Deploying fresh SecurityTokenCore implementation...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    
    const newImplAddress = await newImplementation.getAddress();
    console.log("New implementation deployed at:", newImplAddress);

    // Check if the new implementation has the functions we need
    console.log("\n🔍 Checking new implementation functions...");
    try {
      // These should work on the implementation directly (but will fail due to not being initialized)
      const fragment1 = newImplementation.interface.getFunction("updateTokenPrice");
      const fragment2 = newImplementation.interface.getFunction("updateTokenMetadata");
      console.log("✅ updateTokenPrice selector:", fragment1.selector);
      console.log("✅ updateTokenMetadata selector:", fragment2.selector);
    } catch (funcError) {
      console.log("❌ Functions not found in new implementation:", funcError.message);
      return;
    }

    // Get the proxy contract and check if we can upgrade it
    console.log("\n🔧 Attempting manual upgrade...");
    const proxyContract = await ethers.getContractAt("SecurityTokenCore", PROXY_ADDRESS);
    
    // Check if the proxy has upgradeToAndCall function
    try {
      // Try to upgrade using upgradeToAndCall
      const upgradeData = "0x"; // No initialization data needed
      const tx = await proxyContract.upgradeToAndCall(newImplAddress, upgradeData);
      await tx.wait();
      
      console.log("✅ Manual upgrade successful! Transaction:", tx.hash);
      
      // Test the new functions
      console.log("\n🧪 Testing upgraded contract...");
      
      try {
        await proxyContract.updateTokenPrice.staticCall("Test Price");
        console.log("✅ updateTokenPrice now works!");
      } catch (testError) {
        console.log("❌ updateTokenPrice still fails:", testError.message);
      }
      
      try {
        await proxyContract.updateTokenMetadata.staticCall("Test Price", "Test Tiers", "Test Details");
        console.log("✅ updateTokenMetadata now works!");
      } catch (testError2) {
        console.log("❌ updateTokenMetadata still fails:", testError2.message);
      }

      // Try a real update
      console.log("\n🎯 Attempting real price update...");
      try {
        const updateTx = await proxyContract.updateTokenPrice("8 USD - MANUALLY UPGRADED!");
        await updateTx.wait();
        console.log("✅ Price update successful! Transaction:", updateTx.hash);
        
        // Verify the update
        const updatedMetadata = await proxyContract.getTokenMetadata();
        console.log(`✅ New price: "${updatedMetadata[0]}"`);
        
      } catch (realUpdateError) {
        console.log("❌ Real price update failed:", realUpdateError.message);
      }
      
    } catch (upgradeError) {
      console.log("❌ Manual upgrade failed:", upgradeError.message);
      
      if (upgradeError.message.includes('AccessControl')) {
        console.log("💡 Tip: Check if deployer has UPGRADER_ROLE or DEFAULT_ADMIN_ROLE");
      }
    }

    // Alternative: Check what roles are needed for upgrade
    console.log("\n🔍 Checking upgrade permissions...");
    try {
      const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
      const hasAdmin = await proxyContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      console.log("Has DEFAULT_ADMIN_ROLE:", hasAdmin);
      
      // Check if there's an UPGRADER_ROLE
      try {
        const UPGRADER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("UPGRADER_ROLE"));
        const hasUpgrader = await proxyContract.hasRole(UPGRADER_ROLE, deployer.address);
        console.log("Has UPGRADER_ROLE:", hasUpgrader);
      } catch (upgraderError) {
        console.log("UPGRADER_ROLE not defined in contract");
      }
      
    } catch (roleError) {
      console.log("❌ Error checking roles:", roleError.message);
    }

  } catch (error) {
    console.error("❌ Manual upgrade process failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
