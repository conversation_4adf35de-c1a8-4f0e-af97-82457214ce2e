'use client';

import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';

// Import ABIs - extract the abi property from the JSON artifacts
import SecurityTokenCoreArtifact from '@/contracts/SecurityTokenCore.json';
import UpgradeManagerArtifact from '@/contracts/UpgradeManager.json';

// Extract ABIs from artifacts
const SecurityTokenCoreABI = SecurityTokenCoreArtifact.abi;
const UpgradeManagerABI = UpgradeManagerArtifact.abi;

// Contract addresses from environment
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

interface TokenInfo {
  name: string;
  symbol: string;
  version: string;
  totalSupply: string;
  maxSupply: string;
  decimals: number;
  paused: boolean;
  metadata: {
    tokenPrice: string;
    bonusTiers: string;
    tokenDetails: string;
    tokenImageUrl: string;
  };
}

interface UpgradeInfo {
  emergencyModeActive: boolean;
  registeredModules: string[];
  upgradeDelay: number;
  emergencyModeDuration: number;
}

interface PendingUpgrade {
  upgradeId: string;
  moduleId: string;
  proxy: string;
  newImplementation: string;
  executeTime: number;
  executed: boolean;
  cancelled: boolean;
  description: string;
}

interface UpgradeRecord {
  oldImplementation: string;
  newImplementation: string;
  timestamp: number;
  executor: string;
  version: string;
  isEmergency: boolean;
  description: string;
}

export function useModularToken(tokenAddress?: string) {
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [contractAddresses, setContractAddresses] = useState<{[key: string]: string} | null>(null);
  const [userRoles, setUserRoles] = useState<{[key: string]: boolean} | null>(null);
  const [upgradeInfo, setUpgradeInfo] = useState<UpgradeInfo | null>(null);
  const [pendingUpgrades, setPendingUpgrades] = useState<PendingUpgrade[]>([]);
  const [upgradeHistory, setUpgradeHistory] = useState<UpgradeRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize provider and signer
  const initializeProvider = useCallback(async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send('eth_requestAccounts', []);
        const signer = await provider.getSigner();
        
        setProvider(provider);
        setSigner(signer);
        return { provider, signer };
      } else {
        throw new Error('MetaMask not found');
      }
    } catch (error) {
      console.error('Error initializing provider:', error);
      setError('Failed to connect to wallet');
      return null;
    }
  }, []);

  // Load token information
  const loadTokenInfo = useCallback(async () => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!provider || !contractAddress) return;

    try {
      setLoading(true);
      const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, provider);
      
      const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([
        contract.name(),
        contract.symbol(),
        contract.version(),
        contract.totalSupply(),
        contract.maxSupply(),
        contract.decimals(),
        contract.paused(),
        contract.getTokenMetadata()
      ]);

      console.log('Token contract data:');
      console.log('- Name:', name);
      console.log('- Symbol:', symbol);
      console.log('- Decimals (raw):', decimals);
      console.log('- Decimals (number):', Number(decimals));
      console.log('- Total Supply (raw):', totalSupply.toString());
      console.log('- Max Supply (raw):', maxSupply.toString());
      console.log('- Metadata (raw):', metadata);
      console.log('- Token Price:', metadata[0]);
      console.log('- Bonus Tiers:', metadata[1]);
      console.log('- Token Details:', metadata[2]);
      console.log('- Token Image URL:', metadata[3]);

      const decimalsNumber = Number(decimals);

      // Format with proper blockchain decimal precision
      let formattedTotalSupply: string;
      let formattedMaxSupply: string;

      if (decimalsNumber === 0) {
        // For 0 decimals, show as whole numbers
        formattedTotalSupply = totalSupply.toString();
        formattedMaxSupply = maxSupply.toString();
      } else {
        // For tokens with decimals, use ethers.formatUnits and preserve decimal precision
        const totalSupplyFormatted = ethers.formatUnits(totalSupply, decimalsNumber);
        const maxSupplyFormatted = ethers.formatUnits(maxSupply, decimalsNumber);

        // For blockchain tokens, show with appropriate decimal places
        // Remove excessive trailing zeros but keep meaningful precision
        formattedTotalSupply = parseFloat(totalSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));
        formattedMaxSupply = parseFloat(maxSupplyFormatted).toFixed(Math.min(decimalsNumber, 6));

        // Remove trailing zeros after decimal point
        formattedTotalSupply = formattedTotalSupply.replace(/\.?0+$/, '');
        formattedMaxSupply = formattedMaxSupply.replace(/\.?0+$/, '');

        // Ensure at least .0 for tokens with decimals
        if (!formattedTotalSupply.includes('.')) formattedTotalSupply += '.0';
        if (!formattedMaxSupply.includes('.')) formattedMaxSupply += '.0';
      }

      console.log('- Total Supply (formatted):', formattedTotalSupply);
      console.log('- Max Supply (formatted):', formattedMaxSupply);

      setTokenInfo({
        name,
        symbol,
        version,
        totalSupply: formattedTotalSupply,
        maxSupply: formattedMaxSupply,
        decimals: decimalsNumber,
        paused,
        metadata: {
          tokenPrice: metadata[0],
          bonusTiers: metadata[1],
          tokenDetails: metadata[2],
          tokenImageUrl: metadata[3]
        }
      });
    } catch (error) {
      console.error('Error loading token info:', error);
      setError('Failed to load token information');
    } finally {
      setLoading(false);
    }
  }, [provider, tokenAddress]);

  // Load contract addresses
  const loadContractAddresses = useCallback(async () => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!provider || !contractAddress) return;

    try {
      const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, provider);

      // Define module IDs (these are keccak256 hashes of the module names)
      const moduleIds = {
        'Identity Manager': ethers.keccak256(ethers.toUtf8Bytes("IDENTITY_MANAGER")),
        'Compliance Engine': ethers.keccak256(ethers.toUtf8Bytes("COMPLIANCE_ENGINE")),
        'Transfer Controller': ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_CONTROLLER")),
        'Agent Manager': ethers.keccak256(ethers.toUtf8Bytes("AGENT_MANAGER")),
        'Emergency Manager': ethers.keccak256(ethers.toUtf8Bytes("EMERGENCY_MANAGER")),
        'KYC Claims Module': ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"))
      };

      console.log('Fetching module addresses...');

      // Fetch all module addresses
      const addresses: {[key: string]: string} = {
        'Token Contract': contractAddress
      };

      for (const [moduleName, moduleId] of Object.entries(moduleIds)) {
        try {
          const moduleAddress = await contract.getModule(moduleId);
          if (moduleAddress && moduleAddress !== ethers.ZeroAddress) {
            addresses[moduleName] = moduleAddress;
            console.log(`${moduleName}: ${moduleAddress}`);
          } else {
            console.log(`${moduleName}: Not registered`);
          }
        } catch (error) {
          console.warn(`Error fetching ${moduleName} address:`, error);
        }
      }

      setContractAddresses(addresses);
    } catch (error) {
      console.error('Error loading contract addresses:', error);
    }
  }, [signer, tokenAddress]);

  // Force transfer tokens
  const forceTransfer = useCallback(async (fromAddress: string, toAddress: string, amount: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      // Check if user has required role
      const TRANSFER_MANAGER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_MANAGER_ROLE"));
      const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
      const userAddress = await signer.getAddress();

      const hasTransferManagerRole = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
      const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      if (!hasTransferManagerRole && !hasAdminRole) {
        throw new Error(`Wallet ${userAddress} does not have TRANSFER_MANAGER_ROLE or DEFAULT_ADMIN_ROLE required for force transfers`);
      }

      console.log('Force transferring tokens:', { fromAddress, toAddress, amount });
      console.log('User address:', userAddress);
      console.log('Has TRANSFER_MANAGER_ROLE:', hasTransferManagerRole);
      console.log('Has DEFAULT_ADMIN_ROLE:', hasAdminRole);

      // Parse amount based on token decimals
      const decimals = await contract.decimals();
      const parsedAmount = ethers.parseUnits(amount, decimals);

      console.log('Parsed amount:', parsedAmount.toString());

      // Estimate gas first
      let gasEstimate;
      try {
        gasEstimate = await contract.forcedTransfer.estimateGas(fromAddress, toAddress, parsedAmount);
        console.log('Gas estimate:', gasEstimate.toString());
      } catch (gasError) {
        console.error('Gas estimation failed:', gasError);
        throw new Error(`Gas estimation failed: ${gasError.message}. This usually means the transaction would revert.`);
      }

      // Execute the force transfer
      const gasLimit = gasEstimate + (gasEstimate / 10n); // Add 10% buffer
      const tx = await contract.forcedTransfer(fromAddress, toAddress, parsedAmount, {
        gasLimit: gasLimit,
        gasPrice: ethers.parseUnits('30', 'gwei')
      });

      console.log('Force transfer transaction sent:', tx.hash);
      return tx.wait();
    } catch (error: any) {
      console.error('Force transfer error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else if (error.message.includes('Gas estimation failed')) {
        throw error; // Re-throw gas estimation errors as-is
      } else {
        throw new Error(`Failed to force transfer: ${error.message}`);
      }
    }
  }, [provider, tokenAddress]);

  // Load user roles
  const loadUserRoles = useCallback(async () => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!provider || !signer || !contractAddress) return;

    try {
      const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, provider);
      const userAddress = await signer.getAddress();

      // Define role hashes
      const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
      const AGENT_ROLE = ethers.keccak256(ethers.toUtf8Bytes("AGENT_ROLE"));
      const TRANSFER_MANAGER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_MANAGER_ROLE"));
      const MODULE_MANAGER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("MODULE_MANAGER_ROLE"));

      console.log('Checking roles for user:', userAddress);

      // Check all roles
      const roles: {[key: string]: boolean} = {};

      try {
        roles['DEFAULT_ADMIN_ROLE'] = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);
        console.log('DEFAULT_ADMIN_ROLE:', roles['DEFAULT_ADMIN_ROLE']);
      } catch (error) {
        console.warn('Error checking DEFAULT_ADMIN_ROLE:', error);
        roles['DEFAULT_ADMIN_ROLE'] = false;
      }

      try {
        roles['AGENT_ROLE'] = await contract.hasRole(AGENT_ROLE, userAddress);
        console.log('AGENT_ROLE:', roles['AGENT_ROLE']);
      } catch (error) {
        console.warn('Error checking AGENT_ROLE:', error);
        roles['AGENT_ROLE'] = false;
      }

      try {
        roles['TRANSFER_MANAGER_ROLE'] = await contract.hasRole(TRANSFER_MANAGER_ROLE, userAddress);
        console.log('TRANSFER_MANAGER_ROLE:', roles['TRANSFER_MANAGER_ROLE']);
      } catch (error) {
        console.warn('Error checking TRANSFER_MANAGER_ROLE:', error);
        roles['TRANSFER_MANAGER_ROLE'] = false;
      }

      try {
        roles['MODULE_MANAGER_ROLE'] = await contract.hasRole(MODULE_MANAGER_ROLE, userAddress);
        console.log('MODULE_MANAGER_ROLE:', roles['MODULE_MANAGER_ROLE']);
      } catch (error) {
        console.warn('Error checking MODULE_MANAGER_ROLE:', error);
        roles['MODULE_MANAGER_ROLE'] = false;
      }

      setUserRoles(roles);
    } catch (error) {
      console.error('Error loading user roles:', error);
    }
  }, [provider, signer, tokenAddress]);

  // Load upgrade information
  const loadUpgradeInfo = useCallback(async () => {
    if (!provider || !UPGRADE_MANAGER_ADDRESS) return;

    try {
      setLoading(true);
      const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, provider);
      
      const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([
        contract.isEmergencyModeActive(),
        contract.getRegisteredModules(),
        contract.UPGRADE_DELAY(),
        contract.EMERGENCY_MODE_DURATION(),
        contract.getPendingUpgradeIds()
      ]);

      setUpgradeInfo({
        emergencyModeActive,
        registeredModules,
        upgradeDelay: Number(upgradeDelay),
        emergencyModeDuration: Number(emergencyModeDuration)
      });

      // Load pending upgrades
      const pendingUpgradesData = await Promise.all(
        pendingUpgradeIds.map(async (id: string) => {
          const upgrade = await contract.pendingUpgrades(id);
          return {
            upgradeId: id,
            moduleId: upgrade.moduleId,
            proxy: upgrade.proxy,
            newImplementation: upgrade.newImplementation,
            executeTime: Number(upgrade.executeTime),
            executed: upgrade.executed,
            cancelled: upgrade.cancelled,
            description: upgrade.description
          };
        })
      );

      setPendingUpgrades(pendingUpgradesData);

      // Load upgrade history for SecurityTokenCore
      const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
      const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);
      
      setUpgradeHistory(history.map((record: any) => ({
        oldImplementation: record.oldImplementation,
        newImplementation: record.newImplementation,
        timestamp: Number(record.timestamp),
        executor: record.executor,
        version: record.version,
        isEmergency: record.isEmergency,
        description: record.description
      })));

    } catch (error) {
      console.error('Error loading upgrade info:', error);
      setError('Failed to load upgrade information');
    } finally {
      setLoading(false);
    }
  }, [provider]);

  // Mint tokens
  const mintTokens = useCallback(async (address: string, amount: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      const decimals = tokenInfo?.decimals || 0;
      const amountWei = ethers.parseUnits(amount, decimals);

      const tx = await contract.mint(address, amountWei, { gasLimit: 400000 });
      return tx.wait();
    } catch (error: any) {
      console.error('Mint tokens error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else {
        throw new Error(`Failed to mint tokens: ${error.message}`);
      }
    }
  }, [signer, tokenInfo, tokenAddress]);

  // Toggle pause state with multiple retry strategies
  const togglePause = useCallback(async () => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    const isPausing = !tokenInfo?.paused;
    const functionName = isPausing ? 'pause' : 'unpause';

    // Try multiple strategies to handle Amoy testnet issues
    const strategies = [
      // Strategy 1: Standard call with high gas
      async () => {
        const tx = isPausing
          ? await contract.pause({ gasLimit: 500000, gasPrice: ethers.parseUnits('30', 'gwei') })
          : await contract.unpause({ gasLimit: 500000, gasPrice: ethers.parseUnits('30', 'gwei') });
        return tx.wait();
      },
      // Strategy 2: Raw transaction approach (like the working script)
      async () => {
        const functionSelector = isPausing ? '0x8456cb59' : '0x3f4ba83a';
        const nonce = await signer.getNonce();

        const tx = await signer.sendTransaction({
          to: contractAddress,
          data: functionSelector,
          gasLimit: 500000,
          gasPrice: ethers.parseUnits('30', 'gwei'),
          nonce: nonce
        });
        return tx.wait();
      }
    ];

    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`Attempting ${functionName} strategy ${i + 1}...`);
        const result = await strategies[i]();
        console.log(`${functionName} successful with strategy ${i + 1}`);
        return result;
      } catch (error: any) {
        console.error(`Strategy ${i + 1} failed:`, error);

        if (error.code === 'ACTION_REJECTED') {
          throw new Error('Transaction was rejected by user');
        }

        // If this is the last strategy, throw a comprehensive error
        if (i === strategies.length - 1) {
          if (error.code === 'UNKNOWN_ERROR' && error.error?.code === -32603) {
            throw new Error(`Amoy testnet RPC error: The network is experiencing issues. Please try again in a few minutes, or use a different RPC endpoint.`);
          } else if (error.reason) {
            throw new Error(`Contract error: ${error.reason}`);
          } else {
            throw new Error(`Failed to ${functionName} token after trying multiple methods: ${error.message}`);
          }
        }

        // Wait a bit before trying the next strategy
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }, [signer, tokenInfo, tokenAddress]);

  // Schedule upgrade
  const scheduleUpgrade = useCallback(async (implementationAddress: string, description: string) => {
    if (!signer || !UPGRADE_MANAGER_ADDRESS) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);
    const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
    
    const tx = await contract.scheduleUpgrade(
      SECURITY_TOKEN_CORE_ID,
      implementationAddress,
      description
    );
    
    return tx.wait();
  }, [signer]);

  // Execute upgrade
  const executeUpgrade = useCallback(async (upgradeId: string) => {
    if (!signer || !UPGRADE_MANAGER_ADDRESS) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);
    const tx = await contract.executeUpgrade(upgradeId);
    return tx.wait();
  }, [signer]);

  // Emergency upgrade
  const emergencyUpgrade = useCallback(async (implementationAddress: string, description: string) => {
    if (!signer || !UPGRADE_MANAGER_ADDRESS) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);
    const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
    
    const tx = await contract.emergencyUpgrade(
      SECURITY_TOKEN_CORE_ID,
      implementationAddress,
      description
    );
    
    return tx.wait();
  }, [signer]);

  // Toggle emergency mode
  const toggleEmergencyMode = useCallback(async () => {
    if (!signer || !UPGRADE_MANAGER_ADDRESS) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);
    
    const tx = upgradeInfo?.emergencyModeActive 
      ? await contract.deactivateEmergencyMode()
      : await contract.activateEmergencyMode();
    
    return tx.wait();
  }, [signer, upgradeInfo]);

  // Cancel upgrade
  const cancelUpgrade = useCallback(async (upgradeId: string) => {
    if (!signer || !UPGRADE_MANAGER_ADDRESS) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS, UpgradeManagerABI, signer);
    const tx = await contract.cancelUpgrade(upgradeId);
    return tx.wait();
  }, [signer]);

  // Update token price (now using direct updateTokenPrice function)
  const updateTokenPrice = useCallback(async (newPrice: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      // First check if user has admin role
      const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
      const userAddress = await signer.getAddress();
      const hasAdminRole = await contract.hasRole(DEFAULT_ADMIN_ROLE, userAddress);

      if (!hasAdminRole) {
        throw new Error(`Wallet ${userAddress} does not have DEFAULT_ADMIN_ROLE on this token contract`);
      }

      console.log('Updating token price from wallet:', userAddress);
      console.log('New price:', newPrice);
      console.log('Contract address:', contractAddress);

      // Try direct updateTokenPrice first (for upgraded contracts)
      let gasEstimate;
      try {
        gasEstimate = await contract.updateTokenPrice.estimateGas(newPrice);
        console.log('Gas estimate (direct):', gasEstimate.toString());

        // Execute with estimated gas + buffer
        const gasLimit = gasEstimate + (gasEstimate / 10n); // Add 10% buffer
        const tx = await contract.updateTokenPrice(newPrice, {
          gasLimit: gasLimit,
          gasPrice: ethers.parseUnits('30', 'gwei')
        });

        console.log('Transaction sent (direct):', tx.hash);
        return tx.wait();

      } catch (directError) {
        console.log('Direct updateTokenPrice failed, trying updateTokenMetadata fallback:', directError.message);

        // Fallback to updateTokenMetadata for older contracts
        const currentMetadata = await contract.getTokenMetadata();
        const currentBonusTiers = currentMetadata[1];
        const currentDetails = currentMetadata[2];

        gasEstimate = await contract.updateTokenMetadata.estimateGas(newPrice, currentBonusTiers, currentDetails);
        console.log('Gas estimate (fallback):', gasEstimate.toString());

        const gasLimit = gasEstimate + (gasEstimate / 10n);
        const tx = await contract.updateTokenMetadata(newPrice, currentBonusTiers, currentDetails, {
          gasLimit: gasLimit,
          gasPrice: ethers.parseUnits('30', 'gwei')
        });

        console.log('Transaction sent (fallback):', tx.hash);
        return tx.wait();
      }
    } catch (error: any) {
      console.error('Update token price error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else if (error.message.includes('Gas estimation failed')) {
        throw error; // Re-throw gas estimation errors as-is
      } else {
        throw new Error(`Failed to update token price: ${error.message}`);
      }
    }
  }, [signer, tokenAddress]);

  // Update bonus tiers (now using direct updateBonusTiers function)
  const updateBonusTiers = useCallback(async (newBonusTiers: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      // Try direct updateBonusTiers first (for upgraded contracts)
      try {
        const tx = await contract.updateBonusTiers(newBonusTiers, { gasLimit: 300000 });
        console.log('Bonus tiers updated (direct):', tx.hash);
        return tx.wait();
      } catch (directError) {
        console.log('Direct updateBonusTiers failed, trying updateTokenMetadata fallback:', directError.message);

        // Fallback to updateTokenMetadata for older contracts
        const currentMetadata = await contract.getTokenMetadata();
        const currentPrice = currentMetadata[0];
        const currentDetails = currentMetadata[2];

        const tx = await contract.updateTokenMetadata(currentPrice, newBonusTiers, currentDetails, { gasLimit: 300000 });
        console.log('Bonus tiers updated (fallback):', tx.hash);
        return tx.wait();
      }
    } catch (error: any) {
      console.error('Update bonus tiers error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else {
        throw new Error(`Failed to update bonus tiers: ${error.message}`);
      }
    }
  }, [signer, tokenAddress]);

  // Update max supply
  const updateMaxSupply = useCallback(async (newMaxSupply: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      const maxSupplyWei = ethers.parseUnits(newMaxSupply, tokenInfo?.decimals || 0);
      const tx = await contract.updateMaxSupply(maxSupplyWei, { gasLimit: 300000 });
      return tx.wait();
    } catch (error: any) {
      console.error('Update max supply error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else {
        throw new Error(`Failed to update max supply: ${error.message}`);
      }
    }
  }, [signer, tokenInfo, tokenAddress]);

  // Add to whitelist with identity registration
  const addToWhitelist = useCallback(async (address: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      // First check if the address is already verified/registered
      const isVerified = await contract.isVerified(address);

      if (!isVerified) {
        // Need to register identity first - use the API approach for this
        throw new Error('Address must be registered first. Please use the API method or register the identity manually.');
      }

      // Now try to add to whitelist
      const tx = await contract.addToWhitelist(address, {
        gasLimit: 400000,
        gasPrice: ethers.parseUnits('30', 'gwei')
      });
      return tx.wait();
    } catch (error: any) {
      console.error('Add to whitelist error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else if (error.message.includes('identity not registered')) {
        throw new Error('Address must be registered first. Please use the API method which handles registration automatically.');
      } else {
        throw new Error(`Failed to add address to whitelist: ${error.message}`);
      }
    }
  }, [signer, tokenAddress]);

  // Remove from whitelist
  const removeFromWhitelist = useCallback(async (address: string) => {
    const contractAddress = tokenAddress || SECURITY_TOKEN_CORE_ADDRESS;
    if (!signer || !contractAddress) {
      throw new Error('Wallet not connected');
    }

    const contract = new ethers.Contract(contractAddress, SecurityTokenCoreABI, signer);

    try {
      const tx = await contract.removeFromWhitelist(address, { gasLimit: 300000 });
      return tx.wait();
    } catch (error: any) {
      console.error('Remove from whitelist error:', error);
      if (error.code === 'ACTION_REJECTED') {
        throw new Error('Transaction was rejected by user');
      } else if (error.reason) {
        throw new Error(`Contract error: ${error.reason}`);
      } else {
        throw new Error(`Failed to remove address from whitelist: ${error.message}`);
      }
    }
  }, [signer, tokenAddress]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    await Promise.all([
      loadTokenInfo(),
      loadContractAddresses(),
      loadUserRoles(),
      loadUpgradeInfo()
    ]);
  }, [loadTokenInfo, loadContractAddresses, loadUserRoles, loadUpgradeInfo]);

  // Initialize on mount
  useEffect(() => {
    initializeProvider();
  }, [initializeProvider]);

  // Load data when provider is ready
  useEffect(() => {
    if (provider && signer) {
      refreshData();
    }
  }, [provider, signer, refreshData]);

  return {
    // State
    provider,
    signer,
    tokenInfo,
    contractAddresses,
    userRoles,
    upgradeInfo,
    pendingUpgrades,
    upgradeHistory,
    loading,
    error,

    // Actions
    initializeProvider,
    loadTokenInfo,
    loadContractAddresses,
    loadUserRoles,
    loadUpgradeInfo,
    mintTokens,
    togglePause,
    scheduleUpgrade,
    executeUpgrade,
    emergencyUpgrade,
    toggleEmergencyMode,
    cancelUpgrade,
    refreshData,

    // Admin Functions
    updateTokenPrice,
    updateBonusTiers,
    updateMaxSupply,
    addToWhitelist,
    removeFromWhitelist,
    forceTransfer,

    // Utilities
    setError,
    setLoading
  };
}
