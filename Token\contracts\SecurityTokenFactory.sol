// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import "./SecurityToken.sol";
import "./Whitelist.sol";
import "./WhitelistWithKYC.sol";
import "./Compliance.sol";
import "./interfaces/ISecurityToken.sol";
import "./interfaces/ICompleteWhitelist.sol";

/**
 * @title SecurityTokenFactory
 * @dev Factory contract for deploying ERC-3643 compliant SecurityToken and Whitelist contracts
 */
contract SecurityTokenFactory is AccessControl, ReentrancyGuard {
    bytes32 public constant DEPLOYER_ROLE = keccak256("DEPLOYER_ROLE");

    // Implementation addresses
    address public securityTokenImplementation;
    address public whitelistImplementation;
    address public whitelistWithKYCImplementation;
    address public complianceImplementation;

    // Mapping from token symbol to token address
    mapping(string => address) private _tokenAddresses;

    // Array to store all deployed token addresses for enumeration
    address[] public deployedTokens;

    // Events
    event TokenDeployed(
        address indexed tokenAddress,
        address indexed identityRegistryAddress,
        address indexed complianceAddress,
        string name,
        string symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        bool hasKYC,
        string tokenImageUrl
    );

    event ImplementationsUpdated(
        address indexed oldTokenImplementation,
        address indexed newTokenImplementation,
        address oldWhitelistImplementation,
        address newWhitelistImplementation,
        address oldWhitelistWithKYCImplementation,
        address newWhitelistWithKYCImplementation,
        address oldComplianceImplementation,
        address newComplianceImplementation
    );

    /**
     * @dev Constructor sets up the factory with initial implementations
     * @param admin Address that will have admin rights to the factory
     */
    constructor(address admin) {
        require(admin != address(0), "SecurityTokenFactory: admin cannot be zero address");

        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(DEPLOYER_ROLE, admin);

        // Deploy implementation contracts
        securityTokenImplementation = address(new SecurityToken());
        whitelistImplementation = address(new Whitelist());
        whitelistWithKYCImplementation = address(new WhitelistWithKYC());
        complianceImplementation = address(new Compliance());
    }

    /**
     * @dev Deploy a new SecurityToken and Whitelist/Identity Registry without KYC
     * @param name The name of the token
     * @param symbol The symbol of the token
     * @param decimals The number of decimals for the token (0-18)
     * @param maxSupply The maximum supply of the token
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     * @param tokenPrice Optional token price metadata
     * @param bonusTiers Optional bonus tiers metadata
     * @param tokenDetails Optional token details metadata
     * @param tokenImageUrl Optional token image/logo URL
     * @return tokenAddress The address of the deployed token
     * @return identityRegistryAddress The address of the deployed identity registry
     * @return complianceAddress The address of the deployed compliance contract
     */
    function deploySecurityToken(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl
    ) external onlyRole(DEPLOYER_ROLE) returns (address tokenAddress, address identityRegistryAddress, address complianceAddress) {
        return deploySecurityTokenWithOptions(name, symbol, decimals, maxSupply, admin, tokenPrice, bonusTiers, tokenDetails, tokenImageUrl, false);
    }

    /**
     * @dev Deploy a new SecurityToken and Whitelist/Identity Registry with option for KYC
     * @param name The name of the token
     * @param symbol The symbol of the token
     * @param decimals The number of decimals for the token (0-18)
     * @param maxSupply The maximum supply of the token
     * @param admin The address to be granted DEFAULT_ADMIN_ROLE
     * @param tokenPrice Optional token price metadata
     * @param bonusTiers Optional bonus tiers metadata
     * @param tokenDetails Optional token details metadata
     * @param tokenImageUrl Optional token image/logo URL
     * @param withKYC Whether to use WhitelistWithKYC implementation
     * @return tokenAddress The address of the deployed token
     * @return identityRegistryAddress The address of the deployed identity registry
     * @return complianceAddress The address of the deployed compliance contract
     */
    function deploySecurityTokenWithOptions(
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 maxSupply,
        address admin,
        string memory tokenPrice,
        string memory bonusTiers,
        string memory tokenDetails,
        string memory tokenImageUrl,
        bool withKYC
    ) public onlyRole(DEPLOYER_ROLE) nonReentrant returns (address tokenAddress, address identityRegistryAddress, address complianceAddress) {
        require(admin != address(0), "SecurityTokenFactory: admin cannot be zero address");
        require(bytes(name).length > 0, "SecurityTokenFactory: name cannot be empty");
        require(bytes(symbol).length > 0, "SecurityTokenFactory: symbol cannot be empty");
        require(decimals <= 18, "SecurityTokenFactory: decimals must be 18 or less");
        require(maxSupply > 0, "SecurityTokenFactory: maxSupply must be positive");
        require(_tokenAddresses[symbol] == address(0), "SecurityTokenFactory: token with this symbol already exists");

        // Deploy Whitelist/Identity Registry proxy - choose implementation based on withKYC flag
        address whitelistImpl = withKYC ? whitelistWithKYCImplementation : whitelistImplementation;
        bytes memory whitelistData = abi.encodeWithSelector(
            Whitelist(address(0)).initializeWithAgent.selector,
            admin
        );

        ERC1967Proxy identityRegistryProxy = new ERC1967Proxy(
            whitelistImpl,
            whitelistData
        );

        // Deploy Compliance proxy
        bytes memory complianceData = abi.encodeWithSelector(
            Compliance(address(0)).initialize.selector,
            admin,
            address(identityRegistryProxy)
        );

        ERC1967Proxy complianceProxy = new ERC1967Proxy(
            complianceImplementation,
            complianceData
        );

        // Deploy SecurityToken proxy
        bytes memory tokenData = abi.encodeWithSelector(
            SecurityToken(address(0)).initialize.selector,
            name,
            symbol,
            decimals,
            maxSupply,
            address(identityRegistryProxy),
            address(complianceProxy),
            admin,
            tokenPrice,
            bonusTiers,
            tokenDetails,
            tokenImageUrl
        );

        ERC1967Proxy tokenProxy = new ERC1967Proxy(
            securityTokenImplementation,
            tokenData
        );

        // Store token address
        tokenAddress = address(tokenProxy);
        identityRegistryAddress = address(identityRegistryProxy);
        complianceAddress = address(complianceProxy);
        _tokenAddresses[symbol] = tokenAddress;

        // Add to deployed tokens array for enumeration
        deployedTokens.push(tokenAddress);

        emit TokenDeployed(
            tokenAddress,
            identityRegistryAddress,
            complianceAddress,
            name,
            symbol,
            decimals,
            maxSupply,
            admin,
            withKYC,
            tokenImageUrl
        );

        return (tokenAddress, identityRegistryAddress, complianceAddress);
    }

    /**
     * @dev Update the implementation contracts
     * @param newTokenImplementation The address of the new token implementation
     * @param newWhitelistImplementation The address of the new whitelist implementation
     * @param newWhitelistWithKYCImplementation The address of the new whitelist with KYC implementation
     * @param newComplianceImplementation The address of the new compliance implementation
     */
    function updateImplementations(
        address newTokenImplementation,
        address newWhitelistImplementation,
        address newWhitelistWithKYCImplementation,
        address newComplianceImplementation
    ) external onlyRole(DEFAULT_ADMIN_ROLE) nonReentrant {
        require(newTokenImplementation != address(0), "SecurityTokenFactory: token implementation cannot be zero address");
        require(newWhitelistImplementation != address(0), "SecurityTokenFactory: whitelist implementation cannot be zero address");
        require(newWhitelistWithKYCImplementation != address(0), "SecurityTokenFactory: whitelist with KYC implementation cannot be zero address");
        require(newComplianceImplementation != address(0), "SecurityTokenFactory: compliance implementation cannot be zero address");

        // Verify the new implementations have the required interfaces
        // For token, we check if it's a valid contract by trying to call a function
        try ISecurityToken(newTokenImplementation).version() returns (string memory) {
            // Token implements version, continue
        } catch {
            revert("SecurityTokenFactory: invalid token implementation");
        }

        // For whitelists, check if they implement the required interface
        try IIdentityRegistry(newWhitelistImplementation).isWhitelisted(address(0)) returns (bool) {
            // Whitelist implements isWhitelisted, continue
        } catch {
            revert("SecurityTokenFactory: invalid whitelist implementation");
        }

        try IKYCRegistry(newWhitelistWithKYCImplementation).isKycApproved(address(0)) returns (bool) {
            // WhitelistWithKYC implements isKycApproved, continue
        } catch {
            revert("SecurityTokenFactory: invalid whitelist with KYC implementation");
        }

        emit ImplementationsUpdated(
            securityTokenImplementation,
            newTokenImplementation,
            whitelistImplementation,
            newWhitelistImplementation,
            whitelistWithKYCImplementation,
            newWhitelistWithKYCImplementation,
            complianceImplementation,
            newComplianceImplementation
        );

        securityTokenImplementation = newTokenImplementation;
        whitelistImplementation = newWhitelistImplementation;
        whitelistWithKYCImplementation = newWhitelistWithKYCImplementation;
        complianceImplementation = newComplianceImplementation;
    }

    /**
     * @dev Get the address of a token by its symbol
     * @param symbol The symbol of the token
     * @return address The address of the token
     */
    function getTokenAddressBySymbol(string memory symbol) external view returns (address) {
        return _tokenAddresses[symbol];
    }

    /**
     * @dev Get the total number of deployed tokens
     * @return uint256 The number of deployed tokens
     */
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }

    /**
     * @dev Get a deployed token address by index
     * @param index The index of the token in the deployedTokens array
     * @return address The address of the token at the given index
     */
    function getDeployedToken(uint256 index) external view returns (address) {
        require(index < deployedTokens.length, "SecurityTokenFactory: index out of bounds");
        return deployedTokens[index];
    }

    /**
     * @dev Get all deployed token addresses
     * @return address[] Array of all deployed token addresses
     */
    function getAllDeployedTokens() external view returns (address[] memory) {
        return deployedTokens;
    }

    /**
     * @dev Add a deployer
     * @param deployer The address to be granted DEPLOYER_ROLE
     */
    function addDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(deployer != address(0), "SecurityTokenFactory: deployer cannot be zero address");
        grantRole(DEPLOYER_ROLE, deployer);
    }

    /**
     * @dev Remove a deployer
     * @param deployer The address to have DEPLOYER_ROLE revoked
     */
    function removeDeployer(address deployer) external onlyRole(DEFAULT_ADMIN_ROLE) {
        revokeRole(DEPLOYER_ROLE, deployer);
    }
}