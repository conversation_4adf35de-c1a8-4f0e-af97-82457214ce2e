{"network": "amoy", "chainId": "80002", "factoryAddress": "0x0359527C9EFC960bCd64Cb4928b62cf0B8FAa0b1", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0xa655fd0ef4a733ff195860cfb96f93440d7d30f75048347ed6c28290d92143e2", "timestamp": "2025-06-22T15:37:01.338Z", "contractType": "SecurityTokenFactoryWithWhitelist", "architecture": "Enhanced with Whitelist Support", "securityLevel": "MAXIMUM", "features": {"allSecurityAuditFixes": true, "emergencyControls": true, "functionPausing": true, "enhancedReentrancyProtection": true, "improvedInputValidation": true, "fullKYCIntegration": true, "separateWhitelistContracts": true, "adminPanelIntegration": true, "roleBasedAccessControl": true, "agentManagement": true, "agreementTracking": true, "sizeOptimized": true}}