{"_format": "hh-sol-artifact-1", "contractName": "KYCClaimsModule", "sourceName": "contracts/modules/KYCClaimsModule.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "UUPSUnauthorizedCallContext", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "slot", "type": "bytes32"}], "name": "UUPSUnsupportedProxiableUUID", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}], "name": "ClaimIssued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldRegistry", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newRegistry", "type": "address"}], "name": "ClaimRegistryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "on<PERSON><PERSON><PERSON>", "type": "bool"}], "name": "KYCApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "requiredClaims", "type": "uint256[]"}, {"indexed": false, "internalType": "bool", "name": "kycEnabled", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "claimsEnabled", "type": "bool"}], "name": "TokenClaimsConfigured", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "on<PERSON><PERSON><PERSON>", "type": "bool"}], "name": "WhitelistApproved", "type": "event"}, {"inputs": [], "name": "ACCREDITED_INVESTOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CLAIM_ISSUER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "GENERAL_QUALIFICATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "JURISDICTION_COMPLIANCE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "KYC_VERIFICATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "SPECIFIC_KYC_STATUS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "approveKYC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "claimRegistry", "outputs": [{"internalType": "contract IClaimRegistry", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256[]", "name": "requiredClaims", "type": "uint256[]"}, {"internalType": "bool", "name": "kycEnabled", "type": "bool"}, {"internalType": "bool", "name": "claimsEnabled", "type": "bool"}], "name": "configureTokenClaims", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getVerificationStatus", "outputs": [{"internalType": "bool", "name": "kycApproved", "type": "bool"}, {"internalType": "bool", "name": "whitelisted", "type": "bool"}, {"internalType": "bool", "name": "eligible", "type": "bool"}, {"internalType": "string", "name": "method", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_claimRegistry", "type": "address"}, {"internalType": "address", "name": "_admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isEligible", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isKYCApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}], "name": "issueCustomClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "issueKYCClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "moduleId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_claimRegistry", "type": "address"}], "name": "setClaimRegistry", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenClaimsEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenKYCEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "tokenRequiredClaims", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "traditionalKYC", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "traditionalWhitelist", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}