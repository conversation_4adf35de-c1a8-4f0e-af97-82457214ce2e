const { ethers } = require("hardhat");

/**
 * <PERSON><PERSON><PERSON> to upgrade a module using the UpgradeManager
 * Usage: 
 * - Set environment variables: MODULE_ID, NEW_IMPLEMENTATION_ADDRESS, UPGRADE_MANAGER_ADDRESS
 * - Or pass them as command line arguments
 */

async function main() {
  console.log("🔧 Module Upgrade Script");
  console.log("========================");

  const [deployer] = await ethers.getSigners();
  console.log("Executing with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  // Get parameters from environment or command line
  const moduleId = process.env.MODULE_ID || process.argv[2];
  const newImplementationAddress = process.env.NEW_IMPLEMENTATION_ADDRESS || process.argv[3];
  const upgradeManagerAddress = process.env.UPGRADE_MANAGER_ADDRESS || process.argv[4];
  const description = process.env.UPGRADE_DESCRIPTION || process.argv[5] || "Module upgrade";
  const isEmergency = process.env.IS_EMERGENCY === "true" || process.argv[6] === "true";

  if (!moduleId || !newImplementationAddress || !upgradeManagerAddress) {
    console.error("❌ Missing required parameters:");
    console.error("Usage: npx hardhat run scripts/upgrade-module.js --network <network>");
    console.error("Environment variables required:");
    console.error("- MODULE_ID: The module identifier (e.g., 'IDENTITY_MANAGER')");
    console.error("- NEW_IMPLEMENTATION_ADDRESS: Address of the new implementation contract");
    console.error("- UPGRADE_MANAGER_ADDRESS: Address of the UpgradeManager contract");
    console.error("- UPGRADE_DESCRIPTION: Description of the upgrade (optional)");
    console.error("- IS_EMERGENCY: Set to 'true' for emergency upgrade (optional)");
    process.exit(1);
  }

  console.log("\n📋 Upgrade Parameters:");
  console.log("Module ID:", moduleId);
  console.log("New Implementation:", newImplementationAddress);
  console.log("UpgradeManager:", upgradeManagerAddress);
  console.log("Description:", description);
  console.log("Emergency Upgrade:", isEmergency);

  try {
    // Connect to UpgradeManager
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    const upgradeManager = UpgradeManager.attach(upgradeManagerAddress);

    // Convert module ID to bytes32
    const moduleIdBytes32 = ethers.keccak256(ethers.toUtf8Bytes(moduleId));
    console.log("Module ID (bytes32):", moduleIdBytes32);

    // Check if module is registered
    const moduleProxy = await upgradeManager.moduleProxies(moduleIdBytes32);
    if (moduleProxy === ethers.ZeroAddress) {
      throw new Error(`Module ${moduleId} is not registered in UpgradeManager`);
    }
    console.log("Module Proxy Address:", moduleProxy);

    // Validate new implementation
    const code = await deployer.provider.getCode(newImplementationAddress);
    if (code === "0x") {
      throw new Error("New implementation address is not a contract");
    }

    // Check current implementation
    const ERC1967Utils = await ethers.getContractAt(
      "@openzeppelin/contracts/proxy/ERC1967/ERC1967Utils.sol:ERC1967Utils",
      ethers.ZeroAddress
    );
    
    try {
      // This is a bit tricky - we need to call the implementation getter
      const currentImpl = await deployer.provider.call({
        to: moduleProxy,
        data: "0x5c60da1b" // implementation() selector
      });
      const currentImplementation = ethers.getAddress("0x" + currentImpl.slice(-40));
      console.log("Current Implementation:", currentImplementation);
      
      if (currentImplementation.toLowerCase() === newImplementationAddress.toLowerCase()) {
        console.log("⚠️ Warning: New implementation is the same as current implementation");
      }
    } catch (error) {
      console.log("Could not retrieve current implementation:", error.message);
    }

    if (isEmergency) {
      console.log("\n🚨 Executing Emergency Upgrade...");
      
      // Check if emergency mode is active
      const isEmergencyModeActive = await upgradeManager.isEmergencyModeActive();
      if (!isEmergencyModeActive) {
        console.log("Emergency mode is not active. Activating emergency mode...");
        const activateTx = await upgradeManager.activateEmergencyMode();
        await activateTx.wait();
        console.log("✅ Emergency mode activated");
      }

      // Execute emergency upgrade
      const emergencyTx = await upgradeManager.emergencyUpgrade(
        moduleIdBytes32,
        newImplementationAddress,
        description
      );
      
      console.log("Transaction hash:", emergencyTx.hash);
      console.log("⏳ Waiting for confirmation...");
      
      const receipt = await emergencyTx.wait();
      console.log("✅ Emergency upgrade completed!");
      console.log("Gas used:", receipt.gasUsed.toString());
      
    } else {
      console.log("\n⏰ Scheduling Timelock Upgrade...");
      
      // Schedule upgrade
      const scheduleTx = await upgradeManager.scheduleUpgrade(
        moduleIdBytes32,
        newImplementationAddress,
        description
      );
      
      console.log("Transaction hash:", scheduleTx.hash);
      console.log("⏳ Waiting for confirmation...");
      
      const receipt = await scheduleTx.wait();
      console.log("✅ Upgrade scheduled!");
      console.log("Gas used:", receipt.gasUsed.toString());
      
      // Extract upgrade ID from events
      const upgradeScheduledEvent = receipt.logs.find(log => {
        try {
          const parsed = upgradeManager.interface.parseLog(log);
          return parsed.name === "UpgradeScheduled";
        } catch {
          return false;
        }
      });
      
      if (upgradeScheduledEvent) {
        const parsedEvent = upgradeManager.interface.parseLog(upgradeScheduledEvent);
        const upgradeId = parsedEvent.args.upgradeId;
        const executeTime = parsedEvent.args.executeTime;
        
        console.log("\n📋 Upgrade Details:");
        console.log("Upgrade ID:", upgradeId);
        console.log("Execute Time:", new Date(Number(executeTime) * 1000).toISOString());
        console.log("Current Time:", new Date().toISOString());
        
        const timeUntilExecution = Number(executeTime) - Math.floor(Date.now() / 1000);
        if (timeUntilExecution > 0) {
          const hours = Math.floor(timeUntilExecution / 3600);
          const minutes = Math.floor((timeUntilExecution % 3600) / 60);
          console.log(`⏰ Time until execution: ${hours}h ${minutes}m`);
          
          console.log("\n📝 To execute the upgrade after the timelock:");
          console.log(`npx hardhat run scripts/execute-upgrade.js --network <network>`);
          console.log(`Environment variables:`);
          console.log(`UPGRADE_ID=${upgradeId}`);
          console.log(`UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress}`);
        }
      }
    }

    // Get upgrade history
    console.log("\n📚 Recent Upgrade History:");
    try {
      const history = await upgradeManager.getUpgradeHistory(moduleIdBytes32);
      const recentHistory = history.slice(-3); // Last 3 upgrades
      
      if (recentHistory.length === 0) {
        console.log("No previous upgrades found");
      } else {
        recentHistory.forEach((record, index) => {
          console.log(`${index + 1}. ${new Date(Number(record.timestamp) * 1000).toISOString()}`);
          console.log(`   From: ${record.oldImplementation}`);
          console.log(`   To: ${record.newImplementation}`);
          console.log(`   Emergency: ${record.isEmergency}`);
          console.log(`   Description: ${record.description}`);
        });
      }
    } catch (error) {
      console.log("Could not retrieve upgrade history:", error.message);
    }

    console.log("\n✅ Upgrade process completed successfully!");

  } catch (error) {
    console.error("\n❌ Upgrade failed:");
    console.error(error.message);
    
    if (error.data) {
      try {
        const decodedError = upgradeManager.interface.parseError(error.data);
        console.error("Decoded error:", decodedError);
      } catch {
        console.error("Raw error data:", error.data);
      }
    }
    
    process.exit(1);
  }
}

// Helper function to validate module ID
function validateModuleId(moduleId) {
  const validModuleIds = [
    "IDENTITY_MANAGER",
    "COMPLIANCE_ENGINE", 
    "TRANSFER_CONTROLLER",
    "AGENT_MANAGER",
    "EMERGENCY_MANAGER"
  ];
  
  if (!validModuleIds.includes(moduleId)) {
    console.warn(`⚠️ Warning: ${moduleId} is not a standard module ID`);
    console.warn(`Valid module IDs: ${validModuleIds.join(", ")}`);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
