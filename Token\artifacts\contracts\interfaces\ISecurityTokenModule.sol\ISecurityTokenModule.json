{"_format": "hh-sol-artifact-1", "contractName": "ISecurityTokenModule", "sourceName": "contracts/interfaces/ISecurityTokenModule.sol", "abi": [{"inputs": [], "name": "moduleId", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}