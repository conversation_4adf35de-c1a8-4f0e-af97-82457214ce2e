{"..\\node_modules\\@wagmi\\connectors\\dist\\esm\\coinbaseWallet.js -> @coinbase/wallet-sdk": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\coinbaseWallet.js -> @coinbase/wallet-sdk", "files": []}, "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\coinbaseWallet.js -> cbw-sdk": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\coinbaseWallet.js -> cbw-sdk", "files": []}, "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\metaMask.js -> @metamask/sdk": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\metaMask.js -> @metamask/sdk", "files": ["static/chunks/_app-pages-browser_node_modules_metamask_sdk_dist_browser_es_metamask-sdk_js.js"]}, "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\safe.js -> @safe-global/safe-apps-provider": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\safe.js -> @safe-global/safe-apps-provider", "files": []}, "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\safe.js -> @safe-global/safe-apps-sdk": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\safe.js -> @safe-global/safe-apps-sdk", "files": []}, "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\walletConnect.js -> @walletconnect/ethereum-provider": {"id": "..\\node_modules\\@wagmi\\connectors\\dist\\esm\\walletConnect.js -> @walletconnect/ethereum-provider", "files": []}, "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\actions\\public\\call.js -> ../../utils/ccip.js": {"id": "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\actions\\public\\call.js -> ../../utils/ccip.js", "files": []}, "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\utils\\rpc\\webSocket.js -> isows": {"id": "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\utils\\rpc\\webSocket.js -> isows", "files": []}, "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\utils\\signature\\recoverPublicKey.js -> @noble/curves/secp256k1": {"id": "..\\node_modules\\@walletconnect\\utils\\node_modules\\viem\\_esm\\utils\\signature\\recoverPublicKey.js -> @noble/curves/secp256k1", "files": []}, "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "..\\node_modules\\viem\\_esm\\actions\\public\\call.js -> ../../utils/ccip.js": {"id": "..\\node_modules\\viem\\_esm\\actions\\public\\call.js -> ../../utils/ccip.js", "files": []}, "..\\node_modules\\viem\\_esm\\utils\\rpc\\webSocket.js -> isows": {"id": "..\\node_modules\\viem\\_esm\\utils\\rpc\\webSocket.js -> isows", "files": []}, "..\\node_modules\\viem\\_esm\\utils\\signature\\recoverPublicKey.js -> @noble/curves/secp256k1": {"id": "..\\node_modules\\viem\\_esm\\utils\\signature\\recoverPublicKey.js -> @noble/curves/secp256k1", "files": []}, "app\\create-modular-token\\page.tsx -> ../../contracts/ModularTokenFactory.json": {"id": "app\\create-modular-token\\page.tsx -> ../../contracts/ModularTokenFactory.json", "files": ["static/chunks/_app-pages-browser_src_contracts_ModularTokenFactory_json.js"]}}