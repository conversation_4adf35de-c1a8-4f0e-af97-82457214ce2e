const { ethers } = require("hardhat");

// Token proxy address to upgrade
const PROXY_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚨 EMERGENCY SECURITY FIX - Deploying Fail-Secure Compliance");
  console.log("Token Address:", PROXY_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Get the current contract
    const currentContract = await ethers.getContractAt("SecurityTokenCore", PROXY_ADDRESS);
    
    console.log("🔍 BEFORE FIX - Testing current vulnerability:");
    
    // Test the current vulnerability
    const randomAddress = ethers.Wallet.createRandom().address;
    const isWhitelistedBefore = await currentContract.isWhitelisted(randomAddress);
    const isVerifiedBefore = await currentContract.isVerified(randomAddress);
    
    console.log(`Random address ${randomAddress}:`);
    console.log(`  Whitelisted: ${isWhitelistedBefore} (should be false)`);
    console.log(`  Verified: ${isVerifiedBefore} (should be false)`);
    
    if (isWhitelistedBefore || isVerifiedBefore) {
      console.log("❌ VULNERABILITY CONFIRMED - Random addresses are approved!");
    }

    // Check if user can upgrade
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    const hasAdminRole = await currentContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    if (!hasAdminRole) {
      console.log("❌ Cannot upgrade - deployer doesn't have DEFAULT_ADMIN_ROLE");
      return;
    }
    
    console.log("✅ Deployer has admin role - proceeding with emergency fix");

    // Deploy new SecurityTokenCore implementation with security fix
    console.log("\n🔧 Deploying SECURE SecurityTokenCore implementation...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    
    const newImplAddress = await newImplementation.getAddress();
    console.log("New secure implementation deployed at:", newImplAddress);

    // Upgrade the proxy to the new implementation
    console.log("\n📦 Upgrading to secure implementation...");
    const upgradeData = "0x"; // No initialization data needed
    const tx = await currentContract.upgradeToAndCall(newImplAddress, upgradeData);
    await tx.wait();
    
    console.log("✅ Security upgrade successful! Transaction:", tx.hash);

    // Verify the security fix
    console.log("\n🧪 AFTER FIX - Testing security fix:");
    
    const newRandomAddress = ethers.Wallet.createRandom().address;
    const isWhitelistedAfter = await currentContract.isWhitelisted(newRandomAddress);
    const isVerifiedAfter = await currentContract.isVerified(newRandomAddress);
    
    console.log(`New random address ${newRandomAddress}:`);
    console.log(`  Whitelisted: ${isWhitelistedAfter} (should be false)`);
    console.log(`  Verified: ${isVerifiedAfter} (should be false)`);
    
    if (!isWhitelistedAfter && !isVerifiedAfter) {
      console.log("✅ SECURITY FIX CONFIRMED - Random addresses are now properly denied!");
    } else {
      console.log("❌ SECURITY FIX FAILED - Random addresses are still approved!");
    }

    // Test that normal operations still work for properly whitelisted addresses
    console.log("\n🧪 Testing legitimate operations...");
    
    // The deployer should still be able to mint (if they have the role)
    try {
      const decimals = await currentContract.decimals();
      const testAmount = ethers.parseUnits("1", decimals);
      
      // Try to mint to deployer (should work if deployer is whitelisted)
      await currentContract.mint.staticCall(deployer.address, testAmount);
      console.log("✅ Minting to admin still works (admin is properly whitelisted)");
    } catch (mintError) {
      console.log("❌ Minting to admin failed:", mintError.message);
      console.log("This might be expected if admin isn't whitelisted in the new secure system");
    }

    // Test force transfer behavior
    console.log("\n🧪 Testing force transfer with security fix...");
    
    try {
      const decimals = await currentContract.decimals();
      const testAmount = ethers.parseUnits("1", decimals);
      
      // Try force transfer to random address (should fail now)
      await currentContract.forcedTransfer.staticCall(deployer.address, newRandomAddress, testAmount);
      console.log("❌ CRITICAL: Force transfer to non-whitelisted address still works!");
    } catch (forceError) {
      console.log("✅ Good: Force transfer to non-whitelisted address now fails:", forceError.message);
    }

    console.log("\n🎉 EMERGENCY SECURITY FIX COMPLETED!");
    console.log("📋 Summary:");
    console.log("  - Fixed fail-open vulnerability in _moduleCheck");
    console.log("  - Contract now fails secure (denies by default) when modules aren't registered");
    console.log("  - Random addresses are no longer automatically whitelisted/verified");
    console.log("  - Proper compliance modules need to be deployed and registered for full functionality");
    
    console.log("\n⚠️ NEXT STEPS REQUIRED:");
    console.log("  1. Deploy and register IdentityManager module");
    console.log("  2. Deploy and register ComplianceEngine module");
    console.log("  3. Properly whitelist legitimate addresses through the modules");
    console.log("  4. Test all functionality with proper compliance modules");

    console.log(`\n🔗 View on Polygonscan: https://amoy.polygonscan.com/address/${PROXY_ADDRESS}`);

  } catch (error) {
    console.error("❌ Emergency security fix failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
