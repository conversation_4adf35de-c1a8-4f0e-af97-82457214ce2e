{"_format": "hh-sol-artifact-1", "contractName": "IComplianceEngine", "sourceName": "contracts/interfaces/IModularToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "AgreementAccepted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "ComplianceRuleAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "ruleId", "type": "bytes32"}], "name": "ComplianceRuleRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "compliant", "type": "bool"}], "name": "TransferCompliance", "type": "event"}, {"inputs": [], "name": "acceptAgreement", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "maxHolders", "type": "uint256"}, {"internalType": "uint256", "name": "maxTokensPerHolder", "type": "uint256"}, {"internalType": "uint256", "name": "maxTotalSupply", "type": "uint256"}], "name": "addComplianceRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "canTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "destroyed", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getActiveRuleIds", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getAgreementAcceptanceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHolderCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTransferStats", "outputs": [{"internalType": "uint256", "name": "totalTransfers", "type": "uint256"}, {"internalType": "uint256", "name": "lastTransferTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "hasAcceptedAgreement", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "ruleId", "type": "bytes32"}], "name": "removeComplianceRule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferred", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}