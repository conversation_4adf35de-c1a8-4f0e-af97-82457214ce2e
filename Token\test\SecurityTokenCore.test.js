const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("SecurityTokenCore", function () {
  let SecurityTokenCore;
  let securityTokenCore;
  let owner, agent, user1, user2;

  beforeEach(async function () {
    // Get signers
    [owner, agent, user1, user2] = await ethers.getSigners();

    // Deploy SecurityTokenCore
    SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    securityTokenCore = await upgrades.deployProxy(
      SecurityTokenCore,
      [
        "Security Token Core",
        "STC",
        0, // decimals
        ethers.parseUnits("1000000", 0), // maxSupply
        owner.address,
        "10 USD",
        "Tier 1: 5%",
        "Test token for modular architecture",
        "https://example.com/token.png"
      ],
      { initializer: "initialize" }
    );
    await securityTokenCore.waitForDeployment();
  });

  describe("Initialization", function () {
    it("Should initialize with correct parameters", async function () {
      expect(await securityTokenCore.name()).to.equal("Security Token Core");
      expect(await securityTokenCore.symbol()).to.equal("STC");
      expect(await securityTokenCore.decimals()).to.equal(0);
      expect(await securityTokenCore.maxSupply()).to.equal(ethers.parseUnits("1000000", 0));
      expect(await securityTokenCore.version()).to.equal("4.0.0");
    });

    it("Should set up roles correctly", async function () {
      const DEFAULT_ADMIN_ROLE = await securityTokenCore.DEFAULT_ADMIN_ROLE();
      const AGENT_ROLE = await securityTokenCore.AGENT_ROLE();
      const TRANSFER_MANAGER_ROLE = await securityTokenCore.TRANSFER_MANAGER_ROLE();
      const MODULE_MANAGER_ROLE = await securityTokenCore.MODULE_MANAGER_ROLE();

      expect(await securityTokenCore.hasRole(DEFAULT_ADMIN_ROLE, owner.address)).to.be.true;
      expect(await securityTokenCore.hasRole(AGENT_ROLE, owner.address)).to.be.true;
      expect(await securityTokenCore.hasRole(TRANSFER_MANAGER_ROLE, owner.address)).to.be.true;
      expect(await securityTokenCore.hasRole(MODULE_MANAGER_ROLE, owner.address)).to.be.true;
    });

    it("Should return correct token metadata", async function () {
      const metadata = await securityTokenCore.getTokenMetadata();
      expect(metadata[0]).to.equal("10 USD"); // tokenPrice
      expect(metadata[1]).to.equal("Tier 1: 5%"); // bonusTiers
      expect(metadata[2]).to.equal("Test token for modular architecture"); // tokenDetails
      expect(metadata[3]).to.equal("https://example.com/token.png"); // tokenImageUrl
    });
  });

  describe("Module Management", function () {
    const IDENTITY_MANAGER_ID = ethers.keccak256(ethers.toUtf8Bytes("IDENTITY_MANAGER"));
    
    it("Should register a module", async function () {
      await securityTokenCore.registerModule(IDENTITY_MANAGER_ID, user1.address);
      
      expect(await securityTokenCore.getModule(IDENTITY_MANAGER_ID)).to.equal(user1.address);
      expect(await securityTokenCore.isAuthorizedModule(user1.address)).to.be.true;
    });

    it("Should emit ModuleRegistered event", async function () {
      await expect(securityTokenCore.registerModule(IDENTITY_MANAGER_ID, user1.address))
        .to.emit(securityTokenCore, "ModuleRegistered")
        .withArgs(IDENTITY_MANAGER_ID, user1.address);
    });

    it("Should unregister a module", async function () {
      await securityTokenCore.registerModule(IDENTITY_MANAGER_ID, user1.address);
      await securityTokenCore.unregisterModule(IDENTITY_MANAGER_ID);
      
      expect(await securityTokenCore.getModule(IDENTITY_MANAGER_ID)).to.equal(ethers.ZeroAddress);
      expect(await securityTokenCore.isAuthorizedModule(user1.address)).to.be.false;
    });

    it("Should only allow MODULE_MANAGER_ROLE to register modules", async function () {
      await expect(
        securityTokenCore.connect(user1).registerModule(IDENTITY_MANAGER_ID, user2.address)
      ).to.be.revertedWithCustomError(securityTokenCore, "AccessControlUnauthorizedAccount");
    });

    it("Should not allow registering zero address as module", async function () {
      await expect(
        securityTokenCore.registerModule(IDENTITY_MANAGER_ID, ethers.ZeroAddress)
      ).to.be.revertedWith("SecurityTokenCore: module address cannot be zero");
    });

    it("Should not allow registering same module twice", async function () {
      await securityTokenCore.registerModule(IDENTITY_MANAGER_ID, user1.address);
      
      await expect(
        securityTokenCore.registerModule(IDENTITY_MANAGER_ID, user2.address)
      ).to.be.revertedWith("SecurityTokenCore: module already registered");
    });
  });

  describe("Basic Token Functions", function () {
    it("Should start with zero total supply", async function () {
      expect(await securityTokenCore.totalSupply()).to.equal(0);
    });

    it("Should be pausable by admin", async function () {
      await securityTokenCore.pause();
      expect(await securityTokenCore.paused()).to.be.true;
      
      await securityTokenCore.unpause();
      expect(await securityTokenCore.paused()).to.be.false;
    });

    it("Should only allow admin to pause", async function () {
      await expect(
        securityTokenCore.connect(user1).pause()
      ).to.be.revertedWithCustomError(securityTokenCore, "AccessControlUnauthorizedAccount");
    });
  });

  describe("Transfer Validation", function () {
    it("Should return false for canTransfer when paused", async function () {
      await securityTokenCore.pause();
      
      const canTransfer = await securityTokenCore.canTransfer(
        owner.address, 
        user1.address, 
        ethers.parseUnits("100", 0)
      );
      
      expect(canTransfer).to.be.false;
    });

    it("Should return false for canTransfer with zero amount", async function () {
      const canTransfer = await securityTokenCore.canTransfer(
        owner.address, 
        user1.address, 
        0
      );
      
      expect(canTransfer).to.be.false;
    });

    it("Should return false for canTransfer with insufficient balance", async function () {
      const canTransfer = await securityTokenCore.canTransfer(
        owner.address, 
        user1.address, 
        ethers.parseUnits("100", 0)
      );
      
      expect(canTransfer).to.be.false;
    });
  });

  describe("Upgrade Authorization", function () {
    it("Should only allow admin to authorize upgrades", async function () {
      // This test verifies the _authorizeUpgrade function exists and has proper access control
      // We can't directly test it without deploying a new implementation, but we can verify
      // the access control by checking roles
      const DEFAULT_ADMIN_ROLE = await securityTokenCore.DEFAULT_ADMIN_ROLE();
      expect(await securityTokenCore.hasRole(DEFAULT_ADMIN_ROLE, owner.address)).to.be.true;
      expect(await securityTokenCore.hasRole(DEFAULT_ADMIN_ROLE, user1.address)).to.be.false;
    });
  });
});
