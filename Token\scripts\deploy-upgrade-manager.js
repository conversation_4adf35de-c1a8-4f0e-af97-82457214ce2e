const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🔧 Deploying UpgradeManager...");
  console.log("==============================");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  try {
    // Deploy UpgradeManager
    console.log("\n📋 Deploying UpgradeManager...");
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    
    const upgradeManager = await upgrades.deployProxy(
      UpgradeManager,
      [deployer.address], // admin address
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    await upgradeManager.waitForDeployment();
    const upgradeManagerAddress = await upgradeManager.getAddress();
    
    console.log("✅ UpgradeManager deployed to:", upgradeManagerAddress);

    // Wait for a few block confirmations
    console.log("\n⏳ Waiting for block confirmations...");
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Test the deployment
    console.log("\n🧪 Testing UpgradeManager deployment...");
    
    // Check roles
    const DEFAULT_ADMIN_ROLE = await upgradeManager.DEFAULT_ADMIN_ROLE();
    const UPGRADE_MANAGER_ROLE = await upgradeManager.UPGRADE_MANAGER_ROLE();
    const EMERGENCY_UPGRADE_ROLE = await upgradeManager.EMERGENCY_UPGRADE_ROLE();
    const TIMELOCK_ADMIN_ROLE = await upgradeManager.TIMELOCK_ADMIN_ROLE();
    
    console.log("Admin has DEFAULT_ADMIN_ROLE:", await upgradeManager.hasRole(DEFAULT_ADMIN_ROLE, deployer.address));
    console.log("Admin has UPGRADE_MANAGER_ROLE:", await upgradeManager.hasRole(UPGRADE_MANAGER_ROLE, deployer.address));
    console.log("Admin has EMERGENCY_UPGRADE_ROLE:", await upgradeManager.hasRole(EMERGENCY_UPGRADE_ROLE, deployer.address));
    console.log("Admin has TIMELOCK_ADMIN_ROLE:", await upgradeManager.hasRole(TIMELOCK_ADMIN_ROLE, deployer.address));
    
    // Check constants
    console.log("Upgrade delay:", await upgradeManager.UPGRADE_DELAY(), "seconds");
    console.log("Emergency mode duration:", await upgradeManager.EMERGENCY_MODE_DURATION(), "seconds");
    console.log("Max modules per coordinated upgrade:", await upgradeManager.MAX_MODULES_PER_COORDINATED_UPGRADE());
    
    // Check initial state
    console.log("Emergency mode active:", await upgradeManager.isEmergencyModeActive());
    console.log("Registered modules count:", (await upgradeManager.getRegisteredModules()).length);
    console.log("Pending upgrades count:", (await upgradeManager.getPendingUpgradeIds()).length);

    console.log("\n📝 Environment Variables for .env.local:");
    console.log(`UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress}`);
    
    console.log("\n🎯 Next Steps:");
    console.log("1. Add the UPGRADE_MANAGER_ADDRESS to your .env.local file");
    console.log("2. Register your modules using the registerModule function");
    console.log("3. Use the upgrade scripts to manage module upgrades");
    
    console.log("\n📚 Example Module Registration:");
    console.log(`const moduleId = ethers.keccak256(ethers.toUtf8Bytes("IDENTITY_MANAGER"));`);
    console.log(`await upgradeManager.registerModule(moduleId, moduleProxyAddress);`);
    
    console.log("\n🔧 Example Upgrade Commands:");
    console.log("Schedule upgrade:");
    console.log(`MODULE_ID=IDENTITY_MANAGER NEW_IMPLEMENTATION_ADDRESS=0x... UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress} npx hardhat run scripts/upgrade-module.js --network <network>`);
    
    console.log("\nEmergency upgrade:");
    console.log(`MODULE_ID=IDENTITY_MANAGER NEW_IMPLEMENTATION_ADDRESS=0x... UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress} IS_EMERGENCY=true npx hardhat run scripts/upgrade-module.js --network <network>`);

    console.log("\n✅ UpgradeManager deployment completed successfully!");

  } catch (error) {
    console.error("\n❌ Deployment failed:");
    console.error(error.message);
    
    if (error.data) {
      console.error("Error data:", error.data);
    }
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
