{"_format": "hh-sol-artifact-1", "contractName": "IModuleRegistry", "sourceName": "contracts/interfaces/IModularToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "moduleAddress", "type": "address"}], "name": "ModuleRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "ModuleUnregistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "ModuleUpgraded", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "moduleAddress", "type": "address"}], "name": "checkModuleCompatibility", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllModules", "outputs": [{"internalType": "bytes32[]", "name": "moduleIds", "type": "bytes32[]"}, {"internalType": "address[]", "name": "moduleAddresses", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "getModule", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "isModuleRegistered", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "moduleAddress", "type": "address"}], "name": "registerModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "unregisterModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "newModuleAddress", "type": "address"}], "name": "upgradeModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "validateModuleInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}