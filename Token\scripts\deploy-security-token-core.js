const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🔧 Deploying SecurityTokenCore...");
  console.log("==================================");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  try {
    // Deploy SecurityTokenCore
    console.log("\n📋 Deploying SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const securityTokenCore = await upgrades.deployProxy(
      SecurityTokenCore,
      [
        "Modular Security Token", // name
        "MST", // symbol
        0, // decimals
        ethers.parseUnits("1000000", 0), // maxSupply (1M tokens with 0 decimals)
        deployer.address, // admin
        "10 USD", // tokenPrice
        "Tier 1: 5%, Tier 2: 10%", // bonusTiers
        "Modular ERC-3643 compliant security token with upgradeable architecture", // tokenDetails
        "https://example.com/mst-logo.png" // tokenImageUrl
      ],
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    await securityTokenCore.waitForDeployment();
    const securityTokenCoreAddress = await securityTokenCore.getAddress();
    
    console.log("✅ SecurityTokenCore deployed to:", securityTokenCoreAddress);

    // Wait for a few block confirmations
    console.log("\n⏳ Waiting for block confirmations...");
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Test the deployment
    console.log("\n🧪 Testing SecurityTokenCore deployment...");
    
    // Check basic properties
    console.log("Token name:", await securityTokenCore.name());
    console.log("Token symbol:", await securityTokenCore.symbol());
    console.log("Token decimals:", await securityTokenCore.decimals());
    console.log("Max supply:", ethers.formatUnits(await securityTokenCore.maxSupply(), 0));
    console.log("Total supply:", ethers.formatUnits(await securityTokenCore.totalSupply(), 0));
    console.log("Version:", await securityTokenCore.version());
    
    // Check roles
    const DEFAULT_ADMIN_ROLE = await securityTokenCore.DEFAULT_ADMIN_ROLE();
    const AGENT_ROLE = await securityTokenCore.AGENT_ROLE();
    const TRANSFER_MANAGER_ROLE = await securityTokenCore.TRANSFER_MANAGER_ROLE();
    const MODULE_MANAGER_ROLE = await securityTokenCore.MODULE_MANAGER_ROLE();
    
    console.log("\nRole assignments:");
    console.log("Admin has DEFAULT_ADMIN_ROLE:", await securityTokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address));
    console.log("Admin has AGENT_ROLE:", await securityTokenCore.hasRole(AGENT_ROLE, deployer.address));
    console.log("Admin has TRANSFER_MANAGER_ROLE:", await securityTokenCore.hasRole(TRANSFER_MANAGER_ROLE, deployer.address));
    console.log("Admin has MODULE_MANAGER_ROLE:", await securityTokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address));
    
    // Check metadata
    const metadata = await securityTokenCore.getTokenMetadata();
    console.log("\nToken metadata:");
    console.log("Price:", metadata[0]);
    console.log("Bonus tiers:", metadata[1]);
    console.log("Details:", metadata[2]);
    console.log("Image URL:", metadata[3]);
    
    // Check pause functionality
    console.log("\nTesting pause functionality...");
    console.log("Initially paused:", await securityTokenCore.paused());
    
    await securityTokenCore.pause();
    console.log("After pause:", await securityTokenCore.paused());
    
    await securityTokenCore.unpause();
    console.log("After unpause:", await securityTokenCore.paused());

    console.log("\n📝 Environment Variables for .env.local:");
    console.log(`SECURITY_TOKEN_CORE_ADDRESS=${securityTokenCoreAddress}`);
    
    console.log("\n🎯 Next Steps:");
    console.log("1. Add the SECURITY_TOKEN_CORE_ADDRESS to your .env.local file");
    console.log("2. Deploy the UpgradeManager");
    console.log("3. Register the SecurityTokenCore with the UpgradeManager");
    console.log("4. Deploy and register additional modules");
    
    console.log("\n🔧 Example Module Registration:");
    console.log(`const moduleId = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));`);
    console.log(`await upgradeManager.registerModule(moduleId, "${securityTokenCoreAddress}");`);

    console.log("\n✅ SecurityTokenCore deployment completed successfully!");

  } catch (error) {
    console.error("\n❌ Deployment failed:");
    console.error(error.message);
    
    if (error.data) {
      console.error("Error data:", error.data);
    }
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
