'use client';

import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Import ABIs
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import UpgradeManagerABI from '@/contracts/UpgradeManager.json';

// Contract addresses
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

interface TestResult {
  testName: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message: string;
  duration?: number;
  details?: any;
}

interface UpgradeTestSuite {
  suiteName: string;
  tests: TestResult[];
  status: 'pending' | 'running' | 'completed';
  startTime?: number;
  endTime?: number;
}

export default function UpgradeTestingPage() {
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [testSuites, setTestSuites] = useState<UpgradeTestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  // Initialize provider
  const initializeProvider = async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send('eth_requestAccounts', []);
        const signer = await provider.getSigner();
        setProvider(provider);
        setSigner(signer);
      }
    } catch (error) {
      console.error('Failed to initialize provider:', error);
    }
  };

  // Update test result
  const updateTestResult = (suiteIndex: number, testIndex: number, result: Partial<TestResult>) => {
    setTestSuites(prev => {
      const newSuites = [...prev];
      newSuites[suiteIndex].tests[testIndex] = { ...newSuites[suiteIndex].tests[testIndex], ...result };
      return newSuites;
    });
  };

  // Update suite status
  const updateSuiteStatus = (suiteIndex: number, status: UpgradeTestSuite['status'], endTime?: number) => {
    setTestSuites(prev => {
      const newSuites = [...prev];
      newSuites[suiteIndex].status = status;
      if (endTime) newSuites[suiteIndex].endTime = endTime;
      return newSuites;
    });
  };

  // Test Suite 1: Basic Upgrade System Functionality
  const runBasicUpgradeTests = async (suiteIndex: number) => {
    if (!provider || !signer) throw new Error('Provider not initialized');

    const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, signer);
    const tokenCore = new ethers.Contract(SECURITY_TOKEN_CORE_ADDRESS!, SecurityTokenCoreABI, provider);

    // Test 1: Check upgrade manager initialization
    setCurrentTest('Checking upgrade manager initialization...');
    updateTestResult(suiteIndex, 0, { status: 'running' });
    const startTime1 = Date.now();
    
    try {
      const emergencyModeActive = await upgradeManager.isEmergencyModeActive();
      const upgradeDelay = await upgradeManager.UPGRADE_DELAY();
      const emergencyDuration = await upgradeManager.EMERGENCY_MODE_DURATION();
      
      updateTestResult(suiteIndex, 0, {
        status: 'passed',
        message: `Emergency Mode: ${emergencyModeActive}, Delay: ${upgradeDelay}s, Duration: ${emergencyDuration}s`,
        duration: Date.now() - startTime1,
        details: { emergencyModeActive, upgradeDelay: upgradeDelay.toString(), emergencyDuration: emergencyDuration.toString() }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 0, {
        status: 'failed',
        message: `Failed to check upgrade manager: ${error.message}`,
        duration: Date.now() - startTime1
      });
    }

    // Test 2: Check token version and implementation
    setCurrentTest('Checking token version and implementation...');
    updateTestResult(suiteIndex, 1, { status: 'running' });
    const startTime2 = Date.now();
    
    try {
      const version = await tokenCore.version();
      const implementation = await provider.getStorage(SECURITY_TOKEN_CORE_ADDRESS!, '0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc');
      
      updateTestResult(suiteIndex, 1, {
        status: 'passed',
        message: `Version: ${version}, Implementation: ${implementation.slice(0, 10)}...`,
        duration: Date.now() - startTime2,
        details: { version, implementation }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 1, {
        status: 'failed',
        message: `Failed to check token version: ${error.message}`,
        duration: Date.now() - startTime2
      });
    }

    // Test 3: Check pending upgrades
    setCurrentTest('Checking pending upgrades...');
    updateTestResult(suiteIndex, 2, { status: 'running' });
    const startTime3 = Date.now();
    
    try {
      const pendingUpgradeIds = await upgradeManager.getPendingUpgradeIds();
      const pendingCount = pendingUpgradeIds.length;
      
      updateTestResult(suiteIndex, 2, {
        status: 'passed',
        message: `Found ${pendingCount} pending upgrades`,
        duration: Date.now() - startTime3,
        details: { pendingUpgradeIds, pendingCount }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 2, {
        status: 'failed',
        message: `Failed to check pending upgrades: ${error.message}`,
        duration: Date.now() - startTime3
      });
    }

    // Test 4: Check upgrade history
    setCurrentTest('Checking upgrade history...');
    updateTestResult(suiteIndex, 3, { status: 'running' });
    const startTime4 = Date.now();
    
    try {
      const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
      const history = await upgradeManager.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);
      
      updateTestResult(suiteIndex, 3, {
        status: 'passed',
        message: `Found ${history.length} historical upgrades`,
        duration: Date.now() - startTime4,
        details: { historyCount: history.length, history: history.slice(0, 3) } // Show first 3
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 3, {
        status: 'failed',
        message: `Failed to check upgrade history: ${error.message}`,
        duration: Date.now() - startTime4
      });
    }
  };

  // Test Suite 2: Emergency Mode Testing
  const runEmergencyModeTests = async (suiteIndex: number) => {
    if (!provider || !signer) throw new Error('Provider not initialized');

    const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, signer);

    // Test 1: Check emergency mode permissions
    setCurrentTest('Checking emergency mode permissions...');
    updateTestResult(suiteIndex, 0, { status: 'running' });
    const startTime1 = Date.now();

    try {
      const signerAddress = await signer.getAddress();
      const hasEmergencyRole = await upgradeManager.hasRole(
        await upgradeManager.EMERGENCY_ROLE(),
        signerAddress
      );

      updateTestResult(suiteIndex, 0, {
        status: hasEmergencyRole ? 'passed' : 'failed',
        message: hasEmergencyRole ? 'Has emergency role' : 'Missing emergency role',
        duration: Date.now() - startTime1,
        details: { signerAddress, hasEmergencyRole }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 0, {
        status: 'failed',
        message: `Failed to check emergency permissions: ${error.message}`,
        duration: Date.now() - startTime1
      });
    }

    // Test 2: Check emergency mode status
    setCurrentTest('Checking emergency mode status...');
    updateTestResult(suiteIndex, 1, { status: 'running' });
    const startTime2 = Date.now();

    try {
      const isActive = await upgradeManager.isEmergencyModeActive();
      const activatedAt = await upgradeManager.emergencyModeActivatedAt();
      const duration = await upgradeManager.EMERGENCY_MODE_DURATION();

      updateTestResult(suiteIndex, 1, {
        status: 'passed',
        message: `Emergency mode: ${isActive ? 'Active' : 'Inactive'}`,
        duration: Date.now() - startTime2,
        details: { isActive, activatedAt: activatedAt.toString(), duration: duration.toString() }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 1, {
        status: 'failed',
        message: `Failed to check emergency mode status: ${error.message}`,
        duration: Date.now() - startTime2
      });
    }
  };

  // Test Suite 3: Upgrade Simulation Testing
  const runUpgradeSimulationTests = async (suiteIndex: number) => {
    if (!provider || !signer) throw new Error('Provider not initialized');

    const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, signer);

    // Test 1: Simulate upgrade scheduling
    setCurrentTest('Simulating upgrade scheduling...');
    updateTestResult(suiteIndex, 0, { status: 'running' });
    const startTime1 = Date.now();

    try {
      // Use a dummy implementation address for testing
      const dummyImplementation = '******************************************';
      const description = `Test upgrade simulation ${Date.now()}`;

      // Check if we can schedule (without actually doing it)
      const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
      const upgradeDelay = await upgradeManager.UPGRADE_DELAY();

      updateTestResult(suiteIndex, 0, {
        status: 'passed',
        message: `Upgrade simulation prepared (delay: ${upgradeDelay}s)`,
        duration: Date.now() - startTime1,
        details: { dummyImplementation, description, upgradeDelay: upgradeDelay.toString() }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 0, {
        status: 'failed',
        message: `Failed to simulate upgrade scheduling: ${error.message}`,
        duration: Date.now() - startTime1
      });
    }

    // Test 2: Check timelock mechanism
    setCurrentTest('Testing timelock mechanism...');
    updateTestResult(suiteIndex, 1, { status: 'running' });
    const startTime2 = Date.now();

    try {
      const upgradeDelay = await upgradeManager.UPGRADE_DELAY();
      const currentTime = Math.floor(Date.now() / 1000);
      const executeTime = currentTime + Number(upgradeDelay);

      // Verify timelock calculation
      const timeDiff = executeTime - currentTime;
      const isValidTimelock = timeDiff >= Number(upgradeDelay);

      updateTestResult(suiteIndex, 1, {
        status: isValidTimelock ? 'passed' : 'failed',
        message: `Timelock mechanism: ${isValidTimelock ? 'Valid' : 'Invalid'} (${timeDiff}s)`,
        duration: Date.now() - startTime2,
        details: { upgradeDelay: upgradeDelay.toString(), timeDiff, isValidTimelock }
      });
    } catch (error: any) {
      updateTestResult(suiteIndex, 1, {
        status: 'failed',
        message: `Failed to test timelock mechanism: ${error.message}`,
        duration: Date.now() - startTime2
      });
    }
  };

  // Initialize test suites
  const initializeTestSuites = () => {
    const suites: UpgradeTestSuite[] = [
      {
        suiteName: 'Basic Upgrade System Functionality',
        status: 'pending',
        tests: [
          { testName: 'Upgrade Manager Initialization', status: 'pending', message: 'Waiting to run...' },
          { testName: 'Token Version & Implementation', status: 'pending', message: 'Waiting to run...' },
          { testName: 'Pending Upgrades Check', status: 'pending', message: 'Waiting to run...' },
          { testName: 'Upgrade History Check', status: 'pending', message: 'Waiting to run...' }
        ]
      },
      {
        suiteName: 'Emergency Mode Testing',
        status: 'pending',
        tests: [
          { testName: 'Emergency Mode Permissions', status: 'pending', message: 'Waiting to run...' },
          { testName: 'Emergency Mode Status', status: 'pending', message: 'Waiting to run...' }
        ]
      },
      {
        suiteName: 'Upgrade Simulation Testing',
        status: 'pending',
        tests: [
          { testName: 'Upgrade Scheduling Simulation', status: 'pending', message: 'Waiting to run...' },
          { testName: 'Timelock Mechanism Test', status: 'pending', message: 'Waiting to run...' }
        ]
      }
    ];
    setTestSuites(suites);
  };

  // Run all tests
  const runAllTests = async () => {
    if (!provider || !signer) {
      alert('Please connect your wallet first');
      return;
    }

    setIsRunning(true);
    initializeTestSuites();

    try {
      // Run Basic Upgrade Tests
      updateSuiteStatus(0, 'running');
      await runBasicUpgradeTests(0);
      updateSuiteStatus(0, 'completed', Date.now());

      // Run Emergency Mode Tests
      updateSuiteStatus(1, 'running');
      await runEmergencyModeTests(1);
      updateSuiteStatus(1, 'completed', Date.now());

      // Run Upgrade Simulation Tests
      updateSuiteStatus(2, 'running');
      await runUpgradeSimulationTests(2);
      updateSuiteStatus(2, 'completed', Date.now());

      setCurrentTest('All tests completed!');
    } catch (error: any) {
      console.error('Test execution failed:', error);
      setCurrentTest(`Test execution failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    initializeProvider();
    initializeTestSuites();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Upgrade System Testing</h1>
          <p className="text-gray-600">
            Comprehensive testing suite for the modular upgrade system
          </p>
        </div>
        <div className="flex gap-2">
          {!provider ? (
            <button
              onClick={initializeProvider}
              className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Connect Wallet
            </button>
          ) : (
            <button
              onClick={runAllTests}
              disabled={isRunning}
              className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
            >
              {isRunning ? '🔄 Running Tests...' : '🧪 Run All Tests'}
            </button>
          )}
        </div>
      </div>

      {/* Current Test Status */}
      {isRunning && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-800 font-medium">{currentTest}</span>
          </div>
        </div>
      )}

      {/* Test Suites */}
      <div className="space-y-6">
        {testSuites.map((suite, suiteIndex) => (
          <div key={suiteIndex} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">{suite.suiteName}</h2>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                suite.status === 'completed' ? 'bg-green-100 text-green-800' :
                suite.status === 'running' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {suite.status === 'completed' ? '✅ Completed' :
                 suite.status === 'running' ? '🔄 Running' :
                 '⏳ Pending'}
              </span>
            </div>

            <div className="space-y-3">
              {suite.tests.map((test, testIndex) => (
                <div key={testIndex} className="border rounded-lg p-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className={`inline-flex w-4 h-4 rounded-full ${
                          test.status === 'passed' ? 'bg-green-500' :
                          test.status === 'failed' ? 'bg-red-500' :
                          test.status === 'running' ? 'bg-blue-500 animate-pulse' :
                          'bg-gray-300'
                        }`}></span>
                        <span className="font-medium">{test.testName}</span>
                        {test.duration && (
                          <span className="text-xs text-gray-500">({test.duration}ms)</span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{test.message}</p>
                      {test.details && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">View Details</summary>
                          <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
