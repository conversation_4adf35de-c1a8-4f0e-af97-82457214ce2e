import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function pause() external",
  "function paused() external view returns (bool)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress } = await request.json();

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      );
    }

    // Validate address
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Check current pause status
    const wasPaused = await tokenContract.paused();
    console.log('Token was paused:', wasPaused);

    if (wasPaused) {
      return NextResponse.json({
        success: true,
        message: 'Token is already paused',
        wasPaused: true,
        isPaused: true,
        tokenAddress
      });
    }

    // Pause token
    const tx = await tokenContract.pause();
    await tx.wait();

    // Verify pause
    const isPaused = await tokenContract.paused();

    console.log('Token paused successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'Token paused successfully',
      txHash: tx.hash,
      wasPaused: false,
      isPaused: isPaused,
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error pausing token:', error);
    return NextResponse.json(
      { error: `Failed to pause token: ${error.message}` },
      { status: 500 }
    );
  }
}
