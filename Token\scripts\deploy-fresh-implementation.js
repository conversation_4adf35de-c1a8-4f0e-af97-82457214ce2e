const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying fresh SecurityTokenCore implementation...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  try {
    // Deploy a fresh SecurityTokenCore implementation (without initialization)
    console.log("📦 Deploying SecurityTokenCore implementation...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const implementation = await SecurityTokenCore.deploy();
    await implementation.waitForDeployment();
    
    const implementationAddress = await implementation.getAddress();
    console.log("✅ Fresh SecurityTokenCore implementation deployed to:", implementationAddress);

    // Verify it's not initialized
    try {
      const name = await implementation.name();
      console.log("❌ Implementation is already initialized! Name:", name);
    } catch (e) {
      console.log("✅ Implementation is not initialized (as expected)");
    }

    // Now update the factory to use the new implementation
    const FACTORY_ADDRESS = "******************************************";
    
    console.log("\n🔄 Updating factory to use new implementation...");
    
    // Note: The current factory doesn't have a function to update the implementation
    // We would need to deploy a new factory with the new implementation
    
    console.log("📦 Deploying new ModularTokenFactory with fresh implementation...");
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    
    const UPGRADE_MANAGER_ADDRESS = "******************************************";
    
    const newFactory = await ModularTokenFactory.deploy(
      implementationAddress,     // Fresh implementation
      UPGRADE_MANAGER_ADDRESS,   // Existing upgrade manager
      deployer.address          // Admin
    );

    await newFactory.waitForDeployment();
    const newFactoryAddress = await newFactory.getAddress();

    console.log("✅ New ModularTokenFactory deployed to:", newFactoryAddress);

    // Test the new factory
    console.log("\n🧪 Testing new factory...");
    
    const testTx = await newFactory.deployToken(
      "FreshTest",
      "FT",
      0,
      ethers.parseUnits("1000", 0),
      deployer.address,
      "1 USD",
      "None",
      "Fresh test token",
      ""
    );

    const receipt = await testTx.wait();
    console.log("✅ Test deployment successful!");

    // Get the deployed token address
    const event = receipt.logs.find(log => {
      try {
        const parsed = newFactory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (event) {
      const parsedEvent = newFactory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;
      
      console.log("🎉 Test token deployed to:", tokenAddress);

      // Test the token
      const token = SecurityTokenCore.attach(tokenAddress);
      const name = await token.name();
      const symbol = await token.symbol();
      const decimals = await token.decimals();
      
      console.log("✅ Token name:", name);
      console.log("✅ Token symbol:", symbol);
      console.log("✅ Token decimals:", decimals);
    }

    console.log("\n🎉 Fresh implementation and factory deployment completed!");
    console.log("\n📋 New Addresses:");
    console.log("=" .repeat(60));
    console.log(`Fresh SecurityTokenCore Implementation: ${implementationAddress}`);
    console.log(`New ModularTokenFactory: ${newFactoryAddress}`);
    console.log(`UpgradeManager (unchanged): ${UPGRADE_MANAGER_ADDRESS}`);
    console.log("=" .repeat(60));

    return {
      implementationAddress,
      factoryAddress: newFactoryAddress,
      upgradeManagerAddress: UPGRADE_MANAGER_ADDRESS
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
