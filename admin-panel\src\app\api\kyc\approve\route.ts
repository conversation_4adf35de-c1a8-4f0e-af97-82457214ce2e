import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function approveKYC(address user) external",
  "function addToWhitelist(address account) external"
];

const KYC_CLAIMS_MODULE_ABI = [
  "function approveKYC(address token, address user) external",
  "function issueKYCClaim(address user, bytes calldata data) external returns (bytes32)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, userAddress } = await request.json();

    if (!tokenAddress || !userAddress) {
      return NextResponse.json(
        { error: 'Token address and user address are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(userAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
    const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    let txHash: string;

    if (kycModuleAddress && ethers.isAddress(kycModuleAddress)) {
      // Use KYC Claims Module if available
      console.log('Using KYC Claims Module for KYC approval');
      const kycModule = new ethers.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, signer);
      
      try {
        const tx = await kycModule.approveKYC(tokenAddress, userAddress);
        await tx.wait();
        txHash = tx.hash;
        console.log('KYC approved via KYC Claims Module:', txHash);
      } catch (moduleError) {
        console.log('KYC Claims Module failed, falling back to direct token call:', moduleError);
        
        // Fallback to direct token call
        const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);
        const tx = await tokenContract.approveKYC(userAddress);
        await tx.wait();
        txHash = tx.hash;
        console.log('KYC approved via direct token call:', txHash);
      }
    } else {
      // Direct token call
      console.log('Using direct token call for KYC approval');
      const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);
      const tx = await tokenContract.approveKYC(userAddress);
      await tx.wait();
      txHash = tx.hash;
      console.log('KYC approved via direct token call:', txHash);
    }

    return NextResponse.json({
      success: true,
      message: 'KYC approved successfully',
      txHash,
      userAddress,
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error approving KYC:', error);
    return NextResponse.json(
      { error: `Failed to approve KYC: ${error.message}` },
      { status: 500 }
    );
  }
}
