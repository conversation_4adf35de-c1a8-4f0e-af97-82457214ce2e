const { ethers } = require("hardhat");

// Token address and the problematic transfer
const TOKEN_ADDRESS = "******************************************";
const NON_WHITELISTED_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚨 INVESTIGATING SECURITY BREACH - Force Transfer to Non-Whitelisted Address");
  console.log("Token Address:", TOKEN_ADDRESS);
  console.log("Non-whitelisted recipient:", NON_WHITELISTED_ADDRESS);
  console.log("Investigator:", deployer.address);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    console.log("\n🔍 CHECKING RECIPIENT STATUS:");
    
    // Check if the address is whitelisted
    try {
      const isWhitelisted = await tokenContract.isWhitelisted(NON_WHITELISTED_ADDRESS);
      console.log(`❌ Is whitelisted: ${isWhitelisted}`);
    } catch (whitelistError) {
      console.log("❌ Error checking whitelist status:", whitelistError.message);
    }

    // Check if the address is verified
    try {
      const isVerified = await tokenContract.isVerified(NON_WHITELISTED_ADDRESS);
      console.log(`❌ Is verified: ${isVerified}`);
    } catch (verifyError) {
      console.log("❌ Error checking verification status:", verifyError.message);
    }

    // Check current balance
    const balance = await tokenContract.balanceOf(NON_WHITELISTED_ADDRESS);
    const decimals = await tokenContract.decimals();
    console.log(`💰 Current balance: ${ethers.formatUnits(balance, decimals)} tokens`);

    console.log("\n🔍 ANALYZING FORCE TRANSFER IMPLEMENTATION:");
    
    // Let's examine what our force transfer function actually does
    console.log("Checking the current force transfer implementation...");
    
    // Try to understand why the transfer succeeded
    console.log("\n🔍 CHECKING COMPLIANCE MODULES:");
    
    // Check if there are any registered modules
    try {
      // Try to get module addresses (this might fail if modules aren't registered)
      console.log("Checking for registered compliance modules...");
      
      // The issue might be that we're bypassing ALL compliance checks
      // Let's see what happens in a normal transfer
      console.log("\n🧪 TESTING NORMAL TRANSFER TO SAME ADDRESS:");
      
      // Try a normal transfer to the same non-whitelisted address
      try {
        // This should fail if compliance is working
        await tokenContract.transfer.staticCall(NON_WHITELISTED_ADDRESS, ethers.parseUnits("1", decimals));
        console.log("❌ CRITICAL: Normal transfer to non-whitelisted address would SUCCEED!");
      } catch (normalTransferError) {
        console.log("✅ Good: Normal transfer to non-whitelisted address fails:", normalTransferError.message);
      }

    } catch (moduleError) {
      console.log("❌ Error checking modules:", moduleError.message);
    }

    console.log("\n🔍 CHECKING CONTRACT IMPLEMENTATION:");
    
    // Get the current implementation address
    const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
    const implementationAddress = await deployer.provider.getStorage(TOKEN_ADDRESS, implementationSlot);
    const cleanImplementationAddress = "0x" + implementationAddress.slice(-40);
    
    console.log("Current implementation:", cleanImplementationAddress);

    // Check the bytecode to see what we actually deployed
    const implementationCode = await deployer.provider.getCode(cleanImplementationAddress);
    console.log("Implementation bytecode length:", implementationCode.length);

    console.log("\n🚨 SECURITY ANALYSIS:");
    console.log("1. Force transfer succeeded to non-whitelisted address");
    console.log("2. This violates security token compliance requirements");
    console.log("3. The _forcedTransferInProgress flag is bypassing ALL compliance checks");
    console.log("4. This is a CRITICAL security vulnerability");

    console.log("\n💡 LIKELY CAUSE:");
    console.log("The force transfer implementation sets _forcedTransferInProgress = true");
    console.log("This flag bypasses compliance checks in the _update function");
    console.log("But it's bypassing TOO MUCH - including whitelist verification");

    console.log("\n🔧 REQUIRED FIX:");
    console.log("Force transfers should still require:");
    console.log("- Recipient to be verified (KYC)");
    console.log("- Recipient to be whitelisted");
    console.log("- Only bypass frozen status checks");

    // Test what a proper force transfer should look like
    console.log("\n🧪 TESTING PROPER COMPLIANCE:");
    
    // Check if we can whitelist the address first
    console.log("Attempting to whitelist the address first...");
    try {
      // This would require proper identity registration first
      const whitelistTx = await tokenContract.addToWhitelist(NON_WHITELISTED_ADDRESS);
      await whitelistTx.wait();
      console.log("✅ Address whitelisted successfully");
      
      const isNowWhitelisted = await tokenContract.isWhitelisted(NON_WHITELISTED_ADDRESS);
      console.log(`✅ Verification - Is now whitelisted: ${isNowWhitelisted}`);
      
    } catch (whitelistError) {
      console.log("❌ Cannot whitelist address:", whitelistError.message);
      console.log("This might be because the address needs identity registration first");
    }

  } catch (error) {
    console.error("❌ Investigation failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
