{"_format": "hh-sol-artifact-1", "contractName": "IEmergencyManager", "sourceName": "contracts/interfaces/IModularToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": true, "internalType": "address", "name": "triggered<PERSON>y", "type": "address"}], "name": "CircuitBreakerTriggered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "EmergencyUnpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionPaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes4", "name": "functionSelector", "type": "bytes4"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FunctionUnpaused", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "actionId", "type": "bytes32"}], "name": "cancelScheduledAction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "actionId", "type": "bytes32"}], "name": "executeScheduledAction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCircuitBreakerThreshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "actionId", "type": "bytes32"}], "name": "getScheduledAction", "outputs": [{"internalType": "bytes", "name": "action", "type": "bytes"}, {"internalType": "uint256", "name": "executeTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isCircuitBreakerTriggered", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "isEmergencyPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "isFunctionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "pauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "resetCircuitBreaker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "action", "type": "bytes"}, {"internalType": "uint256", "name": "delay", "type": "uint256"}], "name": "scheduleEmergencyAction", "outputs": [{"internalType": "bytes32", "name": "actionId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "name": "setCircuitBreakerThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "reason", "type": "string"}], "name": "triggerCircuitBreaker", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "unpauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}