import { NextRequest, NextResponse } from 'next/server';

interface InvestorData {
  id: string;
  walletAddress: string;
  email: string;
  kycStatus: 'pending' | 'approved' | 'rejected' | 'expired';
  whitelistStatus: {
    [tokenAddress: string]: {
      approved: boolean;
      approvedAt?: string;
      approvedBy?: string;
    };
  };
  qualificationStatus: 'not_started' | 'in_progress' | 'completed' | 'rejected';
  tokens: {
    address: string;
    name: string;
    symbol: string;
    balance: string;
    frozenBalance: string;
    totalInvested: string;
    averagePrice: string;
    firstPurchase: string;
    lastTransaction: string;
  }[];
  profile: {
    firstName?: string;
    lastName?: string;
    country?: string;
    investorType?: 'individual' | 'institutional';
    accreditationStatus?: 'accredited' | 'non_accredited' | 'pending';
  };
  compliance: {
    claimsIssued: string[];
    agreementsAccepted: string[];
    lastKycUpdate?: string;
    riskAssessment?: 'low' | 'medium' | 'high';
  };
  statistics: {
    totalInvestments: number;
    totalValue: string;
    portfolioTokens: number;
    joinedDate: string;
    lastActivity: string;
  };
}

// Get investor data from database
async function getInvestorData(walletAddress?: string, email?: string): Promise<InvestorData[]> {
  try {
    // For now, return mock data since database might not be available
    const mockInvestors: InvestorData[] = [
      {
        id: 'inv_001',
        walletAddress: '******************************************',
        email: '<EMAIL>',
        kycStatus: 'approved',
        whitelistStatus: {
          '******************************************': {
            approved: true,
            approvedAt: '2024-01-15T12:00:00Z',
            approvedBy: 'admin'
          }
        },
        qualificationStatus: 'completed',
        tokens: [
          {
            address: '******************************************',
            name: 'Augment Security Token',
            symbol: 'AST',
            balance: '1000',
            frozenBalance: '0',
            totalInvested: '9990',
            averagePrice: '9.99',
            firstPurchase: '2024-01-15T14:30:00Z',
            lastTransaction: '2024-01-18T09:15:00Z'
          }
        ],
        profile: {
          firstName: 'Admin',
          lastName: 'User',
          country: 'United States',
          investorType: 'institutional',
          accreditationStatus: 'accredited'
        },
        compliance: {
          claimsIssued: ['10101010000001'],
          agreementsAccepted: ['******************************************'],
          lastKycUpdate: '2024-01-15T11:00:00Z',
          riskAssessment: 'low'
        },
        statistics: {
          totalInvestments: 1,
          totalValue: '9990',
          portfolioTokens: 1,
          joinedDate: '2024-01-15T10:00:00Z',
          lastActivity: '2024-01-18T09:15:00Z'
        }
      }
    ];

    // Filter by wallet address if provided
    if (walletAddress) {
      return mockInvestors.filter(inv => inv.walletAddress.toLowerCase() === walletAddress.toLowerCase());
    }

    // Filter by email if provided
    if (email) {
      return mockInvestors.filter(inv => inv.email.toLowerCase() === email.toLowerCase());
    }

    return mockInvestors;

    return mockInvestors;
  } catch (error) {
    console.error('Error fetching investor data:', error);
    return [];
  }
}

// GET /api/external/investors - Get investor data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const walletAddress = searchParams.get('wallet');
    const email = searchParams.get('email');
    const includeStats = searchParams.get('stats') === 'true';

    // CORS headers for external API access
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=300' // 5 minutes cache
    };

    const investors = await getInvestorData(walletAddress || undefined, email || undefined);

    let response: any = {
      success: true,
      data: {
        investors,
        totalCount: investors.length
      },
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };

    // Add statistics if requested
    if (includeStats) {
      const stats = {
        totalInvestors: investors.length,
        kycApproved: investors.filter(i => i.kycStatus === 'approved').length,
        kycPending: investors.filter(i => i.kycStatus === 'pending').length,
        qualificationCompleted: investors.filter(i => i.qualificationStatus === 'completed').length,
        totalInvestmentValue: investors.reduce((sum, investor) => 
          sum + parseFloat(investor.statistics.totalValue), 0
        ).toString(),
        averageInvestmentValue: investors.length > 0 ? 
          (investors.reduce((sum, investor) => 
            sum + parseFloat(investor.statistics.totalValue), 0
          ) / investors.length).toString() : '0',
        investorsByCountry: investors.reduce((acc: any, investor) => {
          const country = investor.profile.country || 'Unknown';
          acc[country] = (acc[country] || 0) + 1;
          return acc;
        }, {}),
        investorsByType: investors.reduce((acc: any, investor) => {
          const type = investor.profile.investorType || 'individual';
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {})
      };

      response.data.statistics = stats;
    }

    return NextResponse.json(response, { headers });
  } catch (error: any) {
    console.error('External investors API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error.message,
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
