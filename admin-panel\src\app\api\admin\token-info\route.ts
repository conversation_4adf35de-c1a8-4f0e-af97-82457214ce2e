import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function name() external view returns (string memory)",
  "function symbol() external view returns (string memory)",
  "function decimals() external view returns (uint8)",
  "function totalSupply() external view returns (uint256)",
  "function maxSupply() external view returns (uint256)",
  "function paused() external view returns (bool)",
  "function version() external view returns (string memory)",
  "function getTokenMetadata() external view returns (string memory tokenPrice, string memory bonusTiers, string memory tokenDetails, string memory tokenImageUrl)"
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      );
    }

    // Validate address
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;

    if (!rpcUrl) {
      return NextResponse.json(
        { error: 'RPC URL not configured' },
        { status: 500 }
      );
    }

    // Setup provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);

    // Get all token information
    const [
      name,
      symbol,
      decimals,
      totalSupply,
      maxSupply,
      paused,
      version,
      metadata
    ] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.decimals(),
      tokenContract.totalSupply(),
      tokenContract.maxSupply(),
      tokenContract.paused(),
      tokenContract.version(),
      tokenContract.getTokenMetadata()
    ]);

    console.log('Token info retrieved successfully for:', tokenAddress);

    return NextResponse.json({
      success: true,
      tokenAddress,
      tokenInfo: {
        name,
        symbol,
        decimals: decimals.toString(),
        totalSupply: totalSupply.toString(),
        maxSupply: maxSupply.toString(),
        paused,
        version,
        metadata: {
          tokenPrice: metadata[0],
          bonusTiers: metadata[1],
          tokenDetails: metadata[2],
          tokenImageUrl: metadata[3]
        }
      }
    });

  } catch (error: any) {
    console.error('Error getting token info:', error);
    return NextResponse.json(
      { error: `Failed to get token info: ${error.message}` },
      { status: 500 }
    );
  }
}
