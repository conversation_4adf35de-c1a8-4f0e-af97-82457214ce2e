{"_format": "hh-sol-artifact-1", "contractName": "IAgentManager", "sourceName": "contracts/interfaces/IModularToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}], "name": "AgentRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "AgentRoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "agent", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "AgentRoleRevoked", "type": "event"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "addAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "agents", "type": "address[]"}], "name": "batchAddAgents", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "agents", "type": "address[]"}], "name": "batchRemoveAgents", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "canPerformAction", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getAgentAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAgentCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "getAgentRoles", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllAgents", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}, {"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "grantAgentRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "hasAgentRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isAgent", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}], "name": "removeAgent", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "agent", "type": "address"}, {"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "revokeAgentRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}