'use client';

import { useState, useEffect } from 'react';
import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ethers } from 'ethers';
import SecurityTokenABI from '../../contracts/SecurityToken.json';

interface TransferControlsState {
  conditionalTransfersEnabled: boolean;
  transferWhitelistEnabled: boolean;
  transferFeesEnabled: boolean;
  transferFeePercentage: number;
  feeCollector: string;
  currency: string;
}

interface Token {
  address: string;
  name: string;
  symbol: string;
}

export default function TransferControlsPage() {
  const { address, isConnected } = useAccount();
  const { connect, connectors } = useConnect();
  const { disconnect } = useDisconnect();
  const searchParams = useSearchParams();
  const [selectedToken, setSelectedToken] = useState<string>('');
  const [tokens, setTokens] = useState<Token[]>([]);
  const [controls, setControls] = useState<TransferControlsState>({
    conditionalTransfersEnabled: false,
    transferWhitelistEnabled: false,
    transferFeesEnabled: false,
    transferFeePercentage: 0,
    feeCollector: '',
    currency: 'USD'
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [customTokenAddress, setCustomTokenAddress] = useState('');

  // Currency options
  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
    { value: 'CHF', label: 'CHF - Swiss Franc' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
    { value: 'BTC', label: 'BTC - Bitcoin' },
    { value: 'ETH', label: 'ETH - Ethereum' },
    { value: 'USDC', label: 'USDC - USD Coin' },
    { value: 'USDT', label: 'USDT - Tether' }
  ];

  // Known tokens for demo
  const knownTokens: Token[] = [
    {
      address: "******************************************",
      name: "Advanced Control Token",
      symbol: "ACT"
    },
    {
      address: "******************************************",
      name: "Augment_019",
      symbol: "AUG019"
    },
    {
      address: "******************************************",
      name: "Augment_01z",
      symbol: "AUG01Z"
    }
  ];

  // Helper function to ensure unique tokens
  const addOrUpdateToken = (newToken: Token, tokensList: Token[]): Token[] => {
    const existingIndex = tokensList.findIndex(t =>
      t.address.toLowerCase() === newToken.address.toLowerCase()
    );

    if (existingIndex !== -1) {
      // Update existing token
      const updatedTokens = [...tokensList];
      updatedTokens[existingIndex] = newToken;
      console.log(`Updated existing token: ${newToken.name} (${newToken.address})`);
      return updatedTokens;
    } else {
      // Add new token
      console.log(`Added new token: ${newToken.name} (${newToken.address})`);
      return [newToken, ...tokensList];
    }
  };

  // Helper function to validate token list uniqueness
  const validateTokenUniqueness = (tokensList: Token[]) => {
    const addresses = tokensList.map(t => t.address.toLowerCase());
    const uniqueAddresses = new Set(addresses);
    if (addresses.length !== uniqueAddresses.size) {
      console.warn('Duplicate token addresses detected:', addresses);
      const duplicates = addresses.filter((addr, index) => addresses.indexOf(addr) !== index);
      console.warn('Duplicate addresses:', duplicates);
    }
  };

  useEffect(() => {
    const initializeTokens = async () => {
      // Start with known tokens, ensuring uniqueness
      let initialTokens = [...knownTokens];

      // Check if token is provided via URL parameter
      const tokenParam = searchParams.get('token');
      if (tokenParam && ethers.isAddress(tokenParam)) {
        setSelectedToken(tokenParam);

        // If the token is not in our known tokens list, add it with fetched info
        if (!initialTokens.find(t => t.address.toLowerCase() === tokenParam.toLowerCase())) {
          let tokenName = `Token ${tokenParam.substring(0, 8)}...`;
          let tokenSymbol = 'UNKNOWN';

          // Try to fetch token info if wallet is available
          if (window.ethereum) {
            try {
              const provider = new ethers.BrowserProvider(window.ethereum);
              const contract = new ethers.Contract(tokenParam, SecurityTokenABI.abi, provider);

              const [name, symbol] = await Promise.all([
                contract.name(),
                contract.symbol()
              ]);

              tokenName = name;
              tokenSymbol = symbol;
            } catch (error) {
              console.warn('Could not fetch token info for URL param, using placeholder:', error);
            }
          }

          const newToken: Token = {
            address: tokenParam,
            name: tokenName,
            symbol: tokenSymbol
          };
          initialTokens = addOrUpdateToken(newToken, initialTokens);
        }
      } else if (initialTokens.length > 0) {
        setSelectedToken(initialTokens[0].address);
      }

      setTokens(initialTokens);
      validateTokenUniqueness(initialTokens);
    };

    initializeTokens();
  }, [searchParams]);

  useEffect(() => {
    if (selectedToken && isConnected) {
      loadTransferControls();
    }
  }, [selectedToken, isConnected]);

  const loadTransferControls = async () => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      setMessage('');
      const provider = new ethers.BrowserProvider(window.ethereum);
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, provider);

      // First, try to get basic token info and update the token list if needed
      try {
        const [name, symbol] = await Promise.all([
          contract.name(),
          contract.symbol()
        ]);

        // Update the token in the list if it exists but has placeholder info
        setTokens(prevTokens => {
          const updatedToken: Token = {
            address: selectedToken,
            name: name,
            symbol: symbol
          };
          const newTokens = addOrUpdateToken(updatedToken, prevTokens);
          validateTokenUniqueness(newTokens);
          return newTokens;
        });
      } catch (error) {
        console.warn('Could not fetch token basic info:', error);
      }

      // Check if the contract supports the new transfer control functions
      try {
        const [
          conditionalTransfersEnabled,
          transferWhitelistEnabled,
          transferFeesEnabled,
          feeConfig
        ] = await Promise.all([
          contract.conditionalTransfersEnabled(),
          contract.transferWhitelistEnabled(),
          contract.transferFeesEnabled(),
          contract.getTransferFeeConfig()
        ]);

        // Try to get currency info (this might not be available in older contracts)
        let currency = 'USD'; // default
        try {
          currency = await contract.currency();
        } catch (error) {
          console.warn('Currency not available in contract, using default USD');
        }

        setControls({
          conditionalTransfersEnabled,
          transferWhitelistEnabled,
          transferFeesEnabled,
          transferFeePercentage: Number(feeConfig[0]),
          feeCollector: feeConfig[1],
          currency: currency
        });
      } catch (contractError) {
        console.warn('Contract does not support advanced transfer controls:', contractError);
        setMessage('This token contract does not support advanced transfer controls. Please upgrade the contract or select a different token.');
        setControls({
          conditionalTransfersEnabled: false,
          transferWhitelistEnabled: false,
          transferFeesEnabled: false,
          transferFeePercentage: 0,
          feeCollector: '',
          currency: 'USD'
        });
      }
    } catch (error) {
      console.error('Error loading transfer controls:', error);
      setMessage('Error connecting to token contract. Please check the token address and try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateConditionalTransfers = async (enabled: boolean) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setConditionalTransfers(enabled);
      await tx.wait();

      setControls(prev => ({ ...prev, conditionalTransfersEnabled: enabled }));
      setMessage(`Conditional transfers ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating conditional transfers:', error);
      setMessage('Error updating conditional transfers');
    } finally {
      setLoading(false);
    }
  };

  const updateTransferWhitelist = async (enabled: boolean) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setTransferWhitelist(enabled);
      await tx.wait();

      setControls(prev => ({ ...prev, transferWhitelistEnabled: enabled }));
      setMessage(`Transfer whitelisting ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating transfer whitelist:', error);
      setMessage('Error updating transfer whitelist');
    } finally {
      setLoading(false);
    }
  };

  const updateTransferFees = async (enabled: boolean, feePercentage: number, feeCollector: string) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setTransferFees(enabled, feePercentage, feeCollector);
      await tx.wait();

      setControls(prev => ({
        ...prev,
        transferFeesEnabled: enabled,
        transferFeePercentage: feePercentage,
        feeCollector: feeCollector
      }));
      setMessage(`Transfer fees ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating transfer fees:', error);
      setMessage('Error updating transfer fees');
    } finally {
      setLoading(false);
    }
  };

  const addCustomToken = async () => {
    if (!customTokenAddress || !ethers.isAddress(customTokenAddress)) {
      setMessage('Please enter a valid token address');
      return;
    }

    // Check if token already exists
    const existingToken = tokens.find(t => t.address.toLowerCase() === customTokenAddress.toLowerCase());
    if (existingToken) {
      setMessage(`Token ${existingToken.name} (${existingToken.symbol}) already exists in the list`);
      setSelectedToken(customTokenAddress);
      setCustomTokenAddress('');
      return;
    }

    setLoading(true);
    try {
      // Try to fetch token info
      let tokenName = `Token ${customTokenAddress.substring(0, 8)}...`;
      let tokenSymbol = 'UNKNOWN';

      if (window.ethereum) {
        try {
          const provider = new ethers.BrowserProvider(window.ethereum);
          const contract = new ethers.Contract(customTokenAddress, SecurityTokenABI.abi, provider);

          const [name, symbol] = await Promise.all([
            contract.name(),
            contract.symbol()
          ]);

          tokenName = name;
          tokenSymbol = symbol;
        } catch (error) {
          console.warn('Could not fetch token info, using placeholder:', error);
        }
      }

      const newToken: Token = {
        address: customTokenAddress,
        name: tokenName,
        symbol: tokenSymbol
      };

      const updatedTokens = addOrUpdateToken(newToken, tokens);
      setTokens(updatedTokens);
      validateTokenUniqueness(updatedTokens);
      setSelectedToken(customTokenAddress);
      setCustomTokenAddress('');
      setMessage(`Token ${tokenName} (${tokenSymbol}) added successfully`);
    } catch (error) {
      setMessage('Error adding token');
    } finally {
      setLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Transfer Controls</h1>
          <p className="text-gray-600 mb-6">Please connect your wallet to manage transfer controls.</p>

          <div className="space-y-3">
            {connectors.map((connector) => (
              <button
                key={connector.uid}
                onClick={() => connect({ connector })}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium"
              >
                Connect with {connector.name}
              </button>
            ))}
          </div>

          <div className="mt-6">
            <Link href="/" className="text-blue-600 hover:text-blue-800 text-sm">
              ← Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Navigation */}
        <div className="mb-6 flex items-center space-x-4">
          <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
            &larr; Back to Dashboard
          </Link>
          {selectedToken && (
            <Link
              href={`/tokens/${selectedToken}`}
              className="text-gray-600 hover:text-gray-800 flex items-center text-sm"
            >
              View Token Details
            </Link>
          )}
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Advanced Transfer Controls</h1>
                <p className="mt-1 text-sm text-gray-600">
                  Configure conditional transfers, transfer whitelisting, and transfer fees for your security tokens.
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  These controls are applied per token and work in addition to the basic whitelist and freeze functionality.
                </p>
              </div>

              {/* Wallet Info */}
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Connected: <span className="font-mono text-xs">{address?.substring(0, 6)}...{address?.substring(-4)}</span>
                </div>
                <button
                  onClick={() => disconnect()}
                  className="mt-1 text-xs text-red-600 hover:text-red-800"
                >
                  Disconnect
                </button>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Token Selection */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Token
              </label>
              <select
                value={selectedToken}
                onChange={(e) => setSelectedToken(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-2"
              >
                {tokens.map((token, index) => (
                  <option key={`${token.address}-${index}`} value={token.address}>
                    {token.name} ({token.symbol}) - {token.address.substring(0, 10)}...
                  </option>
                ))}
              </select>
              {selectedToken && (
                <div className="text-xs text-gray-600">
                  <p><strong>Full Address:</strong> {selectedToken}</p>
                  <p className="mt-1">Transfer controls are configured individually for each token.</p>
                </div>
              )}

              {/* Add Custom Token */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Add Custom Token Address
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={customTokenAddress}
                    onChange={(e) => setCustomTokenAddress(e.target.value)}
                    placeholder="0x..."
                    className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={addCustomToken}
                    disabled={loading}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    Add
                  </button>
                </div>
              </div>
            </div>

            {/* Status Message */}
            {message && (
              <div className="mb-6 p-4 rounded-md bg-blue-50 border border-blue-200">
                <p className="text-sm text-blue-800">{message}</p>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="mb-6 p-4 rounded-md bg-gray-50 border border-gray-200">
                <p className="text-sm text-gray-600">Processing transaction...</p>
              </div>
            )}

            {/* Current Status Summary */}
            {selectedToken && !loading && (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Current Transfer Control Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                  <div>
                    <span className="font-medium">Conditional Transfers:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.conditionalTransfersEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.conditionalTransfersEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Transfer Whitelist:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.transferWhitelistEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.transferWhitelistEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Transfer Fees:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.transferFeesEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.transferFeesEnabled ? `${controls.transferFeePercentage / 100}%` : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Transfer Controls */}
            <div className="space-y-8">
              {/* Conditional Transfers */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Conditional Transfers</h3>
                    <p className="text-sm text-gray-600">
                      Require approval for all transfers between investors
                    </p>
                  </div>
                  <button
                    onClick={() => updateConditionalTransfers(!controls.conditionalTransfersEnabled)}
                    disabled={loading}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      controls.conditionalTransfersEnabled
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    } disabled:opacity-50`}
                  >
                    {controls.conditionalTransfersEnabled ? 'Disable' : 'Enable'}
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  Status: {controls.conditionalTransfersEnabled ?
                    <span className="text-green-600 font-medium">Enabled</span> :
                    <span className="text-red-600 font-medium">Disabled</span>
                  }
                </div>
              </div>

              {/* Transfer Whitelisting */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Transfer Whitelisting</h3>
                    <p className="text-sm text-gray-600">
                      Only allow whitelisted addresses to initiate transfers
                    </p>
                  </div>
                  <button
                    onClick={() => updateTransferWhitelist(!controls.transferWhitelistEnabled)}
                    disabled={loading}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      controls.transferWhitelistEnabled
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    } disabled:opacity-50`}
                  >
                    {controls.transferWhitelistEnabled ? 'Disable' : 'Enable'}
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  Status: {controls.transferWhitelistEnabled ?
                    <span className="text-green-600 font-medium">Enabled</span> :
                    <span className="text-red-600 font-medium">Disabled</span>
                  }
                </div>
              </div>

              {/* Transfer Fees */}
              <TransferFeesSection
                controls={controls}
                onUpdate={updateTransferFees}
                loading={loading}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

interface TransferFeesSectionProps {
  controls: TransferControlsState;
  onUpdate: (enabled: boolean, feePercentage: number, feeCollector: string) => void;
  loading: boolean;
}

function TransferFeesSection({ controls, onUpdate, loading }: TransferFeesSectionProps) {
  const [feePercentage, setFeePercentage] = useState(controls.transferFeePercentage);
  const [feeCollector, setFeeCollector] = useState(controls.feeCollector);

  useEffect(() => {
    setFeePercentage(controls.transferFeePercentage);
    setFeeCollector(controls.feeCollector);
  }, [controls]);

  const handleUpdate = () => {
    onUpdate(!controls.transferFeesEnabled, feePercentage, feeCollector);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Transfer Fees</h3>
        <p className="text-sm text-gray-600">
          Collect a percentage fee on transfers to a designated collector wallet
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fee Percentage (basis points, 100 = 1%)
          </label>
          <input
            type="number"
            value={feePercentage}
            onChange={(e) => setFeePercentage(Number(e.target.value))}
            min="0"
            max="10000"
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="100"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Fee Collector Address
          </label>
          <input
            type="text"
            value={feeCollector}
            onChange={(e) => setFeeCollector(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="0x..."
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Status: {controls.transferFeesEnabled ?
              <span className="text-green-600 font-medium">Enabled ({controls.transferFeePercentage / 100}%)</span> :
              <span className="text-red-600 font-medium">Disabled</span>
            }
          </div>
          <button
            onClick={handleUpdate}
            disabled={loading}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              controls.transferFeesEnabled
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-green-600 text-white hover:bg-green-700'
            } disabled:opacity-50`}
          >
            {controls.transferFeesEnabled ? 'Disable' : 'Enable'}
          </button>
        </div>
      </div>
    </div>
  );
}
