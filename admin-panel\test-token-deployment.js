const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function main() {
  console.log('🧪 Testing Modular Token Deployment...\n');

  // Configuration
  const FACTORY_ADDRESS = process.env.NEXT_PUBLIC_AMOY_MODULAR_TOKEN_FACTORY_ADDRESS;
  const USER_PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
  const RPC_URL = process.env.AMOY_RPC_URL || "https://rpc-amoy.polygon.technology/";

  console.log('Factory address:', FACTORY_ADDRESS);
  console.log('RPC URL:', RPC_URL);

  try {
    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(RPC_URL);
    const userWallet = new ethers.Wallet(USER_PRIVATE_KEY, provider);
    
    console.log('User wallet address:', userWallet.address);
    
    // Check balance
    const balance = await provider.getBalance(userWallet.address);
    console.log('Wallet balance:', ethers.formatEther(balance), 'ETH');
    
    if (balance < ethers.parseEther('0.01')) {
      console.warn('⚠️ Low balance. You may need more ETH for gas fees.');
    }

    // Load the ModularTokenFactory ABI
    const abiPath = path.join(__dirname, 'src/contracts/ModularTokenFactory.json');
    const factoryABI = JSON.parse(fs.readFileSync(abiPath, 'utf8'));

    // Create factory contract instance
    const factory = new ethers.Contract(FACTORY_ADDRESS, factoryABI, userWallet);

    // Check deployer role
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const hasRole = await factory.hasRole(DEPLOYER_ROLE, userWallet.address);
    console.log('Has deployer role:', hasRole);

    if (!hasRole) {
      console.error('❌ User does not have DEPLOYER_ROLE');
      return;
    }

    // Test parameters
    const testParams = {
      name: 'Test_Token_' + Date.now(),
      symbol: 'TEST' + Math.floor(Math.random() * 1000),
      decimals: 0,
      maxSupply: ethers.parseUnits('1000000', 0),
      admin: userWallet.address,
      tokenPrice: '1.00 USD',
      bonusTiers: 'Early Bird: 10%, Standard: 5%, Late: 0%',
      tokenDetails: 'Test modular security token',
      tokenImageUrl: ''
    };

    console.log('\n📋 Test Parameters:');
    console.log('Name:', testParams.name);
    console.log('Symbol:', testParams.symbol);
    console.log('Decimals:', testParams.decimals);
    console.log('Max Supply:', testParams.maxSupply.toString());
    console.log('Admin:', testParams.admin);

    // Try gas estimation first
    console.log('\n⛽ Estimating gas...');
    try {
      const gasEstimate = await factory.deployToken.estimateGas(
        testParams.name,
        testParams.symbol,
        testParams.decimals,
        testParams.maxSupply,
        testParams.admin,
        testParams.tokenPrice,
        testParams.bonusTiers,
        testParams.tokenDetails,
        testParams.tokenImageUrl
      );
      console.log('✅ Gas estimate:', gasEstimate.toString());

      // Add 20% buffer
      const gasLimit = gasEstimate + (gasEstimate * BigInt(20) / BigInt(100));
      console.log('Gas limit with buffer:', gasLimit.toString());

      // Get current gas prices
      const feeData = await provider.getFeeData();
      console.log('Current gas prices:');
      console.log('- maxFeePerGas:', ethers.formatUnits(feeData.maxFeePerGas || 0, 'gwei'), 'gwei');
      console.log('- maxPriorityFeePerGas:', ethers.formatUnits(feeData.maxPriorityFeePerGas || 0, 'gwei'), 'gwei');

      // Prepare transaction options
      const txOptions = {
        gasLimit: gasLimit,
        maxFeePerGas: feeData.maxFeePerGas || ethers.parseUnits('120', 'gwei'),
        maxPriorityFeePerGas: feeData.maxPriorityFeePerGas || ethers.parseUnits('30', 'gwei')
      };

      console.log('\n🚀 Deploying token...');
      const tx = await factory.deployToken(
        testParams.name,
        testParams.symbol,
        testParams.decimals,
        testParams.maxSupply,
        testParams.admin,
        testParams.tokenPrice,
        testParams.bonusTiers,
        testParams.tokenDetails,
        testParams.tokenImageUrl,
        txOptions
      );

      console.log('✅ Transaction sent:', tx.hash);
      console.log('Waiting for confirmation...');

      const receipt = await tx.wait();
      console.log('✅ Transaction confirmed in block:', receipt.blockNumber);

      // Find the TokenDeployed event
      const event = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed.name === 'TokenDeployed';
        } catch {
          return false;
        }
      });

      if (event) {
        const parsedEvent = factory.interface.parseLog(event);
        const tokenAddress = parsedEvent.args.tokenAddress;
        console.log('🎉 Token deployed successfully!');
        console.log('Token address:', tokenAddress);
        console.log('View on PolygonScan:', `https://amoy.polygonscan.com/address/${tokenAddress}`);
      } else {
        console.log('⚠️ Token deployed but could not find deployment event');
      }

    } catch (gasError) {
      console.error('❌ Gas estimation failed:', gasError.message);
      console.log('Error details:', gasError);
      
      // Try with fixed gas
      console.log('\n🔄 Trying with fixed gas (3M)...');
      try {
        const tx = await factory.deployToken(
          testParams.name,
          testParams.symbol,
          testParams.decimals,
          testParams.maxSupply,
          testParams.admin,
          testParams.tokenPrice,
          testParams.bonusTiers,
          testParams.tokenDetails,
          testParams.tokenImageUrl,
          {
            gasLimit: 3000000,
            maxFeePerGas: ethers.parseUnits('120', 'gwei'),
            maxPriorityFeePerGas: ethers.parseUnits('30', 'gwei')
          }
        );

        console.log('✅ Fixed gas transaction sent:', tx.hash);
        const receipt = await tx.wait();
        console.log('✅ Transaction confirmed in block:', receipt.blockNumber);

      } catch (fixedGasError) {
        console.error('❌ Fixed gas also failed:', fixedGasError.message);
        console.log('Full error:', fixedGasError);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.data) {
      console.log('Error data:', error.data);
    }
    if (error.code) {
      console.log('Error code:', error.code);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
