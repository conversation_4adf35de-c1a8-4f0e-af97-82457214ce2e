'use client';

import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Import ABIs
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import UpgradeManagerABI from '@/contracts/UpgradeManager.json';

// Contract addresses
const SECURITY_TOKEN_CORE_ADDRESS = process.env.NEXT_PUBLIC_AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.NEXT_PUBLIC_AMOY_UPGRADE_MANAGER_ADDRESS;

interface DeploymentStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
  duration?: number;
}

interface UpgradeDeployment {
  id: string;
  name: string;
  description: string;
  implementationAddress: string;
  steps: DeploymentStep[];
  status: 'draft' | 'scheduled' | 'executing' | 'completed' | 'failed';
  scheduledTime?: Date;
  completedTime?: Date;
  upgradeId?: string;
}

export default function UpgradeDeploymentPage() {
  const [provider, setProvider] = useState<ethers.BrowserProvider | null>(null);
  const [signer, setSigner] = useState<ethers.JsonRpcSigner | null>(null);
  const [deployments, setDeployments] = useState<UpgradeDeployment[]>([]);
  const [activeDeployment, setActiveDeployment] = useState<UpgradeDeployment | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);

  // Form state for new deployment
  const [newDeployment, setNewDeployment] = useState({
    name: '',
    description: '',
    implementationAddress: '',
    isEmergency: false
  });

  // Initialize provider
  const initializeProvider = async () => {
    try {
      if (typeof window !== 'undefined' && window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send('eth_requestAccounts', []);
        const signer = await provider.getSigner();
        setProvider(provider);
        setSigner(signer);
      }
    } catch (error) {
      console.error('Failed to initialize provider:', error);
    }
  };

  // Create deployment steps
  const createDeploymentSteps = (isEmergency: boolean): DeploymentStep[] => {
    const baseSteps: DeploymentStep[] = [
      {
        id: 'validate',
        name: 'Validate Implementation',
        description: 'Verify the new implementation contract',
        status: 'pending'
      },
      {
        id: 'permissions',
        name: 'Check Permissions',
        description: 'Verify deployer has required permissions',
        status: 'pending'
      }
    ];

    if (isEmergency) {
      baseSteps.push(
        {
          id: 'emergency-check',
          name: 'Emergency Mode Check',
          description: 'Verify emergency mode is active',
          status: 'pending'
        },
        {
          id: 'emergency-upgrade',
          name: 'Execute Emergency Upgrade',
          description: 'Perform immediate emergency upgrade',
          status: 'pending'
        }
      );
    } else {
      baseSteps.push(
        {
          id: 'schedule',
          name: 'Schedule Upgrade',
          description: 'Schedule upgrade with timelock',
          status: 'pending'
        },
        {
          id: 'timelock-wait',
          name: 'Timelock Wait',
          description: 'Wait for timelock period to expire',
          status: 'pending'
        },
        {
          id: 'execute',
          name: 'Execute Upgrade',
          description: 'Execute the scheduled upgrade',
          status: 'pending'
        }
      );
    }

    baseSteps.push({
      id: 'verify',
      name: 'Verify Upgrade',
      description: 'Verify the upgrade was successful',
      status: 'pending'
    });

    return baseSteps;
  };

  // Create new deployment
  const createDeployment = () => {
    if (!newDeployment.name || !newDeployment.implementationAddress) {
      alert('Please fill in all required fields');
      return;
    }

    const deployment: UpgradeDeployment = {
      id: Date.now().toString(),
      name: newDeployment.name,
      description: newDeployment.description,
      implementationAddress: newDeployment.implementationAddress,
      steps: createDeploymentSteps(newDeployment.isEmergency),
      status: 'draft'
    };

    setDeployments(prev => [deployment, ...prev]);
    setNewDeployment({
      name: '',
      description: '',
      implementationAddress: '',
      isEmergency: false
    });
  };

  // Update deployment step
  const updateDeploymentStep = (deploymentId: string, stepId: string, updates: Partial<DeploymentStep>) => {
    setDeployments(prev => prev.map(deployment => {
      if (deployment.id === deploymentId) {
        return {
          ...deployment,
          steps: deployment.steps.map(step => 
            step.id === stepId ? { ...step, ...updates } : step
          )
        };
      }
      return deployment;
    }));
  };

  // Update deployment status
  const updateDeploymentStatus = (deploymentId: string, status: UpgradeDeployment['status'], completedTime?: Date) => {
    setDeployments(prev => prev.map(deployment => 
      deployment.id === deploymentId 
        ? { ...deployment, status, ...(completedTime && { completedTime }) }
        : deployment
    ));
  };

  // Execute deployment step
  const executeStep = async (deployment: UpgradeDeployment, step: DeploymentStep) => {
    if (!provider || !signer) throw new Error('Provider not initialized');

    const startTime = Date.now();
    updateDeploymentStep(deployment.id, step.id, { status: 'running' });

    try {
      const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, signer);
      const tokenCore = new ethers.Contract(SECURITY_TOKEN_CORE_ADDRESS!, SecurityTokenCoreABI, provider);

      switch (step.id) {
        case 'validate':
          // Validate implementation contract
          const code = await provider.getCode(deployment.implementationAddress);
          if (code === '0x') {
            throw new Error('No contract found at implementation address');
          }
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { codeSize: code.length }
          });
          break;

        case 'permissions':
          // Check permissions
          const signerAddress = await signer.getAddress();
          const hasUpgradeRole = await upgradeManager.hasRole(
            await upgradeManager.UPGRADER_ROLE(),
            signerAddress
          );
          if (!hasUpgradeRole) {
            throw new Error('Signer does not have UPGRADER_ROLE');
          }
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { hasUpgradeRole, signerAddress }
          });
          break;

        case 'emergency-check':
          // Check emergency mode
          const isEmergencyActive = await upgradeManager.isEmergencyModeActive();
          if (!isEmergencyActive) {
            throw new Error('Emergency mode is not active');
          }
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { isEmergencyActive }
          });
          break;

        case 'schedule':
          // Schedule upgrade
          const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
          const tx = await upgradeManager.scheduleUpgrade(
            SECURITY_TOKEN_CORE_ID,
            deployment.implementationAddress,
            deployment.description
          );
          const receipt = await tx.wait();
          
          // Extract upgrade ID from events
          const upgradeScheduledEvent = receipt.logs.find((log: any) => {
            try {
              const parsed = upgradeManager.interface.parseLog(log);
              return parsed?.name === 'UpgradeScheduled';
            } catch {
              return false;
            }
          });

          let upgradeId = '';
          if (upgradeScheduledEvent) {
            const parsed = upgradeManager.interface.parseLog(upgradeScheduledEvent);
            upgradeId = parsed?.args?.upgradeId || '';
          }

          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { transactionHash: tx.hash, upgradeId }
          });

          // Update deployment with upgrade ID
          setDeployments(prev => prev.map(d => 
            d.id === deployment.id ? { ...d, upgradeId, status: 'scheduled' } : d
          ));
          break;

        case 'timelock-wait':
          // This step would typically be handled by a background process
          // For now, we'll just mark it as completed if enough time has passed
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { message: 'Timelock period simulation completed' }
          });
          break;

        case 'execute':
          // Execute upgrade (would need the upgrade ID from schedule step)
          if (!deployment.upgradeId) {
            throw new Error('No upgrade ID found');
          }
          // This would execute the actual upgrade
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { message: 'Upgrade execution simulated' }
          });
          break;

        case 'emergency-upgrade':
          // Execute emergency upgrade
          const emergencyTx = await upgradeManager.emergencyUpgrade(
            ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE")),
            deployment.implementationAddress,
            deployment.description
          );
          const emergencyReceipt = await emergencyTx.wait();
          
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { transactionHash: emergencyTx.hash }
          });
          break;

        case 'verify':
          // Verify upgrade
          const currentVersion = await tokenCore.version();
          updateDeploymentStep(deployment.id, step.id, {
            status: 'completed',
            duration: Date.now() - startTime,
            result: { currentVersion }
          });
          break;

        default:
          throw new Error(`Unknown step: ${step.id}`);
      }
    } catch (error: any) {
      updateDeploymentStep(deployment.id, step.id, {
        status: 'failed',
        duration: Date.now() - startTime,
        error: error.message
      });
      throw error;
    }
  };

  // Execute deployment
  const executeDeployment = async (deployment: UpgradeDeployment) => {
    if (!provider || !signer) {
      alert('Please connect your wallet first');
      return;
    }

    setIsExecuting(true);
    setActiveDeployment(deployment);
    updateDeploymentStatus(deployment.id, 'executing');

    try {
      for (const step of deployment.steps) {
        await executeStep(deployment, step);
        
        // Add delay between steps for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      updateDeploymentStatus(deployment.id, 'completed', new Date());
    } catch (error: any) {
      console.error('Deployment failed:', error);
      updateDeploymentStatus(deployment.id, 'failed');
    } finally {
      setIsExecuting(false);
      setActiveDeployment(null);
    }
  };

  useEffect(() => {
    initializeProvider();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Upgrade Deployment Automation</h1>
          <p className="text-gray-600">
            Automated deployment and testing of contract upgrades
          </p>
        </div>
        {!provider && (
          <button
            onClick={initializeProvider}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Connect Wallet
          </button>
        )}
      </div>

      {/* Create New Deployment */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Create New Deployment</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Deployment Name *
            </label>
            <input
              type="text"
              value={newDeployment.name}
              onChange={(e) => setNewDeployment(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Security Token v2.1.0"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Implementation Address *
            </label>
            <input
              type="text"
              value={newDeployment.implementationAddress}
              onChange={(e) => setNewDeployment(prev => ({ ...prev, implementationAddress: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="0x..."
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={newDeployment.description}
              onChange={(e) => setNewDeployment(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Describe the changes in this upgrade..."
            />
          </div>
          
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isEmergency"
              checked={newDeployment.isEmergency}
              onChange={(e) => setNewDeployment(prev => ({ ...prev, isEmergency: e.target.checked }))}
              className="mr-2"
            />
            <label htmlFor="isEmergency" className="text-sm text-gray-700">
              Emergency Upgrade (bypasses timelock)
            </label>
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={createDeployment}
            disabled={!newDeployment.name || !newDeployment.implementationAddress}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            Create Deployment
          </button>
        </div>
      </div>

      {/* Deployments List */}
      <div className="space-y-4">
        {deployments.map((deployment) => (
          <div key={deployment.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold">{deployment.name}</h3>
                <p className="text-sm text-gray-600">{deployment.description}</p>
                <p className="text-xs text-gray-400">
                  Implementation: {deployment.implementationAddress.slice(0, 10)}...{deployment.implementationAddress.slice(-8)}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  deployment.status === 'completed' ? 'bg-green-100 text-green-800' :
                  deployment.status === 'executing' ? 'bg-blue-100 text-blue-800' :
                  deployment.status === 'failed' ? 'bg-red-100 text-red-800' :
                  deployment.status === 'scheduled' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {deployment.status}
                </span>
                {deployment.status === 'draft' && (
                  <button
                    onClick={() => executeDeployment(deployment)}
                    disabled={isExecuting}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm disabled:opacity-50"
                  >
                    {isExecuting ? 'Executing...' : 'Execute'}
                  </button>
                )}
              </div>
            </div>

            {/* Deployment Steps */}
            <div className="space-y-2">
              {deployment.steps.map((step, index) => (
                <div key={step.id} className="flex items-center gap-3 p-2 rounded border">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    step.status === 'completed' ? 'bg-green-500 text-white' :
                    step.status === 'running' ? 'bg-blue-500 text-white animate-pulse' :
                    step.status === 'failed' ? 'bg-red-500 text-white' :
                    'bg-gray-300 text-gray-600'
                  }`}>
                    {step.status === 'completed' ? '✓' :
                     step.status === 'failed' ? '✗' :
                     step.status === 'running' ? '⟳' :
                     index + 1}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{step.name}</div>
                    <div className="text-xs text-gray-600">{step.description}</div>
                    {step.duration && (
                      <div className="text-xs text-gray-400">Duration: {step.duration}ms</div>
                    )}
                    {step.error && (
                      <div className="text-xs text-red-600">Error: {step.error}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {deployments.length === 0 && (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <p className="text-gray-500">No deployments created yet</p>
          <p className="text-sm text-gray-400">Create your first deployment above</p>
        </div>
      )}
    </div>
  );
}
