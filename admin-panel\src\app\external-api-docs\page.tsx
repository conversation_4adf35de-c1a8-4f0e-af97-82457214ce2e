'use client';

import React, { useState } from 'react';

interface APIEndpoint {
  method: string;
  path: string;
  description: string;
  parameters?: { name: string; type: string; required: boolean; description: string }[];
  response: any;
  example: string;
}

export default function ExternalAPIDocsPage() {
  const [selectedEndpoint, setSelectedEndpoint] = useState<string>('tokens');
  const [activeTab, setActiveTab] = useState<string>('overview');

  const apiEndpoints: { [key: string]: APIEndpoint } = {
    tokens: {
      method: 'GET',
      path: '/api/external/tokens',
      description: 'Retrieve information about deployed security tokens',
      parameters: [
        {
          name: 'address',
          type: 'string',
          required: false,
          description: 'Specific token contract address to retrieve'
        },
        {
          name: 'format',
          type: 'string',
          required: false,
          description: 'Response format (json, xml) - default: json'
        }
      ],
      response: {
        success: true,
        data: {
          tokens: [
            {
              address: '******************************************',
              name: 'Augment Security Token',
              symbol: 'AST',
              version: '4.0.0',
              totalSupply: '1000000',
              maxSupply: '*********',
              decimals: 0,
              price: '7.50 USD',
              bonusTiers: 'Early: 45%, Standard: 30%, Late: 15%',
              isPaused: false,
              totalInvestors: 25,
              issuer: {
                name: 'Augment Technologies',
                address: '0x56f3726C92B8B92a6ab71983886F91718540d888',
                website: 'https://augment.com',
                description: 'Leading blockchain technology company'
              },
              metadata: {
                deployedAt: '2024-01-15T10:30:00Z',
                lastUpdated: '2024-01-20T14:45:00Z',
                network: 'Polygon Amoy Testnet',
                contractType: 'ERC-3643 Security Token'
              },
              compliance: {
                kycRequired: true,
                whitelistEnabled: true,
                claimsRequired: ['10101010000001', '10101010000004']
              },
              statistics: {
                totalTransactions: 150,
                averageTransactionValue: '5000',
                largestTransaction: '50000',
                activeInvestors: 20
              }
            }
          ],
          totalCount: 1,
          summary: {
            totalSupply: '1000000',
            totalInvestors: 25,
            activeTokens: 1,
            pausedTokens: 0
          }
        },
        timestamp: '2024-01-20T15:00:00Z',
        version: '1.0.0'
      },
      example: `curl -X GET "https://your-domain.com/api/external/tokens" \\
  -H "Content-Type: application/json"`
    },
    investors: {
      method: 'GET',
      path: '/api/external/investors',
      description: 'Retrieve investor information and portfolio data',
      parameters: [
        {
          name: 'wallet',
          type: 'string',
          required: false,
          description: 'Specific wallet address to retrieve'
        },
        {
          name: 'email',
          type: 'string',
          required: false,
          description: 'Specific email address to retrieve'
        },
        {
          name: 'stats',
          type: 'boolean',
          required: false,
          description: 'Include aggregated statistics - default: false'
        }
      ],
      response: {
        success: true,
        data: {
          investors: [
            {
              id: 'inv_123',
              walletAddress: '******************************************',
              email: '<EMAIL>',
              kycStatus: 'approved',
              whitelistStatus: {
                '******************************************': {
                  approved: true,
                  approvedAt: '2024-01-15T12:00:00Z',
                  approvedBy: 'admin'
                }
              },
              qualificationStatus: 'completed',
              tokens: [
                {
                  address: '******************************************',
                  name: 'Augment Security Token',
                  symbol: 'AST',
                  balance: '1000',
                  frozenBalance: '0',
                  totalInvested: '7500',
                  averagePrice: '7.50',
                  firstPurchase: '2024-01-15T14:30:00Z',
                  lastTransaction: '2024-01-18T09:15:00Z'
                }
              ],
              profile: {
                firstName: 'John',
                lastName: 'Doe',
                country: 'United States',
                investorType: 'individual',
                accreditationStatus: 'accredited'
              },
              compliance: {
                claimsIssued: ['10101010000001'],
                agreementsAccepted: ['******************************************'],
                lastKycUpdate: '2024-01-15T11:00:00Z',
                riskAssessment: 'low'
              },
              statistics: {
                totalInvestments: 1,
                totalValue: '7500',
                portfolioTokens: 1,
                joinedDate: '2024-01-15T10:00:00Z',
                lastActivity: '2024-01-18T09:15:00Z'
              }
            }
          ],
          totalCount: 1,
          statistics: {
            totalInvestors: 25,
            kycApproved: 20,
            kycPending: 5,
            qualificationCompleted: 18,
            totalInvestmentValue: '187500',
            averageInvestmentValue: '7500',
            investorsByCountry: {
              'United States': 15,
              'Canada': 5,
              'United Kingdom': 3,
              'Germany': 2
            },
            investorsByType: {
              'individual': 20,
              'institutional': 5
            }
          }
        },
        timestamp: '2024-01-20T15:00:00Z',
        version: '1.0.0'
      },
      example: `curl -X GET "https://your-domain.com/api/external/investors?stats=true" \\
  -H "Content-Type: application/json"`
    }
  };

  const currentEndpoint = apiEndpoints[selectedEndpoint];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">External API Documentation</h1>
        <p className="text-xl text-gray-600 mb-8">
          Comprehensive API for third-party integrations with the ERC-3643 Security Token System
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
          <p className="text-blue-800">
            <strong>Base URL:</strong> <code className="bg-blue-100 px-2 py-1 rounded">https://your-domain.com</code>
          </p>
          <p className="text-blue-800 mt-2">
            <strong>Version:</strong> 1.0.0 | <strong>Format:</strong> JSON | <strong>CORS:</strong> Enabled | <strong>Auth:</strong> Bearer Token
          </p>
          <p className="text-blue-800 mt-2">
            <strong>Rate Limit:</strong> 100 requests/minute | <strong>API Keys:</strong> <a href="/api-keys" className="underline">Manage here</a>
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-center">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {['overview', 'authentication', 'endpoints', 'examples'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-md font-medium capitalize ${
                activeTab === tab
                  ? 'bg-white text-blue-600 shadow'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-2xl font-bold mb-6">API Overview</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">🏢 Token Information API</h3>
              <p className="text-gray-600 mb-4">
                Access comprehensive information about deployed security tokens including metadata, 
                compliance status, and real-time statistics.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Token details and metadata</li>
                <li>• Supply and pricing information</li>
                <li>• Compliance and regulatory status</li>
                <li>• Real-time statistics</li>
              </ul>
            </div>
            
            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">👥 Investor Data API</h3>
              <p className="text-gray-600 mb-4">
                Retrieve investor profiles, portfolio information, and compliance status 
                for comprehensive investor management.
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Investor profiles and KYC status</li>
                <li>• Portfolio and investment history</li>
                <li>• Whitelist and compliance data</li>
                <li>• Aggregated statistics</li>
              </ul>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Key Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl mb-2">🔒</div>
                <h4 className="font-medium">Secure Access</h4>
                <p className="text-sm text-gray-600">CORS-enabled with optional authentication</p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">⚡</div>
                <h4 className="font-medium">Real-time Data</h4>
                <p className="text-sm text-gray-600">Live blockchain data with caching</p>
              </div>
              <div className="text-center">
                <div className="text-2xl mb-2">📊</div>
                <h4 className="font-medium">Rich Analytics</h4>
                <p className="text-sm text-gray-600">Comprehensive statistics and insights</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'authentication' && (
        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-2xl font-bold mb-6">Authentication</h2>

          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">🔑 API Key Authentication</h3>
              <p className="text-blue-700 mb-3">
                The external API uses Bearer token authentication with API keys for secure access:
              </p>
              <ul className="text-blue-700 space-y-1">
                <li>• <strong>Method:</strong> Bearer Token</li>
                <li>• <strong>Header:</strong> <code className="bg-blue-100 px-2 py-1 rounded">Authorization: Bearer &lt;api_key&gt;</code></li>
                <li>• <strong>Rate Limit:</strong> 100 requests per minute per API key</li>
                <li>• <strong>Permissions:</strong> read, write, admin</li>
              </ul>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-green-800 mb-2">🚀 Getting Started</h3>
              <div className="text-green-700 space-y-2">
                <p>1. <a href="/api-keys" className="underline font-medium">Generate an API key</a> in the admin panel</p>
                <p>2. Include the API key in your requests:</p>
                <pre className="bg-green-100 p-2 rounded text-sm mt-2">
curl -H "Authorization: Bearer your_api_key_here" \\
  https://your-domain.com/api/external/tokens
                </pre>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">📊 Public vs Authenticated Access</h3>
              <div className="text-yellow-700 space-y-2">
                <p><strong>Public Access:</strong> Basic token information without authentication</p>
                <p><strong>Authenticated Access:</strong> Full data including detailed statistics</p>
                <p><strong>Detailed Data:</strong> Add <code className="bg-yellow-100 px-1 rounded">?detailed=true</code> parameter (requires authentication)</p>
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">🔒 CORS Support</h3>
              <p className="text-gray-700 mb-3">
                Cross-Origin Resource Sharing (CORS) is enabled for all external API endpoints:
              </p>
              <ul className="text-gray-700 space-y-1">
                <li>• <code>Access-Control-Allow-Origin: *</code></li>
                <li>• <code>Access-Control-Allow-Methods: GET, POST, OPTIONS</code></li>
                <li>• <code>Access-Control-Allow-Headers: Content-Type, Authorization</code></li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'endpoints' && (
        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-2xl font-bold mb-6">API Endpoints</h2>
          
          {/* Endpoint Selector */}
          <div className="flex space-x-2 mb-6">
            {Object.keys(apiEndpoints).map((key) => (
              <button
                key={key}
                onClick={() => setSelectedEndpoint(key)}
                className={`px-4 py-2 rounded-md font-medium capitalize ${
                  selectedEndpoint === key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {key}
              </button>
            ))}
          </div>

          {/* Endpoint Details */}
          <div className="border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <span className={`px-3 py-1 rounded text-sm font-medium ${
                currentEndpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                currentEndpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {currentEndpoint.method}
              </span>
              <code className="text-lg font-mono bg-gray-100 px-3 py-1 rounded">
                {currentEndpoint.path}
              </code>
            </div>
            
            <p className="text-gray-600 mb-6">{currentEndpoint.description}</p>

            {/* Parameters */}
            {currentEndpoint.parameters && (
              <div className="mb-6">
                <h4 className="text-lg font-semibold mb-3">Parameters</h4>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-300 px-4 py-2 text-left">Name</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Type</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Required</th>
                        <th className="border border-gray-300 px-4 py-2 text-left">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      {currentEndpoint.parameters.map((param, index) => (
                        <tr key={index}>
                          <td className="border border-gray-300 px-4 py-2 font-mono text-sm">
                            {param.name}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {param.type}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {param.required ? '✅ Yes' : '❌ No'}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 text-sm">
                            {param.description}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Response */}
            <div className="mb-6">
              <h4 className="text-lg font-semibold mb-3">Response</h4>
              <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm">
                {JSON.stringify(currentEndpoint.response, null, 2)}
              </pre>
            </div>

            {/* Example */}
            <div>
              <h4 className="text-lg font-semibold mb-3">Example Request</h4>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
                {currentEndpoint.example}
              </pre>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'examples' && (
        <div className="bg-white rounded-lg shadow p-8">
          <h2 className="text-2xl font-bold mb-6">Integration Examples</h2>
          
          <div className="space-y-8">
            {/* JavaScript Example */}
            <div>
              <h3 className="text-xl font-semibold mb-4">JavaScript / Node.js</h3>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`// Fetch all tokens
const response = await fetch('https://your-domain.com/api/external/tokens');
const data = await response.json();

if (data.success) {
  console.log('Total tokens:', data.data.totalCount);
  data.data.tokens.forEach(token => {
    console.log(\`\${token.name} (\${token.symbol}): \${token.totalSupply} tokens\`);
  });
}

// Fetch specific investor
const investorResponse = await fetch(
  'https://your-domain.com/api/external/investors?wallet=******************************************&stats=true'
);
const investorData = await investorResponse.json();

if (investorData.success) {
  const investor = investorData.data.investors[0];
  console.log(\`Investor: \${investor.profile.firstName} \${investor.profile.lastName}\`);
  console.log(\`KYC Status: \${investor.kycStatus}\`);
  console.log(\`Total Investment: $\${investor.statistics.totalValue}\`);
}`}
              </pre>
            </div>

            {/* Python Example */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Python</h3>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`import requests
import json

# Fetch all tokens
response = requests.get('https://your-domain.com/api/external/tokens')
data = response.json()

if data['success']:
    print(f"Total tokens: {data['data']['totalCount']}")
    for token in data['data']['tokens']:
        print(f"{token['name']} ({token['symbol']}): {token['totalSupply']} tokens")

# Fetch investor with statistics
investor_response = requests.get(
    'https://your-domain.com/api/external/investors',
    params={'wallet': '******************************************', 'stats': 'true'}
)
investor_data = investor_response.json()

if investor_data['success']:
    investor = investor_data['data']['investors'][0]
    print(f"Investor: {investor['profile']['firstName']} {investor['profile']['lastName']}")
    print(f"KYC Status: {investor['kycStatus']}")
    print(f"Total Investment: ${investor['statistics']['totalValue']}")`}
              </pre>
            </div>

            {/* cURL Example */}
            <div>
              <h3 className="text-xl font-semibold mb-4">cURL</h3>
              <pre className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`# Get all tokens
curl -X GET "https://your-domain.com/api/external/tokens" \\
  -H "Content-Type: application/json"

# Get specific token
curl -X GET "https://your-domain.com/api/external/tokens?address=******************************************" \\
  -H "Content-Type: application/json"

# Get investor data with statistics
curl -X GET "https://your-domain.com/api/external/investors?stats=true" \\
  -H "Content-Type: application/json"

# Get specific investor by wallet
curl -X GET "https://your-domain.com/api/external/investors?wallet=******************************************" \\
  -H "Content-Type: application/json"`}
              </pre>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
