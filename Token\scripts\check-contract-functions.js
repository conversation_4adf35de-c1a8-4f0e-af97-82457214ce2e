const { ethers } = require("hardhat");

// Token address to check
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 Checking available functions on token:", TOKEN_ADDRESS);

  try {
    // Try different ABIs to see what works
    
    // 1. Try with minimal ABI
    console.log("\n📋 Testing basic ERC20 functions...");
    const basicABI = [
      "function name() view returns (string)",
      "function symbol() view returns (string)",
      "function decimals() view returns (uint8)",
      "function totalSupply() view returns (uint256)",
      "function balanceOf(address) view returns (uint256)"
    ];
    
    const basicContract = new ethers.Contract(TOKEN_ADDRESS, basicABI, deployer);
    
    try {
      const name = await basicContract.name();
      const symbol = await basicContract.symbol();
      const decimals = await basicContract.decimals();
      const totalSupply = await basicContract.totalSupply();
      
      console.log(`✅ Name: ${name}`);
      console.log(`✅ Symbol: ${symbol}`);
      console.log(`✅ Decimals: ${decimals}`);
      console.log(`✅ Total Supply: ${totalSupply.toString()}`);
    } catch (basicError) {
      console.log("❌ Basic ERC20 functions failed:", basicError.message);
    }

    // 2. Try SecurityToken functions
    console.log("\n📋 Testing SecurityToken functions...");
    const securityTokenABI = [
      "function maxSupply() view returns (uint256)",
      "function paused() view returns (bool)",
      "function hasRole(bytes32, address) view returns (bool)",
      "function tokenPrice() view returns (string)",
      "function bonusTiers() view returns (string)",
      "function tokenDetails() view returns (string)"
    ];
    
    const securityContract = new ethers.Contract(TOKEN_ADDRESS, securityTokenABI, deployer);
    
    try {
      const maxSupply = await securityContract.maxSupply();
      const paused = await securityContract.paused();
      console.log(`✅ Max Supply: ${maxSupply.toString()}`);
      console.log(`✅ Paused: ${paused}`);
      
      // Try individual metadata functions
      try {
        const tokenPrice = await securityContract.tokenPrice();
        console.log(`✅ Token Price (individual): "${tokenPrice}"`);
      } catch (priceError) {
        console.log("❌ tokenPrice() function not available:", priceError.message);
      }
      
      try {
        const bonusTiers = await securityContract.bonusTiers();
        console.log(`✅ Bonus Tiers (individual): "${bonusTiers}"`);
      } catch (tiersError) {
        console.log("❌ bonusTiers() function not available:", tiersError.message);
      }
      
    } catch (securityError) {
      console.log("❌ SecurityToken functions failed:", securityError.message);
    }

    // 3. Try SecurityTokenCore functions
    console.log("\n📋 Testing SecurityTokenCore functions...");
    const coreABI = [
      "function getTokenMetadata() view returns (string, string, string, string)",
      "function updateTokenPrice(string) external",
      "function updateTokenMetadata(string, string, string) external",
      "function version() view returns (string)"
    ];
    
    const coreContract = new ethers.Contract(TOKEN_ADDRESS, coreABI, deployer);
    
    try {
      const metadata = await coreContract.getTokenMetadata();
      console.log(`✅ Metadata: Price="${metadata[0]}", Tiers="${metadata[1]}", Details="${metadata[2]}", Image="${metadata[3]}"`);
    } catch (metadataError) {
      console.log("❌ getTokenMetadata() failed:", metadataError.message);
    }
    
    try {
      const version = await coreContract.version();
      console.log(`✅ Version: ${version}`);
    } catch (versionError) {
      console.log("❌ version() function not available:", versionError.message);
    }

    // 4. Test update functions with different approaches
    console.log("\n📋 Testing update functions...");

    // Check function selectors in bytecode
    const contractCode = await deployer.provider.getCode(TOKEN_ADDRESS);
    const functionSelectors = {
      "updateTokenPrice": "0xfba187d9",
      "updateTokenMetadata": "0x062f601b",
      "updateBonusTiers": "0x8b7afe2e"
    };

    console.log("Function selector analysis:");
    for (const [funcName, selector] of Object.entries(functionSelectors)) {
      const exists = contractCode.includes(selector.slice(2));
      console.log(`${exists ? '✅' : '❌'} ${funcName}: ${selector} ${exists ? 'FOUND' : 'NOT FOUND'}`);
    }

    // Test updateTokenMetadata specifically
    console.log("\n🔍 Testing updateTokenMetadata function...");
    try {
      await coreContract.updateTokenMetadata.staticCall("Test Price", "Test Tiers", "Test Details");
      console.log("✅ updateTokenMetadata static call succeeded");
    } catch (updateMetadataError) {
      console.log("❌ updateTokenMetadata static call failed:", updateMetadataError.message);

      // Check admin role
      const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
      try {
        const hasRole = await securityContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
        console.log(`   Admin role check: ${hasRole}`);

        if (!hasRole) {
          console.log("   ❌ This might be a role permission issue");
        } else {
          console.log("   ✅ User has admin role, so it's not a permission issue");
          console.log("   🔍 This suggests the function doesn't exist or has different signature");
        }
      } catch (roleError) {
        console.log("   ❌ Could not check role:", roleError.message);
      }
    }

    // Try to find what update functions actually exist
    console.log("\n🔍 Trying alternative function signatures...");

    // Test if it's the old SecurityToken contract instead
    const oldSecurityTokenABI = [
      "function updateTokenPrice(string) external",
      "function updateBonusTiers(string) external",
      "function updateTokenDetails(string) external"
    ];

    const oldContract = new ethers.Contract(TOKEN_ADDRESS, oldSecurityTokenABI, deployer);

    try {
      await oldContract.updateTokenPrice.staticCall("Test Price");
      console.log("✅ OLD updateTokenPrice static call succeeded - this is SecurityToken, not SecurityTokenCore!");
    } catch (oldError) {
      console.log("❌ OLD updateTokenPrice also failed:", oldError.message);
    }

  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
