{"network": "amoy", "chainId": "80002", "factoryAddress": "0x010c6932B830ae6A80a1Ac517ca7B5216b294C42", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0xee89863a72813843ffb35947e3d88ea15da5478f80c6abade09c67748f2e438a", "timestamp": "2025-06-22T15:47:54.217Z", "contractType": "SecurityTokenFactoryFinal", "architecture": "Final with ALL Features", "securityLevel": "MAXIMUM", "features": {"securityTokenFinal": true, "forcedTransfer": true, "freezeTokens": true, "unfreezeTokens": true, "availableBalanceOf": true, "transferManagerRole": true, "securityLevels": true, "functionPausing": true, "agreementTracking": true, "fullERC3643Compliance": true, "allSecurityAuditFixes": true, "proxyPattern": true, "bypassesSizeLimits": true, "adminPanelIntegration": true, "kycSupport": true}}