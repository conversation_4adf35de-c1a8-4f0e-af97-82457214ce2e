import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Add the historical freeze operation that we know happened
    const historicalFreeze = await prisma.freezeOperation.create({
      data: {
        tokenAddress: '0xf5f155eda46107a9dadc86804b43f006d7df67ff',
        userAddress: '0x1b26f8d2cbdf72265bac3d427e333b8eee6ab9b2',
        amount: 7,
        operation: 'freeze',
        txHash: '0x16310fc2c272d8895f6fce755a8e0ef0d66530db712a6682a7cd482ce3723d7e', // The actual transaction hash
        timestamp: new Date('2024-12-18T12:00:00Z') // Approximate time
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Historical freeze data seeded successfully',
      operation: historicalFreeze
    });

  } catch (error: any) {
    console.error('Error seeding freeze data:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed freeze data',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
