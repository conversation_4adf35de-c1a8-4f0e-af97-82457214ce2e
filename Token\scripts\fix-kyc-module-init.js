const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 Fixing KYC Claims Module initialization...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Fixing with account:", deployer.address);

  const KYC_CLAIMS_MODULE_ADDRESS = "******************************************";
  const CLAIM_REGISTRY_ADDRESS = "******************************************";
  const NEW_TOKEN_ADDRESS = "******************************************";

  try {
    // Get the KYC Claims Module
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    const kycModule = KYCClaimsModule.attach(KYC_CLAIMS_MODULE_ADDRESS);

    console.log("📋 Contract addresses:");
    console.log(`- KYCClaimsModule: ${KYC_CLAIMS_MODULE_ADDRESS}`);
    console.log(`- ClaimRegistry: ${CLAIM_REGISTRY_ADDRESS}`);
    console.log(`- New Token: ${NEW_TOKEN_ADDRESS}`);

    // Check current state
    console.log("\n🔍 Checking current module state...");
    try {
      const claimRegistry = await kycModule.claimRegistry();
      console.log(`Current claim registry: ${claimRegistry}`);
      
      if (claimRegistry === ethers.ZeroAddress) {
        console.log("⚠️ Module not initialized! Initializing now...");
        
        const initTx = await kycModule.initialize(
          CLAIM_REGISTRY_ADDRESS,
          deployer.address
        );
        await initTx.wait();
        console.log("✅ Module initialized successfully!");
        
        // Verify
        const newClaimRegistry = await kycModule.claimRegistry();
        console.log(`New claim registry: ${newClaimRegistry}`);
      } else {
        console.log("✅ Module already initialized");
      }
    } catch (error) {
      console.log("❌ Error checking module state:", error.message);
    }

    // Check roles
    console.log("\n🔑 Checking module roles...");
    try {
      const DEFAULT_ADMIN_ROLE = await kycModule.DEFAULT_ADMIN_ROLE();
      const MODULE_ADMIN_ROLE = await kycModule.MODULE_ADMIN_ROLE();
      const CLAIM_ISSUER_ROLE = await kycModule.CLAIM_ISSUER_ROLE();
      
      const hasDefaultAdmin = await kycModule.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
      const hasModuleAdmin = await kycModule.hasRole(MODULE_ADMIN_ROLE, deployer.address);
      const hasClaimIssuer = await kycModule.hasRole(CLAIM_ISSUER_ROLE, deployer.address);
      
      console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
      console.log(`✅ MODULE_ADMIN_ROLE: ${hasModuleAdmin}`);
      console.log(`✅ CLAIM_ISSUER_ROLE: ${hasClaimIssuer}`);
      
      if (!hasClaimIssuer) {
        console.log("🔧 Granting CLAIM_ISSUER_ROLE...");
        const grantTx = await kycModule.grantRole(CLAIM_ISSUER_ROLE, deployer.address);
        await grantTx.wait();
        console.log("✅ CLAIM_ISSUER_ROLE granted!");
      }
    } catch (error) {
      console.log("❌ Error checking roles:", error.message);
    }

    // Test the module directly
    console.log("\n🧪 Testing module directly...");
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    
    try {
      // Test KYC approval
      console.log(`Testing direct KYC approval for ${TEST_USER}...`);
      const kycTx = await kycModule.approveKYC(NEW_TOKEN_ADDRESS, TEST_USER);
      await kycTx.wait();
      console.log("✅ Direct KYC approval successful!");
      
      // Test whitelist addition
      console.log(`Testing direct whitelist addition for ${TEST_USER}...`);
      const whitelistTx = await kycModule.addToWhitelist(NEW_TOKEN_ADDRESS, TEST_USER);
      await whitelistTx.wait();
      console.log("✅ Direct whitelist addition successful!");
      
      // Check status
      console.log("Checking verification status...");
      const status = await kycModule.getVerificationStatus(NEW_TOKEN_ADDRESS, TEST_USER);
      console.log("✅ Verification status:", {
        kycApproved: status[0],
        whitelisted: status[1],
        eligible: status[2],
        method: status[3]
      });
      
    } catch (testError) {
      console.log("❌ Direct module test failed:", testError.message);
    }

    // Test through token contract
    console.log("\n🧪 Testing through token contract...");
    try {
      const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
      const tokenCore = SecurityTokenCore.attach(NEW_TOKEN_ADDRESS);
      
      // Check if module is registered
      const KYC_CLAIMS_MODULE_ID = ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"));
      const registeredModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
      console.log(`Registered module: ${registeredModule}`);
      
      if (registeredModule === KYC_CLAIMS_MODULE_ADDRESS) {
        console.log("✅ Module is properly registered");
        
        // Test KYC approval through token
        console.log(`Testing KYC approval through token for ${TEST_USER}...`);
        const kycTx = await tokenCore.approveKYC(TEST_USER);
        await kycTx.wait();
        console.log("✅ Token KYC approval successful!");
        
        // Test whitelist addition through token
        console.log(`Testing whitelist addition through token for ${TEST_USER}...`);
        const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
        await whitelistTx.wait();
        console.log("✅ Token whitelist addition successful!");
        
      } else {
        console.log("❌ Module not properly registered");
      }
      
    } catch (tokenError) {
      console.log("❌ Token test failed:", tokenError.message);
    }

    console.log("\n🎉 KYC Claims Module fix completed!");

  } catch (error) {
    console.error("❌ Fix failed:", error);
    throw error;
  }
}

// Execute fix
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
