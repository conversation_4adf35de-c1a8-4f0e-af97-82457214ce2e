// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

/**
 * @title IModularToken - Core interfaces for modular token architecture
 * @dev Defines the interfaces for all modules in the modular token system
 */

// ============================================================================
// CORE MODULE INTERFACES
// ============================================================================

/**
 * @title IIdentityManager
 * @dev Interface for identity, KYC, and whitelist management
 */
interface IIdentityManager {
    // Events
    event AddressWhitelisted(address indexed account);
    event AddressRemovedFromWhitelist(address indexed account);
    event AddressFrozen(address indexed account);
    event AddressUnfrozen(address indexed account);
    event KYCVerified(address indexed account, uint256 timestamp);
    event KYCRevoked(address indexed account, uint256 timestamp);

    // Core verification functions
    function isVerified(address account) external view returns (bool);
    function isWhitelisted(address account) external view returns (bool);
    function isFrozen(address account) external view returns (bool);
    function canTransfer(address from, address to) external view returns (bool);
    function investorCountry(address account) external view returns (uint16);

    // Whitelist management
    function addToWhitelist(address account) external;
    function removeFromWhitelist(address account) external;
    function batchAddToWhitelist(address[] calldata accounts) external;
    function batchRemoveFromWhitelist(address[] calldata accounts) external;

    // Freeze management
    function freezeAddress(address account) external;
    function unfreezeAddress(address account) external;
    function batchFreezeAddresses(address[] calldata accounts) external;
    function batchUnfreezeAddresses(address[] calldata accounts) external;

    // KYC management
    function verifyKYC(address account, uint16 country) external;
    function revokeKYC(address account) external;
    
    // View functions
    function getWhitelistCount() external view returns (uint256);
    function getVerifiedCount() external view returns (uint256);
    function getFrozenCount() external view returns (uint256);
}

/**
 * @title IComplianceEngine
 * @dev Interface for compliance rules and agreement management
 */
interface IComplianceEngine {
    // Events
    event ComplianceRuleAdded(bytes32 indexed ruleId, string name);
    event ComplianceRuleRemoved(bytes32 indexed ruleId);
    event AgreementAccepted(address indexed account, uint256 timestamp);
    event TransferCompliance(address indexed from, address indexed to, uint256 amount, bool compliant);

    // Core compliance functions
    function canTransfer(address from, address to, uint256 amount) external view returns (bool);
    function transferred(address from, address to, uint256 amount) external;
    function destroyed(address from, uint256 amount) external;

    // Agreement management
    function hasAcceptedAgreement(address account) external view returns (bool);
    function acceptAgreement() external;
    function getAgreementAcceptanceTimestamp(address account) external view returns (uint256);

    // Compliance rule management
    function addComplianceRule(
        bytes32 ruleId,
        string calldata name,
        uint256 maxHolders,
        uint256 maxTokensPerHolder,
        uint256 maxTotalSupply
    ) external;
    function removeComplianceRule(bytes32 ruleId) external;
    function getActiveRuleIds() external view returns (bytes32[] memory);

    // Statistics
    function getTransferStats() external view returns (uint256 totalTransfers, uint256 lastTransferTime);
    function getHolderCount() external view returns (uint256);
}

/**
 * @title ITransferController
 * @dev Interface for advanced transfer controls and force transfers
 */
interface ITransferController {
    // Events
    event ForcedTransfer(address indexed from, address indexed to, uint256 value);
    event ConditionalTransferEnabled(bool enabled);
    event TransferApproved(address indexed from, address indexed to, uint256 amount, bytes32 approvalId);
    event TransferFeeCollected(address indexed from, address indexed to, uint256 originalAmount, uint256 feeAmount);

    // Force transfer functions
    function forcedTransfer(address from, address to, uint256 amount) external returns (bool);
    function batchForcedTransfer(
        address[] calldata from,
        address[] calldata to,
        uint256[] calldata amounts
    ) external returns (bool);

    // Conditional transfer functions
    function enableConditionalTransfers(bool enabled) external;
    function approveTransfer(address from, address to, uint256 amount) external returns (bytes32 approvalId);
    function executeApprovedTransfer(bytes32 approvalId) external returns (bool);
    function revokeTransferApproval(bytes32 approvalId) external;

    // Transfer fee functions
    function setTransferFees(uint256 feePercentage, address collector) external;
    function enableTransferFees(bool enabled) external;
    function getTransferFee(uint256 amount) external view returns (uint256);

    // Transfer whitelist functions
    function enableTransferWhitelist(bool enabled) external;
    function addToTransferWhitelist(address account) external;
    function removeFromTransferWhitelist(address account) external;
    function isTransferWhitelisted(address account) external view returns (bool);

    // View functions
    function isConditionalTransfersEnabled() external view returns (bool);
    function isTransferFeesEnabled() external view returns (bool);
    function isTransferWhitelistEnabled() external view returns (bool);
    function getTransferFeePercentage() external view returns (uint256);
    function getFeeCollector() external view returns (address);
}

/**
 * @title IAgentManager
 * @dev Interface for agent role management and tracking
 */
interface IAgentManager {
    // Events
    event AgentAdded(address indexed agent);
    event AgentRemoved(address indexed agent);
    event AgentRoleGranted(address indexed agent, bytes32 indexed role);
    event AgentRoleRevoked(address indexed agent, bytes32 indexed role);

    // Agent management functions
    function addAgent(address agent) external;
    function removeAgent(address agent) external;
    function batchAddAgents(address[] calldata agents) external;
    function batchRemoveAgents(address[] calldata agents) external;

    // Agent validation functions
    function isAgent(address account) external view returns (bool);
    function hasAgentRole(address account, bytes32 role) external view returns (bool);
    function canPerformAction(address account, bytes4 selector) external view returns (bool);

    // Agent enumeration functions
    function getAgentCount() external view returns (uint256);
    function getAgentAt(uint256 index) external view returns (address);
    function getAllAgents() external view returns (address[] memory);

    // Role management functions
    function grantAgentRole(address agent, bytes32 role) external;
    function revokeAgentRole(address agent, bytes32 role) external;
    function getAgentRoles(address agent) external view returns (bytes32[] memory);
}

/**
 * @title IKYCClaimsModule
 * @dev Interface for KYC and custom claims verification
 */
interface IKYCClaimsModule {
    // Events
    event KYCApproved(address indexed token, address indexed user, bool onChain);
    event WhitelistApproved(address indexed token, address indexed user, bool onChain);
    event ClaimIssued(address indexed user, uint256 indexed claimType, bytes32 claimId);
    event TokenClaimsConfigured(address indexed token, uint256[] requiredClaims, bool kycEnabled, bool claimsEnabled);

    // Core verification functions
    function isKYCApproved(address token, address user) external view returns (bool);
    function isWhitelisted(address token, address user) external view returns (bool);
    function isEligible(address token, address user) external view returns (bool);

    // Configuration functions
    function configureTokenClaims(
        address token,
        uint256[] calldata requiredClaims,
        bool kycEnabled,
        bool claimsEnabled
    ) external;

    // Traditional approval functions
    function approveKYC(address token, address user) external;
    function addToWhitelist(address token, address user) external;

    // On-chain claim functions
    function issueKYCClaim(address user, bytes calldata data) external returns (bytes32);
    function issueCustomClaim(
        address user,
        uint256 claimType,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external returns (bytes32);

    // View functions
    function getVerificationStatus(address token, address user) external view returns (
        bool kycApproved,
        bool whitelisted,
        bool eligible,
        string memory method
    );
}

/**
 * @title IEmergencyManager
 * @dev Interface for emergency controls and system-wide pausing
 */
interface IEmergencyManager {
    // Events
    event EmergencyPaused(address indexed admin);
    event EmergencyUnpaused(address indexed admin);
    event FunctionPaused(bytes4 indexed functionSelector, address indexed admin);
    event FunctionUnpaused(bytes4 indexed functionSelector, address indexed admin);
    event CircuitBreakerTriggered(string reason, address indexed triggeredBy);

    // Emergency pause functions
    function emergencyPause() external;
    function emergencyUnpause() external;
    function pauseFunction(bytes4 selector) external;
    function unpauseFunction(bytes4 selector) external;

    // Circuit breaker functions
    function triggerCircuitBreaker(string calldata reason) external;
    function resetCircuitBreaker() external;
    function setCircuitBreakerThreshold(uint256 threshold) external;

    // Time-locked functions
    function scheduleEmergencyAction(bytes calldata action, uint256 delay) external returns (bytes32 actionId);
    function executeScheduledAction(bytes32 actionId) external;
    function cancelScheduledAction(bytes32 actionId) external;

    // View functions
    function isEmergencyPaused() external view returns (bool);
    function isFunctionPaused(bytes4 selector) external view returns (bool);
    function isCircuitBreakerTriggered() external view returns (bool);
    function getCircuitBreakerThreshold() external view returns (uint256);
    function getScheduledAction(bytes32 actionId) external view returns (bytes memory action, uint256 executeTime);
}

// ============================================================================
// MODULE REGISTRY INTERFACE
// ============================================================================

/**
 * @title IModuleRegistry
 * @dev Interface for managing module addresses and inter-module communication
 */
interface IModuleRegistry {
    // Events
    event ModuleRegistered(bytes32 indexed moduleId, address indexed moduleAddress);
    event ModuleUnregistered(bytes32 indexed moduleId);
    event ModuleUpgraded(bytes32 indexed moduleId, address indexed oldAddress, address indexed newAddress);

    // Module management functions
    function registerModule(bytes32 moduleId, address moduleAddress) external;
    function unregisterModule(bytes32 moduleId) external;
    function upgradeModule(bytes32 moduleId, address newModuleAddress) external;

    // Module access functions
    function getModule(bytes32 moduleId) external view returns (address);
    function isModuleRegistered(bytes32 moduleId) external view returns (bool);
    function getAllModules() external view returns (bytes32[] memory moduleIds, address[] memory moduleAddresses);

    // Module validation functions
    function validateModuleInterface(bytes32 moduleId, bytes4 interfaceId) external view returns (bool);
    function checkModuleCompatibility(bytes32 moduleId, address moduleAddress) external view returns (bool);
}

// ============================================================================
// CONSTANTS
// ============================================================================

library ModuleIds {
    bytes32 public constant IDENTITY_MANAGER = keccak256("IDENTITY_MANAGER");
    bytes32 public constant COMPLIANCE_ENGINE = keccak256("COMPLIANCE_ENGINE");
    bytes32 public constant TRANSFER_CONTROLLER = keccak256("TRANSFER_CONTROLLER");
    bytes32 public constant AGENT_MANAGER = keccak256("AGENT_MANAGER");
    bytes32 public constant EMERGENCY_MANAGER = keccak256("EMERGENCY_MANAGER");
    bytes32 public constant KYC_CLAIMS_MODULE = keccak256("KYC_CLAIMS_MODULE");
}
