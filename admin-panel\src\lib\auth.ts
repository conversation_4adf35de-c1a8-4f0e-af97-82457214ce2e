import { NextRequest } from 'next/server';

// Simple in-memory storage for demo (use database in production)
const apiKeys = new Map<string, {
  keyHash: string;
  name: string;
  permissions: string[];
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
  rateLimit?: {
    requests: number;
    windowStart: Date;
  };
}>();

// Add some default API keys for testing
apiKeys.set('ak_demo_key_for_testing_12345678901234567890123456789012', {
  keyHash: 'demo_hash',
  name: 'Demo API Key',
  permissions: ['read', 'write'],
  createdAt: new Date(),
  isActive: true
});

export interface AuthResult {
  authenticated: boolean;
  permissions: string[];
  error?: string;
  keyInfo?: {
    name: string;
    permissions: string[];
    lastUsed?: Date;
  };
}

export function authenticateRequest(request: NextRequest): AuthResult {
  // Check for API key in Authorization header
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader) {
    return {
      authenticated: false,
      permissions: [],
      error: 'Missing Authorization header'
    };
  }

  // Extract API key from Bearer token
  const match = authHeader.match(/^Bearer\s+(.+)$/);
  if (!match) {
    return {
      authenticated: false,
      permissions: [],
      error: 'Invalid Authorization header format. Use: Bearer <api_key>'
    };
  }

  const apiKey = match[1];

  // Validate API key
  const keyData = apiKeys.get(apiKey);
  
  if (!keyData || !keyData.isActive) {
    return {
      authenticated: false,
      permissions: [],
      error: 'Invalid or inactive API key'
    };
  }

  // Check rate limiting (simple implementation)
  const now = new Date();
  if (keyData.rateLimit) {
    const windowDuration = 60 * 1000; // 1 minute window
    const maxRequests = 100; // 100 requests per minute

    if (now.getTime() - keyData.rateLimit.windowStart.getTime() > windowDuration) {
      // Reset window
      keyData.rateLimit = {
        requests: 1,
        windowStart: now
      };
    } else {
      keyData.rateLimit.requests++;
      
      if (keyData.rateLimit.requests > maxRequests) {
        return {
          authenticated: false,
          permissions: [],
          error: 'Rate limit exceeded. Maximum 100 requests per minute.'
        };
      }
    }
  } else {
    keyData.rateLimit = {
      requests: 1,
      windowStart: now
    };
  }

  // Update last used timestamp
  keyData.lastUsed = now;
  apiKeys.set(apiKey, keyData);

  return {
    authenticated: true,
    permissions: keyData.permissions,
    keyInfo: {
      name: keyData.name,
      permissions: keyData.permissions,
      lastUsed: keyData.lastUsed
    }
  };
}

export function requirePermission(authResult: AuthResult, requiredPermission: string): boolean {
  if (!authResult.authenticated) {
    return false;
  }

  return authResult.permissions.includes(requiredPermission) || authResult.permissions.includes('admin');
}

export function createAuthHeaders(includeAuth: boolean = false) {
  const baseHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json'
  };

  if (includeAuth) {
    return {
      ...baseHeaders,
      'WWW-Authenticate': 'Bearer realm="API"'
    };
  }

  return baseHeaders;
}

// Generate new API key
export function generateApiKey(name: string, permissions: string[] = ['read']): string {
  const apiKey = `ak_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}${Date.now().toString(36)}`;
  
  apiKeys.set(apiKey, {
    keyHash: 'generated_hash',
    name,
    permissions,
    createdAt: new Date(),
    isActive: true
  });

  return apiKey;
}

// List all API keys (without exposing actual keys)
export function listApiKeys() {
  return Array.from(apiKeys.entries()).map(([key, data]) => ({
    keyId: key.substring(0, 12) + '...',
    name: data.name,
    permissions: data.permissions,
    createdAt: data.createdAt.toISOString(),
    lastUsed: data.lastUsed?.toISOString(),
    isActive: data.isActive,
    requestCount: data.rateLimit?.requests || 0
  }));
}
