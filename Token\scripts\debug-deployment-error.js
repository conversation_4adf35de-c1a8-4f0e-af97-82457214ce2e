const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Debugging deployment error...\n");

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deployer account:", deployer.address);

  const FACTORY_ADDRESS = "******************************************";
  const IMPLEMENTATION_ADDRESS = "******************************************";

  try {
    // Get the factory contract
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(FACTORY_ADDRESS);

    console.log("📋 Factory Information:");
    const implementation = await factory.securityTokenImplementation();
    const upgradeManager = await factory.upgradeManager();
    const tokensCount = await factory.getDeployedTokensCount();
    
    console.log("Implementation:", implementation);
    console.log("Upgrade Manager:", upgradeManager);
    console.log("Deployed tokens count:", tokensCount.toString());

    // Check roles
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const hasRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
    console.log("Has deployer role:", hasRole);

    // Test parameters (same as the failing transaction)
    const testParams = {
      name: "modul_01",
      symbol: "modul_01", 
      decimals: 0,
      maxSupply: ethers.parseUnits("890000", 0), // 890000 tokens with 0 decimals
      admin: "******************************************",
      tokenPrice: "2 USD",
      bonusTiers: "Early Bird: 10%, Standard: 5%, Late: 2%",
      tokenDetails: "Security token with advanced compliance features23",
      tokenImageUrl: "https://mnp.rs/wp-content/uploads/2024/11/IMG_8634.png"
    };

    console.log("\n🧪 Testing deployment with same parameters...");
    console.log("Parameters:", testParams);

    // Try to estimate gas first
    try {
      const gasEstimate = await factory.deployToken.estimateGas(
        testParams.name,
        testParams.symbol,
        testParams.decimals,
        testParams.maxSupply,
        testParams.admin,
        testParams.tokenPrice,
        testParams.bonusTiers,
        testParams.tokenDetails,
        testParams.tokenImageUrl
      );
      console.log("✅ Gas estimate successful:", gasEstimate.toString());
    } catch (gasError) {
      console.log("❌ Gas estimation failed:", gasError.message);
      
      // Try to decode the error
      if (gasError.data) {
        console.log("Error data:", gasError.data);
        
        // Check if it's a known error
        const errorData = gasError.data;
        if (errorData === "0xd6bda275") {
          console.log("🔍 This is AccessControlUnauthorizedAccount error");
          
          // Let's check what role is actually needed
          try {
            // Try to call the function and see what role it expects
            const tx = await factory.deployToken.populateTransaction(
              testParams.name,
              testParams.symbol,
              testParams.decimals,
              testParams.maxSupply,
              testParams.admin,
              testParams.tokenPrice,
              testParams.bonusTiers,
              testParams.tokenDetails,
              testParams.tokenImageUrl
            );
            console.log("Transaction data:", tx.data);
          } catch (e) {
            console.log("Error creating transaction:", e.message);
          }
        }
      }
      
      return;
    }

    // If gas estimation succeeds, try the actual deployment
    console.log("\n🚀 Attempting actual deployment...");
    const tx = await factory.deployToken(
      testParams.name,
      testParams.symbol,
      testParams.decimals,
      testParams.maxSupply,
      testParams.admin,
      testParams.tokenPrice,
      testParams.bonusTiers,
      testParams.tokenDetails,
      testParams.tokenImageUrl
    );

    console.log("✅ Transaction sent:", tx.hash);
    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed!");

    // Get the deployed token address
    const event = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (event) {
      const parsedEvent = factory.interface.parseLog(event);
      console.log("🎉 Token deployed to:", parsedEvent.args.tokenAddress);
    }

  } catch (error) {
    console.error("❌ Error:", error);
    
    // Detailed error analysis
    if (error.data) {
      console.log("\n🔍 Error Analysis:");
      console.log("Error data:", error.data);
      console.log("Error code:", error.code);
      
      // Try to decode common errors
      const commonErrors = {
        "0xd6bda275": "AccessControlUnauthorizedAccount(address account, bytes32 neededRole)",
        "0x50c83a81": "AccessControlBadConfirmation()",
        "0xab143c06": "ReentrancyGuardReentrantCall()",
        "0x4e487b71": "Panic(uint256)", // Array out of bounds, division by zero, etc.
        "0x08c379a0": "Error(string)" // Generic revert with message
      };
      
      const errorSig = error.data.slice(0, 10);
      if (commonErrors[errorSig]) {
        console.log("Decoded error type:", commonErrors[errorSig]);
        
        if (errorSig === "0xd6bda275") {
          // This is AccessControlUnauthorizedAccount
          // The error data contains: account (32 bytes) + neededRole (32 bytes)
          const account = "0x" + error.data.slice(10, 74);
          const neededRole = "0x" + error.data.slice(74, 138);
          console.log("Account:", account);
          console.log("Needed role:", neededRole);
          
          // Check what this role is
          const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
          const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
          
          if (neededRole.toLowerCase() === DEPLOYER_ROLE.toLowerCase()) {
            console.log("❌ Missing DEPLOYER_ROLE");
          } else if (neededRole.toLowerCase() === DEFAULT_ADMIN_ROLE.toLowerCase()) {
            console.log("❌ Missing DEFAULT_ADMIN_ROLE");
          } else {
            console.log("❌ Missing unknown role:", neededRole);
          }
        }
      }
    }
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
