const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Testing KYC Claims Module...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  const KYC_MODULE_ADDRESS = "******************************************";
  const CLAIM_REGISTRY_ADDRESS = "******************************************";

  try {
    // Get the deployed module
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    const kycModule = KYCClaimsModule.attach(KYC_MODULE_ADDRESS);

    console.log("📋 Module address:", KYC_MODULE_ADDRESS);

    // Check if it's already initialized
    try {
      const claimRegistry = await kycModule.claimRegistry();
      console.log("✅ Module already initialized with claim registry:", claimRegistry);
    } catch (e) {
      console.log("❌ Module not initialized, attempting initialization...");
      
      // Try to initialize
      const initTx = await kycModule.initialize(
        CLAIM_REGISTRY_ADDRESS,
        deployer.address
      );
      await initTx.wait();
      console.log("✅ Module initialized successfully");
    }

    // Test basic functionality
    console.log("\n🔍 Testing basic functionality...");
    
    const moduleId = await kycModule.moduleId();
    const version = await kycModule.version();
    
    console.log("✅ Module ID:", moduleId);
    console.log("✅ Version:", version);

    // Test constants
    const kycVerification = await kycModule.KYC_VERIFICATION();
    const generalQualification = await kycModule.GENERAL_QUALIFICATION();
    
    console.log("✅ KYC_VERIFICATION topic:", kycVerification.toString());
    console.log("✅ GENERAL_QUALIFICATION topic:", generalQualification.toString());

    console.log("\n🎉 KYC Claims Module is working correctly!");

  } catch (error) {
    console.error("❌ Error:", error);
    
    if (error.message.includes("execution reverted")) {
      console.log("\n💡 This might be because:");
      console.log("1. The ClaimRegistry address is incorrect");
      console.log("2. The module is already initialized");
      console.log("3. There's an access control issue");
    }
  }
}

// Execute test
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
