{"_format": "hh-sol-artifact-1", "contractName": "IKYCClaimsModule", "sourceName": "contracts/interfaces/IModularToken.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "claimType", "type": "uint256"}, {"indexed": false, "internalType": "bytes32", "name": "claimId", "type": "bytes32"}], "name": "ClaimIssued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "on<PERSON><PERSON><PERSON>", "type": "bool"}], "name": "KYCApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "requiredClaims", "type": "uint256[]"}, {"indexed": false, "internalType": "bool", "name": "kycEnabled", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "claimsEnabled", "type": "bool"}], "name": "TokenClaimsConfigured", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "on<PERSON><PERSON><PERSON>", "type": "bool"}], "name": "WhitelistApproved", "type": "event"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "approveKYC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256[]", "name": "requiredClaims", "type": "uint256[]"}, {"internalType": "bool", "name": "kycEnabled", "type": "bool"}, {"internalType": "bool", "name": "claimsEnabled", "type": "bool"}], "name": "configureTokenClaims", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getVerificationStatus", "outputs": [{"internalType": "bool", "name": "kycApproved", "type": "bool"}, {"internalType": "bool", "name": "whitelisted", "type": "bool"}, {"internalType": "bool", "name": "eligible", "type": "bool"}, {"internalType": "string", "name": "method", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isEligible", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "isKYCApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}], "name": "issueCustomClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "issueKYCClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}