'use client';

import React, { useState, useEffect } from 'react';

interface APIEndpoint {
  name: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  path: string;
  description: string;
  category: string;
  testData?: any;
  expectedStatus?: number;
}

interface APITestResult {
  endpoint: APIEndpoint;
  status: 'pending' | 'running' | 'passed' | 'failed';
  responseStatus?: number;
  responseTime?: number;
  responseData?: any;
  error?: string;
}

export default function APIIntegrationPage() {
  const [testResults, setTestResults] = useState<APITestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Define API endpoints to test
  const apiEndpoints: APIEndpoint[] = [
    // Status & Health
    {
      name: 'System Status',
      method: 'GET',
      path: '/api/status',
      description: 'Check system health and status',
      category: 'System',
      expectedStatus: 200
    },

    // Token Management
    {
      name: 'Get Tokens',
      method: 'GET',
      path: '/api/tokens',
      description: 'Retrieve all deployed tokens',
      category: 'Tokens',
      expectedStatus: 200
    },
    {
      name: 'Get Modular Tokens',
      method: 'GET',
      path: '/api/modular-tokens',
      description: 'Retrieve modular token information',
      category: 'Tokens',
      expectedStatus: 200
    },

    // Admin Functions
    {
      name: 'Token Info',
      method: 'GET',
      path: '/api/admin/token-info',
      description: 'Get detailed token information',
      category: 'Admin',
      expectedStatus: 200
    },

    // Client Management
    {
      name: 'Get Clients',
      method: 'GET',
      path: '/api/clients',
      description: 'Retrieve client list',
      category: 'Clients',
      expectedStatus: 200
    },
    {
      name: 'Client Stats',
      method: 'GET',
      path: '/api/clients/stats',
      description: 'Get client statistics',
      category: 'Clients',
      expectedStatus: 200
    },

    // KYC & Claims
    {
      name: 'KYC Status',
      method: 'GET',
      path: '/api/kyc/status',
      description: 'Check KYC status endpoint',
      category: 'KYC',
      expectedStatus: 200
    },
    {
      name: 'Claims',
      method: 'GET',
      path: '/api/claims',
      description: 'Retrieve claims information',
      category: 'Claims',
      expectedStatus: 200
    },
    {
      name: 'Claim Types',
      method: 'GET',
      path: '/api/claim-types',
      description: 'Get available claim types',
      category: 'Claims',
      expectedStatus: 200
    },

    // Orders
    {
      name: 'Get Orders',
      method: 'GET',
      path: '/api/orders',
      description: 'Retrieve order information',
      category: 'Orders',
      expectedStatus: 200
    },

    // Qualifications
    {
      name: 'Get Qualifications',
      method: 'GET',
      path: '/api/qualifications',
      description: 'Retrieve qualification data',
      category: 'Qualifications',
      expectedStatus: 200
    },
    {
      name: 'Qualification Progress',
      method: 'GET',
      path: '/api/qualification-progress',
      description: 'Get qualification progress',
      category: 'Qualifications',
      expectedStatus: 200
    },

    // Identity & Whitelist
    {
      name: 'Identity',
      method: 'GET',
      path: '/api/identity',
      description: 'Get identity information',
      category: 'Identity',
      expectedStatus: 200
    },
    {
      name: 'Whitelist Check',
      method: 'GET',
      path: '/api/whitelist/check',
      description: 'Check whitelist status',
      category: 'Whitelist',
      expectedStatus: 200
    },

    // Token Agreements
    {
      name: 'Token Agreements',
      method: 'GET',
      path: '/api/token-agreements',
      description: 'Get token agreements',
      category: 'Agreements',
      expectedStatus: 200
    }
  ];

  // Initialize test results
  useEffect(() => {
    const initialResults = apiEndpoints.map(endpoint => ({
      endpoint,
      status: 'pending' as const
    }));
    setTestResults(initialResults);
  }, []);

  // Test single endpoint
  const testEndpoint = async (endpoint: APIEndpoint): Promise<APITestResult> => {
    const startTime = Date.now();
    
    try {
      const response = await fetch(endpoint.path, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
        ...(endpoint.testData && { body: JSON.stringify(endpoint.testData) })
      });

      const responseTime = Date.now() - startTime;
      let responseData;
      
      try {
        responseData = await response.json();
      } catch {
        responseData = await response.text();
      }

      const passed = endpoint.expectedStatus ? 
        response.status === endpoint.expectedStatus : 
        response.ok;

      return {
        endpoint,
        status: passed ? 'passed' : 'failed',
        responseStatus: response.status,
        responseTime,
        responseData,
        error: passed ? undefined : `Expected ${endpoint.expectedStatus}, got ${response.status}`
      };
    } catch (error: any) {
      return {
        endpoint,
        status: 'failed',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  };

  // Run all tests
  const runAllTests = async () => {
    setIsRunningTests(true);
    
    for (let i = 0; i < apiEndpoints.length; i++) {
      const endpoint = apiEndpoints[i];
      
      // Update status to running
      setTestResults(prev => prev.map((result, index) => 
        index === i ? { ...result, status: 'running' } : result
      ));

      // Run test
      const result = await testEndpoint(endpoint);
      
      // Update with result
      setTestResults(prev => prev.map((prevResult, index) => 
        index === i ? result : prevResult
      ));

      // Add delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunningTests(false);
  };

  // Run tests for specific category
  const runCategoryTests = async (category: string) => {
    setIsRunningTests(true);
    
    const categoryEndpoints = apiEndpoints
      .map((endpoint, index) => ({ endpoint, index }))
      .filter(({ endpoint }) => category === 'all' || endpoint.category === category);

    for (const { endpoint, index } of categoryEndpoints) {
      // Update status to running
      setTestResults(prev => prev.map((result, i) => 
        i === index ? { ...result, status: 'running' } : result
      ));

      // Run test
      const result = await testEndpoint(endpoint);
      
      // Update with result
      setTestResults(prev => prev.map((prevResult, i) => 
        i === index ? result : prevResult
      ));

      // Add delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunningTests(false);
  };

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(apiEndpoints.map(e => e.category)))];

  // Filter results by category
  const filteredResults = selectedCategory === 'all' 
    ? testResults 
    : testResults.filter(result => result.endpoint.category === selectedCategory);

  // Calculate stats
  const totalTests = filteredResults.length;
  const passedTests = filteredResults.filter(r => r.status === 'passed').length;
  const failedTests = filteredResults.filter(r => r.status === 'failed').length;
  const pendingTests = filteredResults.filter(r => r.status === 'pending').length;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">API Integration Testing</h1>
          <p className="text-gray-600">
            Test all API endpoints for functionality and performance
          </p>
        </div>
        <div className="flex gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </option>
            ))}
          </select>
          <button
            onClick={() => runCategoryTests(selectedCategory)}
            disabled={isRunningTests}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {isRunningTests ? '🔄 Running Tests...' : '🧪 Run Tests'}
          </button>
        </div>
      </div>

      {/* Test Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-blue-600">{totalTests}</div>
          <div className="text-sm text-gray-600">Total Tests</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-green-600">{passedTests}</div>
          <div className="text-sm text-gray-600">Passed</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-red-600">{failedTests}</div>
          <div className="text-sm text-gray-600">Failed</div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <div className="text-3xl font-bold text-gray-600">{pendingTests}</div>
          <div className="text-sm text-gray-600">Pending</div>
        </div>
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">
          API Test Results 
          {selectedCategory !== 'all' && (
            <span className="text-sm font-normal text-gray-600"> - {selectedCategory}</span>
          )}
        </h2>
        
        <div className="space-y-3">
          {filteredResults.map((result, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
                      result.status === 'passed' ? 'bg-green-500 text-white' :
                      result.status === 'failed' ? 'bg-red-500 text-white' :
                      result.status === 'running' ? 'bg-blue-500 text-white animate-pulse' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      {result.status === 'passed' ? '✓' :
                       result.status === 'failed' ? '✗' :
                       result.status === 'running' ? '⟳' :
                       '○'}
                    </div>
                    <div>
                      <span className="font-medium">{result.endpoint.name}</span>
                      <span className={`ml-2 px-2 py-1 text-xs rounded ${
                        result.endpoint.method === 'GET' ? 'bg-blue-100 text-blue-800' :
                        result.endpoint.method === 'POST' ? 'bg-green-100 text-green-800' :
                        result.endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {result.endpoint.method}
                      </span>
                      <span className="ml-2 text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">
                        {result.endpoint.category}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{result.endpoint.description}</p>
                  <p className="text-xs text-gray-500 mt-1 font-mono">{result.endpoint.path}</p>
                  
                  {result.responseTime && (
                    <p className="text-xs text-gray-500 mt-1">
                      Response time: {result.responseTime}ms
                    </p>
                  )}
                  
                  {result.error && (
                    <p className="text-sm text-red-600 mt-1">Error: {result.error}</p>
                  )}
                </div>
                
                <div className="text-right">
                  {result.responseStatus && (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      result.responseStatus >= 200 && result.responseStatus < 300 ? 'bg-green-100 text-green-800' :
                      result.responseStatus >= 400 ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {result.responseStatus}
                    </span>
                  )}
                </div>
              </div>
              
              {result.responseData && (
                <details className="mt-3">
                  <summary className="text-xs text-gray-500 cursor-pointer">View Response Data</summary>
                  <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto max-h-40">
                    {typeof result.responseData === 'string' 
                      ? result.responseData 
                      : JSON.stringify(result.responseData, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      </div>

      {filteredResults.length === 0 && (
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <p className="text-gray-500">No tests available for the selected category</p>
        </div>
      )}
    </div>
  );
}
