const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Checking SecurityTokenCore roles...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Checking roles for account:", deployer.address);

  const SECURITY_TOKEN_CORE_ADDRESS = process.env.TOKEN_ADDRESS || "******************************************";

  try {
    // Get the SecurityTokenCore contract
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const tokenCore = SecurityTokenCore.attach(SECURITY_TOKEN_CORE_ADDRESS);

    console.log("📋 Contract address:", SECURITY_TOKEN_CORE_ADDRESS);

    // Check all roles
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    const TRANSFER_MANAGER_ROLE = await tokenCore.TRANSFER_MANAGER_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();

    console.log("\n🔑 Role Constants:");
    console.log(`DEFAULT_ADMIN_ROLE: ${DEFAULT_ADMIN_ROLE}`);
    console.log(`AGENT_ROLE: ${AGENT_ROLE}`);
    console.log(`TRANSFER_MANAGER_ROLE: ${TRANSFER_MANAGER_ROLE}`);
    console.log(`MODULE_MANAGER_ROLE: ${MODULE_MANAGER_ROLE}`);

    console.log("\n👤 Current account roles:");
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    const hasTransferManager = await tokenCore.hasRole(TRANSFER_MANAGER_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);

    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);
    console.log(`✅ TRANSFER_MANAGER_ROLE: ${hasTransferManager}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);

    if (!hasModuleManager) {
      console.log("\n⚠️ Account does not have MODULE_MANAGER_ROLE!");
      
      if (hasDefaultAdmin) {
        console.log("🔧 Granting MODULE_MANAGER_ROLE...");
        const grantTx = await tokenCore.grantRole(MODULE_MANAGER_ROLE, deployer.address);
        await grantTx.wait();
        console.log("✅ MODULE_MANAGER_ROLE granted!");
        
        // Verify
        const hasModuleManagerNow = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
        console.log(`✅ Verification - MODULE_MANAGER_ROLE: ${hasModuleManagerNow}`);
      } else {
        console.log("❌ Cannot grant MODULE_MANAGER_ROLE - account does not have DEFAULT_ADMIN_ROLE");
      }
    }

    // Check token info
    console.log("\n📊 Token Information:");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const version = await tokenCore.version();
    
    console.log(`Name: ${name}`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Version: ${version}`);

  } catch (error) {
    console.error("❌ Error checking roles:", error);
    throw error;
  }
}

// Execute check
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
