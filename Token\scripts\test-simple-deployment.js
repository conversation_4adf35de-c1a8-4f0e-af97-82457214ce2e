const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Testing simple token deployment...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deployer:", deployer.address);

  const FACTORY_ADDRESS = "******************************************";

  try {
    // Get the factory contract
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(FACTORY_ADDRESS);

    // Simple test parameters
    const testParams = {
      name: "SimpleTest",
      symbol: "ST",
      decimals: 0,
      maxSupply: ethers.parseUnits("1000", 0), // 1000 tokens with 0 decimals
      admin: deployer.address,
      tokenPrice: "1 USD",
      bonusTiers: "None",
      tokenDetails: "Simple test token",
      tokenImageUrl: ""
    };

    console.log("🚀 Deploying simple test token...");
    console.log("Parameters:", testParams);

    const tx = await factory.deployToken(
      testParams.name,
      testParams.symbol,
      testParams.decimals,
      testParams.maxSupply,
      testParams.admin,
      testParams.tokenPrice,
      testParams.bonusTiers,
      testParams.tokenDetails,
      testParams.tokenImageUrl
    );

    console.log("✅ Transaction sent:", tx.hash);
    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed!");

    // Get the deployed token address
    const event = receipt.logs.find(log => {
      try {
        const parsed = factory.interface.parseLog(log);
        return parsed.name === 'TokenDeployed';
      } catch {
        return false;
      }
    });

    if (event) {
      const parsedEvent = factory.interface.parseLog(event);
      const tokenAddress = parsedEvent.args.tokenAddress;
      
      console.log("🎉 Token deployed to:", tokenAddress);

      // Test the deployed token
      const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
      const token = SecurityTokenCore.attach(tokenAddress);

      const name = await token.name();
      const symbol = await token.symbol();
      const decimals = await token.decimals();
      const maxSupply = await token.maxSupply();
      const totalSupply = await token.totalSupply();

      console.log("\n📋 Token Information:");
      console.log("Name:", name);
      console.log("Symbol:", symbol);
      console.log("Decimals:", decimals);
      console.log("Max Supply:", ethers.formatUnits(maxSupply, decimals));
      console.log("Total Supply:", ethers.formatUnits(totalSupply, decimals));

      // Try to mint some tokens
      console.log("\n🪙 Testing minting...");
      try {
        const mintTx = await token.mint(deployer.address, ethers.parseUnits("100", decimals));
        await mintTx.wait();
        console.log("✅ Minting successful!");
        
        const newTotalSupply = await token.totalSupply();
        console.log("New Total Supply:", ethers.formatUnits(newTotalSupply, decimals));
      } catch (mintError) {
        console.log("❌ Minting failed:", mintError.message);
        
        // This is expected since modules aren't registered
        if (mintError.message.includes("module not registered") || 
            mintError.message.includes("identity check failed") ||
            mintError.message.includes("recipient not verified")) {
          console.log("💡 This is expected - modules need to be registered first");
        }
      }

      return tokenAddress;
    } else {
      console.log("❌ Could not find TokenDeployed event");
    }

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    
    if (error.data) {
      console.log("Error data:", error.data);
    }
    
    throw error;
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
