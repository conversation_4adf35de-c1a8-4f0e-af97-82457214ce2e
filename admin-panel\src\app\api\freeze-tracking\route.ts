import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tokenAddress, userAddress, amount, operation, txHash } = body;

    if (!tokenAddress || !userAddress || !amount || !operation || !txHash) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Store the freeze/unfreeze operation
    const freezeOperation = await prisma.freezeOperation.create({
      data: {
        tokenAddress: tokenAddress.toLowerCase(),
        userAddress: userAddress.toLowerCase(),
        amount: parseFloat(amount),
        operation: operation, // 'freeze' or 'unfreeze'
        txHash: txHash,
        timestamp: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      operationId: freezeOperation.id
    });

  } catch (error: any) {
    console.error('Error storing freeze operation:', error);
    return NextResponse.json(
      { 
        error: 'Failed to store freeze operation',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'tokenAddress is required' },
        { status: 400 }
      );
    }

    // Database tracking is disabled due to Prisma client issues
    // Frontend uses localStorage for tracking instead
    console.log('Database tracking disabled - returning empty data for localStorage fallback');

    return NextResponse.json({
      success: true,
      operations: [],
      userFrozenAmounts: {},
      message: 'Database tracking disabled - using localStorage fallback'
    });

  } catch (error: any) {
    console.error('Error in freeze tracking API:', error);
    return NextResponse.json(
      {
        success: true,
        operations: [],
        userFrozenAmounts: {},
        message: 'Database not available - using localStorage fallback'
      },
      { status: 200 }
    );
  }
}
