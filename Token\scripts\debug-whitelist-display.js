const { ethers } = require("hardhat");

// Token address to check
const TOKEN_ADDRESS = "******************************************";

// Known whitelisted addresses
const KNOWN_ADDRESSES = [
  "******************************************",
  "******************************************",
];

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 DEBUGGING WHITELIST DISPLAY ISSUE");
  console.log("Token Address:", TOKEN_ADDRESS);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    console.log("\n📋 DIRECT CONTRACT CALLS:");
    
    // Test direct contract calls
    for (const address of KNOWN_ADDRESSES) {
      console.log(`\nTesting address: ${address}`);
      
      try {
        const isWhitelisted = await tokenContract.isWhitelisted(address);
        const isVerified = await tokenContract.isVerified(address);
        const balance = await tokenContract.balanceOf(address);
        const decimals = await tokenContract.decimals();
        
        console.log(`  Direct contract call - isWhitelisted: ${isWhitelisted}`);
        console.log(`  Direct contract call - isVerified: ${isVerified}`);
        console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
        
        if (isWhitelisted && isVerified) {
          console.log(`  ✅ Status: PROPERLY WHITELISTED`);
        } else {
          console.log(`  ❌ Status: NOT PROPERLY WHITELISTED`);
        }
      } catch (error) {
        console.log(`  ❌ Error checking address: ${error.message}`);
      }
    }

    console.log("\n🌐 TESTING ADMIN PANEL API:");
    
    // Test the admin panel API endpoint
    try {
      const response = await fetch(`http://localhost:6677/api/contracts/token/whitelist?tokenAddress=${TOKEN_ADDRESS}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log("API Response:", JSON.stringify(data, null, 2));
        
        if (data.whitelistedAddresses && data.whitelistedAddresses.length > 0) {
          console.log(`✅ API returns ${data.whitelistedAddresses.length} whitelisted addresses`);
        } else {
          console.log("❌ API returns empty whitelist - THIS IS THE PROBLEM!");
        }
      } else {
        console.log("❌ API request failed:", response.status, response.statusText);
        const errorText = await response.text();
        console.log("Error details:", errorText);
      }
    } catch (apiError) {
      console.log("❌ API test failed:", apiError.message);
    }

    console.log("\n🔍 CHECKING WHITELIST ENUMERATION:");
    
    // The issue might be that the admin panel is trying to enumerate whitelisted addresses
    // but our direct storage doesn't have enumeration capability
    
    console.log("The problem is likely that:");
    console.log("1. Contract has direct whitelist storage (_directWhitelist mapping)");
    console.log("2. Admin panel expects enumerable whitelist (array of addresses)");
    console.log("3. Mappings can't be enumerated without additional tracking");
    
    console.log("\n💡 SOLUTION NEEDED:");
    console.log("We need to either:");
    console.log("1. Add enumeration capability to the contract (address array + events)");
    console.log("2. Update admin panel to work with known addresses");
    console.log("3. Deploy proper whitelist modules");

    // Check if there are any whitelist events we can use
    console.log("\n📋 CHECKING RECENT WHITELIST EVENTS:");
    
    try {
      // Get recent AddressWhitelisted events
      const filter = tokenContract.filters.AddressWhitelisted();
      const events = await tokenContract.queryFilter(filter, -1000); // Last 1000 blocks
      
      console.log(`Found ${events.length} AddressWhitelisted events:`);
      for (const event of events) {
        console.log(`  - Address: ${event.args.account} (Block: ${event.blockNumber})`);
      }
      
      if (events.length > 0) {
        console.log("✅ Events exist - admin panel should use these for enumeration");
      } else {
        console.log("❌ No whitelist events found");
      }
    } catch (eventError) {
      console.log("❌ Error checking events:", eventError.message);
    }

  } catch (error) {
    console.error("❌ Debug failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
