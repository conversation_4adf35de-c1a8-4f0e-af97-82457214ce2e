import { NextRequest, NextResponse } from 'next/server';
import { randomBytes, createHash } from 'crypto';

// Simple in-memory storage for demo (use database in production)
const apiKeys = new Map<string, {
  keyHash: string;
  name: string;
  permissions: string[];
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
}>();

// Generate API key
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, permissions = ['read'] } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'API key name is required' },
        { status: 400 }
      );
    }

    // Generate a secure API key
    const apiKey = `ak_${randomBytes(32).toString('hex')}`;
    const keyHash = createHash('sha256').update(apiKey).digest('hex');

    // Store API key info (hash only, not the actual key)
    apiKeys.set(apiKey, {
      keyHash,
      name,
      permissions,
      createdAt: new Date(),
      isActive: true
    });

    return NextResponse.json({
      success: true,
      data: {
        apiKey, // Only returned once during creation
        name,
        permissions,
        createdAt: new Date().toISOString(),
        usage: {
          endpoint: '/api/external/*',
          header: 'Authorization: Bearer ' + apiKey,
          example: `curl -H "Authorization: Bearer ${apiKey}" https://your-domain.com/api/external/tokens`
        }
      }
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to generate API key', message: error.message },
      { status: 500 }
    );
  }
}

// List API keys (without showing actual keys)
export async function GET() {
  try {
    const keys = Array.from(apiKeys.entries()).map(([key, data]) => ({
      keyId: key.substring(0, 12) + '...',
      name: data.name,
      permissions: data.permissions,
      createdAt: data.createdAt.toISOString(),
      lastUsed: data.lastUsed?.toISOString(),
      isActive: data.isActive
    }));

    return NextResponse.json({
      success: true,
      data: {
        keys,
        totalCount: keys.length
      }
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to list API keys', message: error.message },
      { status: 500 }
    );
  }
}

// Validate API key (internal function)
export function validateApiKey(apiKey: string): { valid: boolean; permissions: string[] } {
  const keyData = apiKeys.get(apiKey);
  
  if (!keyData || !keyData.isActive) {
    return { valid: false, permissions: [] };
  }

  // Update last used timestamp
  keyData.lastUsed = new Date();
  apiKeys.set(apiKey, keyData);

  return { valid: true, permissions: keyData.permissions };
}
