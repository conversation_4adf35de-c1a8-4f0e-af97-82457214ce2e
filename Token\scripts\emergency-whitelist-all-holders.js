const { ethers } = require("hardhat");

// Token address to fix
const TOKEN_ADDRESS = "******************************************";

// All token holders that need to be whitelisted
const TOKEN_HOLDERS = [
  "******************************************", // 60 tokens - already whitelisted
  "******************************************", // 2 tokens - already whitelisted
  "******************************************", // 303 tokens - NEEDS WHITELISTING
  "******************************************", // 100 tokens - NEEDS WHITELISTING
  "******************************************", // 40 tokens - NEEDS WHITELISTING
  "******************************************", // 10 tokens - NEEDS WHITELISTING
];

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚨 EMERGENCY WHITELISTING ALL TOKEN HOLDERS");
  console.log("Token Address:", TOKEN_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    // Check admin permissions
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    if (!hasAdminRole) {
      console.log("❌ Cannot whitelist - deployer doesn't have DEFAULT_ADMIN_ROLE");
      return;
    }
    
    console.log("✅ Deployer has admin role - proceeding with emergency whitelisting");

    console.log("\n📋 CURRENT STATUS CHECK:");
    
    let alreadyWhitelisted = 0;
    let needsWhitelisting = 0;
    
    for (const address of TOKEN_HOLDERS) {
      const isWhitelisted = await tokenContract.isWhitelisted(address);
      const balance = await tokenContract.balanceOf(address);
      const decimals = await tokenContract.decimals();
      
      console.log(`\n${address}:`);
      console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
      console.log(`  Currently whitelisted: ${isWhitelisted}`);
      
      if (isWhitelisted) {
        alreadyWhitelisted++;
        console.log(`  ✅ Already compliant`);
      } else {
        needsWhitelisting++;
        console.log(`  ❌ NEEDS WHITELISTING`);
      }
    }
    
    console.log(`\n📊 Summary:`);
    console.log(`  Already whitelisted: ${alreadyWhitelisted}`);
    console.log(`  Needs whitelisting: ${needsWhitelisting}`);

    if (needsWhitelisting === 0) {
      console.log("✅ All token holders are already whitelisted!");
      return;
    }

    console.log("\n🔧 EMERGENCY WHITELISTING PROCESS:");
    
    let successCount = 0;
    let failCount = 0;
    
    for (const address of TOKEN_HOLDERS) {
      try {
        const isWhitelisted = await tokenContract.isWhitelisted(address);
        
        if (!isWhitelisted) {
          console.log(`\nWhitelisting ${address}...`);
          
          const tx = await tokenContract.addToWhitelist(address);
          await tx.wait();
          
          // Verify the whitelisting worked
          const nowWhitelisted = await tokenContract.isWhitelisted(address);
          const nowVerified = await tokenContract.isVerified(address);
          
          if (nowWhitelisted && nowVerified) {
            console.log(`✅ Successfully whitelisted and verified ${address}`);
            console.log(`   Transaction: ${tx.hash}`);
            successCount++;
          } else {
            console.log(`❌ Whitelisting failed for ${address} - not properly set`);
            failCount++;
          }
        } else {
          console.log(`✅ ${address} already whitelisted`);
          successCount++;
        }
      } catch (error) {
        console.log(`❌ Failed to whitelist ${address}: ${error.message}`);
        failCount++;
      }
    }

    console.log("\n🎯 EMERGENCY WHITELISTING RESULTS:");
    console.log(`  Successful: ${successCount}`);
    console.log(`  Failed: ${failCount}`);
    console.log(`  Total addresses: ${TOKEN_HOLDERS.length}`);

    if (successCount === TOKEN_HOLDERS.length) {
      console.log("🎉 ALL TOKEN HOLDERS SUCCESSFULLY WHITELISTED!");
    } else {
      console.log("⚠️ Some addresses failed to whitelist - manual intervention needed");
    }

    // Final verification
    console.log("\n🔍 FINAL COMPLIANCE VERIFICATION:");
    
    let compliantTokens = 0n;
    let nonCompliantTokens = 0n;
    const decimals = await tokenContract.decimals();
    
    for (const address of TOKEN_HOLDERS) {
      const isWhitelisted = await tokenContract.isWhitelisted(address);
      const isVerified = await tokenContract.isVerified(address);
      const balance = await tokenContract.balanceOf(address);
      
      console.log(`\n${address}:`);
      console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
      console.log(`  Whitelisted: ${isWhitelisted}`);
      console.log(`  Verified: ${isVerified}`);
      
      if (isWhitelisted && isVerified) {
        console.log(`  ✅ COMPLIANT`);
        compliantTokens += balance;
      } else {
        console.log(`  ❌ NON-COMPLIANT`);
        nonCompliantTokens += balance;
      }
    }
    
    const totalSupply = await tokenContract.totalSupply();
    
    console.log("\n📊 FINAL COMPLIANCE REPORT:");
    console.log(`Total Supply: ${ethers.formatUnits(totalSupply, decimals)} tokens`);
    console.log(`Compliant Tokens: ${ethers.formatUnits(compliantTokens, decimals)} tokens`);
    console.log(`Non-Compliant Tokens: ${ethers.formatUnits(nonCompliantTokens, decimals)} tokens`);
    
    const compliancePercentage = (Number(compliantTokens) / Number(totalSupply)) * 100;
    console.log(`Compliance Rate: ${compliancePercentage.toFixed(1)}%`);
    
    if (compliancePercentage === 100) {
      console.log("🎉 100% COMPLIANCE ACHIEVED!");
    } else {
      console.log(`⚠️ ${100 - compliancePercentage.toFixed(1)}% of tokens still non-compliant`);
    }

  } catch (error) {
    console.error("❌ Emergency whitelisting failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
