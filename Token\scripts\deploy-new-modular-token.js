const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying New Modular Token with KYC Claims Integration...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  // Existing contracts
  const KYC_CLAIMS_MODULE_ADDRESS = "******************************************";
  const UPGRADE_MANAGER_ADDRESS = "******************************************";

  console.log("📋 Using existing contracts:");
  console.log(`- KYCClaimsModule: ${KYC_CLAIMS_MODULE_ADDRESS}`);
  console.log(`- UpgradeManager: ${UPGRADE_MANAGER_ADDRESS}\n`);

  try {
    // Deploy new SecurityTokenCore
    console.log("📦 Deploying new SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    // Deploy as proxy
    const tokenCore = await upgrades.deployProxy(SecurityTokenCore, [
      "Augment Security Token",      // name
      "AST",                        // symbol
      0,                           // decimals
      ethers.parseUnits("1000000", 0), // maxSupply (1M tokens with 0 decimals)
      deployer.address,            // admin
      "1.00 USD",                  // tokenPrice
      "Early: 20%, Standard: 10%", // bonusTiers
      "Modular ERC-3643 security token with KYC Claims integration", // tokenDetails
      "https://example.com/token-logo.png" // tokenImageUrl
    ], {
      initializer: 'initialize',
      kind: 'uups'
    });

    await tokenCore.waitForDeployment();
    const tokenCoreAddress = await tokenCore.getAddress();
    console.log("✅ SecurityTokenCore deployed to:", tokenCoreAddress);

    // Verify initialization
    console.log("\n🔍 Verifying token initialization...");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const decimals = await tokenCore.decimals();
    const maxSupply = await tokenCore.maxSupply();
    const version = await tokenCore.version();
    
    console.log(`✅ Name: ${name}`);
    console.log(`✅ Symbol: ${symbol}`);
    console.log(`✅ Decimals: ${decimals}`);
    console.log(`✅ Max Supply: ${maxSupply.toString()}`);
    console.log(`✅ Version: ${version}`);

    // Check admin roles
    console.log("\n🔑 Verifying admin roles...");
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    
    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);

    // Register KYC Claims Module
    console.log("\n🔗 Registering KYC Claims Module...");
    const KYC_CLAIMS_MODULE_ID = ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"));
    
    const registerTx = await tokenCore.registerModule(KYC_CLAIMS_MODULE_ID, KYC_CLAIMS_MODULE_ADDRESS);
    await registerTx.wait();
    console.log("✅ KYC Claims Module registered successfully!");

    // Verify module registration
    const registeredModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
    const isAuthorized = await tokenCore.isAuthorizedModule(KYC_CLAIMS_MODULE_ADDRESS);
    
    console.log(`✅ Registered module: ${registeredModule}`);
    console.log(`✅ Is authorized: ${isAuthorized}`);

    // Test KYC functionality
    console.log("\n🧪 Testing KYC functionality...");
    const TEST_USER = "******************************************";
    
    try {
      // Test KYC approval
      console.log(`Testing KYC approval for ${TEST_USER}...`);
      const kycTx = await tokenCore.approveKYC(TEST_USER);
      await kycTx.wait();
      console.log("✅ KYC approval successful!");

      // Test whitelist addition
      console.log(`Testing whitelist addition for ${TEST_USER}...`);
      const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
      await whitelistTx.wait();
      console.log("✅ Whitelist addition successful!");

      // Check verification status
      console.log("Checking verification status...");
      const status = await tokenCore.getVerificationStatus(TEST_USER);
      console.log("✅ Verification status:", {
        kycApproved: status[0],
        whitelisted: status[1],
        eligible: status[2],
        method: status[3]
      });

      // Test minting
      console.log(`Testing token minting to ${TEST_USER}...`);
      const mintTx = await tokenCore.mint(TEST_USER, 100);
      await mintTx.wait();
      console.log("✅ Token minting successful!");

      const balance = await tokenCore.balanceOf(TEST_USER);
      console.log(`✅ User balance: ${balance.toString()} tokens`);

    } catch (testError) {
      console.log("⚠️ Some tests failed:", testError.message);
    }

    console.log("\n🎉 New Modular Token deployment completed successfully!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(60));
    console.log(`NEW SecurityTokenCore: ${tokenCoreAddress}`);
    console.log(`KYCClaimsModule: ${KYC_CLAIMS_MODULE_ADDRESS}`);
    console.log(`UpgradeManager: ${UPGRADE_MANAGER_ADDRESS}`);
    console.log(`Module ID: ${KYC_CLAIMS_MODULE_ID}`);
    console.log(`Admin: ${deployer.address}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log("=" .repeat(60));

    console.log("\n💡 NEXT STEPS:");
    console.log("1. Update admin panel .env.local with new SecurityTokenCore address");
    console.log("2. Test KYC & Claims functionality in admin panel");
    console.log("3. All buttons should now work properly!");

    return {
      tokenCoreAddress,
      kycModuleAddress: KYC_CLAIMS_MODULE_ADDRESS,
      upgradeManagerAddress: UPGRADE_MANAGER_ADDRESS,
      admin: deployer.address
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
