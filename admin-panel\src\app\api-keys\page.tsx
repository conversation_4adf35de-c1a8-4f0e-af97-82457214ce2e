'use client';

import React, { useState, useEffect } from 'react';

interface ApiKey {
  keyId: string;
  name: string;
  permissions: string[];
  createdAt: string;
  lastUsed?: string;
  isActive: boolean;
  requestCount?: number;
}

interface NewApiKey {
  apiKey: string;
  name: string;
  permissions: string[];
  createdAt: string;
  usage: {
    endpoint: string;
    header: string;
    example: string;
  };
}

export default function ApiKeysPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [newKeyForm, setNewKeyForm] = useState({
    name: '',
    permissions: ['read']
  });
  const [newlyCreatedKey, setNewlyCreatedKey] = useState<NewApiKey | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Load existing API keys
  const loadApiKeys = async () => {
    try {
      const response = await fetch('/api/auth/generate-key');
      const data = await response.json();
      
      if (data.success) {
        setApiKeys(data.data.keys);
      }
    } catch (error) {
      console.error('Failed to load API keys:', error);
    }
  };

  // Generate new API key
  const generateApiKey = async () => {
    if (!newKeyForm.name.trim()) {
      setError('API key name is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/generate-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newKeyForm)
      });

      const data = await response.json();

      if (data.success) {
        setNewlyCreatedKey(data.data);
        setNewKeyForm({ name: '', permissions: ['read'] });
        await loadApiKeys(); // Refresh the list
      } else {
        setError(data.error || 'Failed to generate API key');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to generate API key');
    } finally {
      setIsLoading(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Test API key
  const testApiKey = async (apiKey: string) => {
    try {
      const response = await fetch('/api/external/tokens', {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });
      
      if (response.ok) {
        alert('API key is working correctly!');
      } else {
        alert(`API key test failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      alert('API key test failed: Network error');
    }
  };

  useEffect(() => {
    loadApiKeys();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">API Key Management</h1>
        <p className="text-gray-600">
          Generate and manage API keys for external integrations
        </p>
      </div>

      {/* Authentication Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-blue-800 mb-4">🔒 API Authentication</h2>
        <div className="space-y-3 text-blue-700">
          <p><strong>Authentication Method:</strong> Bearer Token</p>
          <p><strong>Header Format:</strong> <code className="bg-blue-100 px-2 py-1 rounded">Authorization: Bearer &lt;api_key&gt;</code></p>
          <p><strong>Rate Limit:</strong> 100 requests per minute per API key</p>
          <p><strong>Permissions:</strong> read (basic access), write (full access), admin (all permissions)</p>
        </div>
      </div>

      {/* Generate New API Key */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">Generate New API Key</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key Name *
            </label>
            <input
              type="text"
              value={newKeyForm.name}
              onChange={(e) => setNewKeyForm(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Production Integration, Mobile App, Partner API"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Permissions
            </label>
            <div className="space-y-2">
              {['read', 'write', 'admin'].map((permission) => (
                <label key={permission} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newKeyForm.permissions.includes(permission)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setNewKeyForm(prev => ({
                          ...prev,
                          permissions: [...prev.permissions, permission]
                        }));
                      } else {
                        setNewKeyForm(prev => ({
                          ...prev,
                          permissions: prev.permissions.filter(p => p !== permission)
                        }));
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm">
                    {permission} 
                    <span className="text-gray-500 ml-1">
                      {permission === 'read' && '(Basic token and investor data)'}
                      {permission === 'write' && '(Full access to all data)'}
                      {permission === 'admin' && '(All permissions including management)'}
                    </span>
                  </span>
                </label>
              ))}
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <button
            onClick={generateApiKey}
            disabled={isLoading || !newKeyForm.name.trim()}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {isLoading ? '🔄 Generating...' : '🔑 Generate API Key'}
          </button>
        </div>
      </div>

      {/* Newly Created API Key */}
      {newlyCreatedKey && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-green-800 mb-4">✅ API Key Generated Successfully</h2>
          
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 border">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                API Key (Save this - it won't be shown again!)
              </label>
              <div className="flex items-center gap-2">
                <code className="flex-1 bg-gray-100 px-3 py-2 rounded font-mono text-sm break-all">
                  {newlyCreatedKey.apiKey}
                </code>
                <button
                  onClick={() => copyToClipboard(newlyCreatedKey.apiKey)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm"
                >
                  Copy
                </button>
              </div>
            </div>

            <div className="bg-white rounded-lg p-4 border">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Usage Example
              </label>
              <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm overflow-x-auto">
{newlyCreatedKey.usage.example}
              </pre>
            </div>

            <button
              onClick={() => testApiKey(newlyCreatedKey.apiKey)}
              className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
            >
              🧪 Test API Key
            </button>

            <button
              onClick={() => setNewlyCreatedKey(null)}
              className="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded ml-2"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Existing API Keys */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Existing API Keys</h2>
          <button
            onClick={loadApiKeys}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            🔄 Refresh
          </button>
        </div>

        {apiKeys.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No API keys generated yet</p>
        ) : (
          <div className="space-y-4">
            {apiKeys.map((key, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-semibold">{key.name}</h3>
                    <p className="text-sm text-gray-600">Key ID: {key.keyId}</p>
                    <div className="flex items-center gap-2 mt-2">
                      {key.permissions.map((permission) => (
                        <span
                          key={permission}
                          className={`px-2 py-1 text-xs rounded ${
                            permission === 'admin' ? 'bg-red-100 text-red-800' :
                            permission === 'write' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}
                        >
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    <p>Created: {new Date(key.createdAt).toLocaleDateString()}</p>
                    {key.lastUsed && (
                      <p>Last used: {new Date(key.lastUsed).toLocaleDateString()}</p>
                    )}
                    {key.requestCount !== undefined && (
                      <p>Requests: {key.requestCount}</p>
                    )}
                    <span className={`inline-flex px-2 py-1 text-xs rounded mt-2 ${
                      key.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {key.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* API Documentation Links */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">📚 API Documentation</h2>
        <div className="space-y-2">
          <a
            href="/external-api-docs"
            className="block text-blue-600 hover:text-blue-800 font-medium"
          >
            📖 Complete API Documentation
          </a>
          <a
            href="/api-integration"
            className="block text-blue-600 hover:text-blue-800 font-medium"
          >
            🧪 API Integration Testing
          </a>
        </div>
      </div>
    </div>
  );
}
