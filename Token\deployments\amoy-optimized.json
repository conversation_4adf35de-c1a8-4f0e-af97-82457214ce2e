{"network": "amoy", "chainId": "80002", "factoryAddress": "0xC328C1a84d9110642982C2Fb638244982B26C340", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0x701e447ef637811b15204584ceef52b78b0b3c331505f5ee395311e57022a54a", "timestamp": "2025-06-21T12:10:51.434Z", "contractType": "SecurityTokenFactoryOptimized", "architecture": "Optimized with Built-in Whitelist", "securityLevel": "MAXIMUM", "features": {"builtInWhitelistFunctions": true, "onChainKYCVerification": true, "isWhitelistedFunction": true, "updateWhitelistFunction": true, "batchWhitelistOperations": true, "kycApprovalFunctions": true, "addressFreezingFunctions": true, "agentManagement": true, "emergencyControls": true, "agreementTracking": true, "adminPanelCompatibility": true, "legacyWhitelistCompatibility": true, "sizeOptimized": true}}