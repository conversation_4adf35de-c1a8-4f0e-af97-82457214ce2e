const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 Initializing KYC Claims Module...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Initializing with account:", deployer.address);

  const KYC_MODULE_ADDRESS = "******************************************";
  const CLAIM_REGISTRY_ADDRESS = "******************************************";

  try {
    // Get the deployed module
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    const kycModule = KYCClaimsModule.attach(KYC_MODULE_ADDRESS);

    console.log("📋 Module address:", KYC_MODULE_ADDRESS);
    console.log("📋 Claim registry address:", CLAIM_REGISTRY_ADDRESS);

    // Try to initialize
    console.log("\n🔧 Attempting initialization...");
    const initTx = await kycModule.initialize(
      CLAIM_REGISTRY_ADDRESS,
      deployer.address
    );
    await initTx.wait();
    console.log("✅ Module initialized successfully");

    // Verify initialization
    console.log("\n🔍 Verifying initialization...");
    const claimRegistry = await kycModule.claimRegistry();
    const hasAdminRole = await kycModule.hasRole(await kycModule.DEFAULT_ADMIN_ROLE(), deployer.address);
    const hasModuleAdminRole = await kycModule.hasRole(await kycModule.MODULE_ADMIN_ROLE(), deployer.address);
    const hasClaimIssuerRole = await kycModule.hasRole(await kycModule.CLAIM_ISSUER_ROLE(), deployer.address);
    
    console.log("✅ Claim registry address:", claimRegistry);
    console.log("✅ Has admin role:", hasAdminRole);
    console.log("✅ Has module admin role:", hasModuleAdminRole);
    console.log("✅ Has claim issuer role:", hasClaimIssuerRole);

    // Test configuration with a token
    console.log("\n🧪 Testing token configuration...");
    const TEST_TOKEN_ADDRESS = "0x663b561d089Aa42ED19E2D1e0E16E5B66f9CcbCF"; // FreshTest token
    
    const requiredClaims = [
      10101010000001, // KYC_VERIFICATION
      10101010000004  // GENERAL_QUALIFICATION
    ];
    
    const configTx = await kycModule.configureTokenClaims(
      TEST_TOKEN_ADDRESS,
      requiredClaims,
      true,  // KYC enabled
      true   // Claims enabled
    );
    await configTx.wait();
    console.log("✅ Test token configured with KYC and claims");

    // Test KYC approval
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    console.log(`\n👤 Testing KYC approval for user: ${TEST_USER}`);
    
    const kycTx = await kycModule.approveKYC(TEST_TOKEN_ADDRESS, TEST_USER);
    await kycTx.wait();
    console.log("✅ KYC approved for test user");

    // Check verification status
    const status = await kycModule.getVerificationStatus(TEST_TOKEN_ADDRESS, TEST_USER);
    console.log("✅ Verification status:", {
      kycApproved: status[0],
      whitelisted: status[1],
      eligible: status[2],
      method: status[3]
    });

    console.log("\n🎉 KYC Claims Module initialization completed successfully!");

  } catch (error) {
    console.error("❌ Initialization failed:", error);
    
    if (error.message.includes("Initializable: contract is already initialized")) {
      console.log("\n💡 Module is already initialized. Let's check its current state...");
      
      try {
        const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
        const kycModule = KYCClaimsModule.attach(KYC_MODULE_ADDRESS);
        
        const claimRegistry = await kycModule.claimRegistry();
        console.log("Current claim registry:", claimRegistry);
        
        if (claimRegistry === ethers.ZeroAddress) {
          console.log("❌ Claim registry is not set. This is a problem.");
        } else {
          console.log("✅ Claim registry is properly set.");
        }
      } catch (e) {
        console.log("❌ Could not check module state:", e.message);
      }
    }
  }
}

// Execute initialization
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
