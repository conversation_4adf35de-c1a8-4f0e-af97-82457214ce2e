{"network": "amoy", "chainId": "80002", "factoryAddress": "0x4040a75Cf631922F6D089f83f74cACb76E6b026a", "adminAddress": "0x56f3726C92B8B92a6ab71983886F91718540d888", "deploymentHash": "0xf52c7f7deff86042347c31bb0836268825dbb527036a2ea65410ee0ffbcfb9d6", "timestamp": "2025-06-22T09:24:23.847Z", "contractType": "SecurityTokenFactoryMinimal", "architecture": "Minimal with Essential Whitelist", "securityLevel": "ESSENTIAL", "features": {"essentialWhitelistFunctions": true, "onChainKYCVerification": true, "isWhitelistedFunction": true, "updateWhitelistFunction": true, "batchWhitelistOperations": true, "kycApprovalFunctions": true, "agentManagement": true, "emergencyControls": true, "legacyWhitelistCompatibility": true, "ultraMinimalSize": true, "reliableDeployment": true}}