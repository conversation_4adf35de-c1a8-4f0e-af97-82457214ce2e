import { ethers } from 'ethers';
import { NextRequest, NextResponse } from 'next/server';
import SecurityTokenArtifact from '../../../../contracts/SecurityToken.json';

// Extract ABI from artifact
const SecurityTokenABI = SecurityTokenArtifact.abi;
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';

// Load private key from environment variable - in production, use a proper secrets management system
const PRIVATE_KEY = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
const RPC_URLS = {
  amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology',
  polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',
  unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology', // Default to Amoy for unknown networks
};

// Helper function to execute the command line upgrade script
async function executeUpgradeScript(contractAddress: string, contractType: string, network: string): Promise<string> {
  // Calculate path to the project root (assuming admin-panel is a subdirectory)
  const projectRoot = path.resolve(process.cwd(), '..');
  console.log(`Project root directory: ${projectRoot}`);

  // Create a temporary batch file to execute the command
  const tempBatchPath = path.join(projectRoot, 'temp-upgrade.bat');
  const batchContent = `@echo off
cd Token
set CONTRACT_ADDRESS=${contractAddress}
set CONTRACT_TYPE=${contractType}
npx hardhat run scripts/04-upgrade-contracts.js --network ${network}
`;

  // Write the batch file
  fs.writeFileSync(tempBatchPath, batchContent);
  console.log(`Created temporary batch file at: ${tempBatchPath}`);

  return new Promise((resolve, reject) => {
    console.log(`Executing batch file from directory: ${projectRoot}`);

    exec(tempBatchPath, { cwd: projectRoot }, (error: any, stdout: string, stderr: string) => {
      // Clean up the temporary batch file
      try {
        fs.unlinkSync(tempBatchPath);
        console.log('Temporary batch file deleted');
      } catch (unlinkError) {
        console.error('Error deleting temporary batch file:', unlinkError);
      }

      if (error) {
        console.error(`Error executing upgrade script: ${error.message}`);
        reject(`Error: ${error.message}\n${stderr}`);
        return;
      }
      resolve(stdout);
    });
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { contractAddress, contractType, network = 'amoy' } = body;

    if (!contractAddress || !contractType) {
      return NextResponse.json(
        { error: 'Contract address and type are required' },
        { status: 400 }
      );
    }

    if (!PRIVATE_KEY) {
      return NextResponse.json(
        {
          error: 'CONTRACT_ADMIN_PRIVATE_KEY environment variable is not set',
          details: 'For security reasons, the API requires a secure method to sign transactions.',
          clientSideInstructions: true,
          message: 'The server is not configured with admin credentials. Please use the command-line upgrade script:',
          commandExample: `# Unix/Linux/Mac:\ncd Token\nexport CONTRACT_ADDRESS=${contractAddress}\nexport CONTRACT_TYPE=${contractType}\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows Command Prompt:\ncd Token\nset CONTRACT_ADDRESS=${contractAddress}\nset CONTRACT_TYPE=${contractType}\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}\n\n# Windows PowerShell:\ncd Token\n$env:CONTRACT_ADDRESS="${contractAddress}"\n$env:CONTRACT_TYPE="${contractType}"\nnpx hardhat run scripts/04-upgrade-contracts.js --network ${network === 'unknown' ? 'amoy' : network}`
        },
        { status: 422 }  // 422 Unprocessable Entity is more appropriate than 500 for this case
      );
    }

    // Get RPC URL for the specified network, defaulting to Amoy
    const actualNetwork = network === 'unknown' ? 'amoy' : network;

    try {
      // Execute the command-line upgrade script instead of trying to do it in-process
      console.log(`Executing upgrade script for ${contractType} at ${contractAddress} on ${actualNetwork}`);
      const result = await executeUpgradeScript(contractAddress, contractType, actualNetwork);

      return NextResponse.json({
        success: true,
        message: "Contract upgraded successfully",
        details: result
      });
    } catch (scriptError: any) {
      console.error('Error executing upgrade script:', scriptError);
      return NextResponse.json({
        success: false,
        message: "Failed to upgrade contract",
        error: scriptError.toString()
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error upgrading contract:', error);
    return NextResponse.json(
      { error: error.message || 'An unknown error occurred' },
      { status: 500 }
    );
  }
}