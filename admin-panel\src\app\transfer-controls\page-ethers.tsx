'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ethers } from 'ethers';
import SecurityTokenABI from '../../contracts/SecurityToken.json';

interface TransferControlsState {
  conditionalTransfersEnabled: boolean;
  transferWhitelistEnabled: boolean;
  transferFeesEnabled: boolean;
  transferFeePercentage: number;
  feeCollector: string;
}

interface Token {
  address: string;
  name: string;
  symbol: string;
}

export default function TransferControlsPageEthers() {
  const searchParams = useSearchParams();
  const [selectedToken, setSelectedToken] = useState<string>('');
  const [tokens, setTokens] = useState<Token[]>([]);
  const [controls, setControls] = useState<TransferControlsState>({
    conditionalTransfersEnabled: false,
    transferWhitelistEnabled: false,
    transferFeesEnabled: false,
    transferFeePercentage: 0,
    feeCollector: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [customTokenAddress, setCustomTokenAddress] = useState('');
  const [walletConnected, setWalletConnected] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');

  // Known tokens for demo
  const knownTokens: Token[] = [
    {
      address: "******************************************",
      name: "Advanced Control Token",
      symbol: "ACT"
    },
    {
      address: "******************************************",
      name: "Augment_019",
      symbol: "AUG019"
    },
    {
      address: "******************************************",
      name: "Augment_01z",
      symbol: "AUG01Z"
    }
  ];

  useEffect(() => {
    const initializeTokens = async () => {
      setTokens(knownTokens);

      // Check if token is provided via URL parameter
      const tokenParam = searchParams.get('token');
      if (tokenParam && ethers.isAddress(tokenParam)) {
        setSelectedToken(tokenParam);

        // If the token is not in our known tokens list, add it with fetched info
        if (!knownTokens.find(t => t.address.toLowerCase() === tokenParam.toLowerCase())) {
          let tokenName = `Token ${tokenParam.substring(0, 8)}...`;
          let tokenSymbol = 'UNKNOWN';

          // Try to fetch token info
          try {
            const provider = new ethers.JsonRpcProvider("https://rpc-amoy.polygon.technology");
            const contract = new ethers.Contract(tokenParam, SecurityTokenABI.abi, provider);

            const [name, symbol] = await Promise.all([
              contract.name(),
              contract.symbol()
            ]);

            tokenName = name;
            tokenSymbol = symbol;
          } catch (error) {
            console.warn('Could not fetch token info for URL param, using placeholder:', error);
          }

          const newToken: Token = {
            address: tokenParam,
            name: tokenName,
            symbol: tokenSymbol
          };
          setTokens(prev => [newToken, ...prev]);
        }
      } else if (knownTokens.length > 0) {
        setSelectedToken(knownTokens[0].address);
      }
    };

    initializeTokens();
    // Check wallet connection
    checkWalletConnection();
  }, [searchParams]);

  useEffect(() => {
    if (selectedToken && walletConnected) {
      loadTransferControls();
    }
  }, [selectedToken, walletConnected]);

  const checkWalletConnection = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        const accounts = await provider.listAccounts();
        if (accounts.length > 0) {
          setWalletConnected(true);
          setWalletAddress(accounts[0].address);
        }
      }
    } catch (error) {
      console.error('Error checking wallet connection:', error);
    }
  };

  const connectWallet = async () => {
    try {
      if (window.ethereum) {
        const provider = new ethers.BrowserProvider(window.ethereum);
        await provider.send("eth_requestAccounts", []);
        const signer = await provider.getSigner();
        const address = await signer.getAddress();
        setWalletConnected(true);
        setWalletAddress(address);
      } else {
        setMessage("Please install MetaMask to use this feature!");
      }
    } catch (error) {
      console.error("Error connecting wallet:", error);
      setMessage("Error connecting wallet");
    }
  };

  const disconnectWallet = () => {
    setWalletConnected(false);
    setWalletAddress('');
  };

  const loadTransferControls = async () => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      setMessage('');
      const provider = new ethers.BrowserProvider(window.ethereum);
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, provider);

      // Check if the contract supports the new transfer control functions
      try {
        const [
          conditionalTransfersEnabled,
          transferWhitelistEnabled,
          transferFeesEnabled,
          feeConfig
        ] = await Promise.all([
          contract.conditionalTransfersEnabled(),
          contract.transferWhitelistEnabled(),
          contract.transferFeesEnabled(),
          contract.getTransferFeeConfig()
        ]);

        setControls({
          conditionalTransfersEnabled,
          transferWhitelistEnabled,
          transferFeesEnabled,
          transferFeePercentage: Number(feeConfig[0]),
          feeCollector: feeConfig[1]
        });
      } catch (contractError) {
        console.warn('Contract does not support advanced transfer controls:', contractError);
        setMessage('This token contract does not support advanced transfer controls. Please upgrade the contract or select a different token.');
        setControls({
          conditionalTransfersEnabled: false,
          transferWhitelistEnabled: false,
          transferFeesEnabled: false,
          transferFeePercentage: 0,
          feeCollector: ''
        });
      }
    } catch (error) {
      console.error('Error loading transfer controls:', error);
      setMessage('Error connecting to token contract. Please check the token address and try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateConditionalTransfers = async (enabled: boolean) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setConditionalTransfers(enabled);
      await tx.wait();

      setControls(prev => ({ ...prev, conditionalTransfersEnabled: enabled }));
      setMessage(`Conditional transfers ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating conditional transfers:', error);
      setMessage('Error updating conditional transfers');
    } finally {
      setLoading(false);
    }
  };

  const updateTransferWhitelist = async (enabled: boolean) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setTransferWhitelist(enabled);
      await tx.wait();

      setControls(prev => ({ ...prev, transferWhitelistEnabled: enabled }));
      setMessage(`Transfer whitelisting ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating transfer whitelist:', error);
      setMessage('Error updating transfer whitelist');
    } finally {
      setLoading(false);
    }
  };

  const updateTransferFees = async (enabled: boolean, feePercentage: number, feeCollector: string) => {
    if (!selectedToken || !window.ethereum) return;

    try {
      setLoading(true);
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const contract = new ethers.Contract(selectedToken, SecurityTokenABI.abi, signer);

      const tx = await contract.setTransferFees(enabled, feePercentage, feeCollector);
      await tx.wait();

      setControls(prev => ({
        ...prev,
        transferFeesEnabled: enabled,
        transferFeePercentage: feePercentage,
        feeCollector: feeCollector
      }));
      setMessage(`Transfer fees ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error updating transfer fees:', error);
      setMessage('Error updating transfer fees');
    } finally {
      setLoading(false);
    }
  };

  const addCustomToken = () => {
    if (!customTokenAddress || !ethers.isAddress(customTokenAddress)) {
      setMessage('Please enter a valid token address');
      return;
    }

    // Check if token already exists
    if (tokens.find(t => t.address.toLowerCase() === customTokenAddress.toLowerCase())) {
      setMessage('Token already exists in the list');
      return;
    }

    const newToken: Token = {
      address: customTokenAddress,
      name: `Token ${customTokenAddress.substring(0, 8)}...`,
      symbol: 'CUSTOM'
    };

    setTokens(prev => [newToken, ...prev]);
    setSelectedToken(customTokenAddress);
    setCustomTokenAddress('');
    setMessage('Token added successfully');
  };

  if (!walletConnected) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Transfer Controls</h1>
          <p className="text-gray-600 mb-6">Please connect your wallet to manage transfer controls.</p>

          <button
            onClick={connectWallet}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md font-medium mb-4"
          >
            Connect Wallet
          </button>

          <div className="mt-6">
            <Link href="/" className="text-blue-600 hover:text-blue-800 text-sm">
              ← Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Navigation */}
        <div className="mb-6 flex items-center space-x-4">
          <Link href="/" className="text-blue-600 hover:text-blue-800 flex items-center">
            ← Back to Dashboard
          </Link>
          {selectedToken && (
            <Link
              href={`/tokens/${selectedToken}`}
              className="text-gray-600 hover:text-gray-800 flex items-center text-sm"
            >
              View Token Details
            </Link>
          )}
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Advanced Transfer Controls</h1>
                <p className="mt-1 text-sm text-gray-600">
                  Configure conditional transfers, transfer whitelisting, and transfer fees for your security tokens.
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  These controls are applied per token and work in addition to the basic whitelist and freeze functionality.
                </p>
              </div>

              {/* Wallet Info */}
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Connected: <span className="font-mono text-xs">{walletAddress?.substring(0, 6)}...{walletAddress?.substring(-4)}</span>
                </div>
                <button
                  onClick={disconnectWallet}
                  className="mt-1 text-xs text-red-600 hover:text-red-800"
                >
                  Disconnect
                </button>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Token Selection */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Token
              </label>
              <select
                value={selectedToken}
                onChange={(e) => setSelectedToken(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-2"
              >
                {tokens.map((token, index) => (
                  <option key={`${token.address}-${index}`} value={token.address}>
                    {token.name} ({token.symbol}) - {token.address.substring(0, 10)}...
                  </option>
                ))}
              </select>
              {selectedToken && (
                <div className="text-xs text-gray-600">
                  <p><strong>Full Address:</strong> {selectedToken}</p>
                  <p className="mt-1">Transfer controls are configured individually for each token.</p>
                </div>
              )}

              {/* Add Custom Token */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <label className="block text-xs font-medium text-gray-700 mb-2">
                  Add Custom Token Address
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={customTokenAddress}
                    onChange={(e) => setCustomTokenAddress(e.target.value)}
                    placeholder="0x..."
                    className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={addCustomToken}
                    disabled={loading}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    Add
                  </button>
                </div>
              </div>
            </div>

            {/* Status Message */}
            {message && (
              <div className="mb-6 p-4 rounded-md bg-blue-50 border border-blue-200">
                <p className="text-sm text-blue-800">{message}</p>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="mb-6 p-4 rounded-md bg-gray-50 border border-gray-200">
                <p className="text-sm text-gray-600">Processing transaction...</p>
              </div>
            )}

            {/* Current Status Summary */}
            {selectedToken && !loading && (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Current Transfer Control Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                  <div>
                    <span className="font-medium">Conditional Transfers:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.conditionalTransfersEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.conditionalTransfersEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Transfer Whitelist:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.transferWhitelistEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.transferWhitelistEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Transfer Fees:</span>
                    <span className={`ml-2 px-2 py-1 rounded ${controls.transferFeesEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {controls.transferFeesEnabled ? `${controls.transferFeePercentage / 100}%` : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Transfer Controls */}
            <div className="space-y-8">
              {/* Conditional Transfers */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Conditional Transfers</h3>
                    <p className="text-sm text-gray-600">
                      Require approval for all transfers between investors
                    </p>
                  </div>
                  <button
                    onClick={() => updateConditionalTransfers(!controls.conditionalTransfersEnabled)}
                    disabled={loading}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      controls.conditionalTransfersEnabled
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    } disabled:opacity-50`}
                  >
                    {controls.conditionalTransfersEnabled ? 'Disable' : 'Enable'}
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  Status: {controls.conditionalTransfersEnabled ?
                    <span className="text-green-600 font-medium">Enabled</span> :
                    <span className="text-red-600 font-medium">Disabled</span>
                  }
                </div>
              </div>

              {/* Transfer Whitelisting */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">Transfer Whitelisting</h3>
                    <p className="text-sm text-gray-600">
                      Only allow whitelisted addresses to initiate transfers
                    </p>
                  </div>
                  <button
                    onClick={() => updateTransferWhitelist(!controls.transferWhitelistEnabled)}
                    disabled={loading}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      controls.transferWhitelistEnabled
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    } disabled:opacity-50`}
                  >
                    {controls.transferWhitelistEnabled ? 'Disable' : 'Enable'}
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  Status: {controls.transferWhitelistEnabled ?
                    <span className="text-green-600 font-medium">Enabled</span> :
                    <span className="text-red-600 font-medium">Disabled</span>
                  }
                </div>
              </div>

              {/* Transfer Fees - Simplified version */}
              <div className="border border-gray-200 rounded-lg p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Transfer Fees</h3>
                  <p className="text-sm text-gray-600">
                    Collect a percentage fee on transfers to a designated collector wallet
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fee Percentage (basis points, 100 = 1%)
                    </label>
                    <input
                      type="number"
                      value={controls.transferFeePercentage}
                      onChange={(e) => setControls(prev => ({ ...prev, transferFeePercentage: Number(e.target.value) }))}
                      min="0"
                      max="10000"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="100"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Fee Collector Address
                    </label>
                    <input
                      type="text"
                      value={controls.feeCollector}
                      onChange={(e) => setControls(prev => ({ ...prev, feeCollector: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="0x..."
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      Status: {controls.transferFeesEnabled ?
                        <span className="text-green-600 font-medium">Enabled ({controls.transferFeePercentage / 100}%)</span> :
                        <span className="text-red-600 font-medium">Disabled</span>
                      }
                    </div>
                    <button
                      onClick={() => updateTransferFees(!controls.transferFeesEnabled, controls.transferFeePercentage, controls.feeCollector)}
                      disabled={loading}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        controls.transferFeesEnabled
                          ? 'bg-red-600 text-white hover:bg-red-700'
                          : 'bg-green-600 text-white hover:bg-green-700'
                      } disabled:opacity-50`}
                    >
                      {controls.transferFeesEnabled ? 'Disable' : 'Enable'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
