# 🏗️ MODULAR TOKEN ARCHITECTURE SPECIFICATION

## Overview
This document specifies our modular token architecture designed to overcome the 24.576 KiB contract size limit while maintaining ALL functionality.

## Current Situation
- **SecurityToken**: 24.562 KiB (EXCEEDS 24.576 KiB limit by -14 bytes)
- **Target**: Split into 6 specialized modules, each <15 KiB

## 🎯 MODULAR DESIGN

### Module 1: SecurityTokenCore (~15-18 KiB)
**Responsibility**: Core ERC20 functionality + basic compliance hooks
**Size Target**: <18 KiB

**Features**:
- ERC20 base functionality (transfer, approve, etc.)
- ERC20Burnable (burn, burnFrom)
- Basic pausable functionality
- UUPS upgradeable
- Module registry and communication
- Basic initialization

**Removed Features** (delegated to other modules):
- Agent management → AgentManager
- Complex transfer controls → TransferController  
- Emergency controls → EmergencyManager
- Agreement management → ComplianceEngine
- Whitelist delegation → IdentityManager

### Module 2: IdentityManager (~10-12 KiB)
**Responsibility**: Identity, KYC, and whitelist management
**Size Target**: <12 KiB

**Features**:
- ONCHAINID integration
- KYC data storage and verification
- Whitelist management (add, remove, batch operations)
- Investor verification
- Country restrictions
- Freeze/unfreeze functionality
- Identity-based compliance checks

**Interface**:
```solidity
interface IIdentityManager {
    function isVerified(address account) external view returns (bool);
    function isWhitelisted(address account) external view returns (bool);
    function isFrozen(address account) external view returns (bool);
    function canTransfer(address from, address to) external view returns (bool);
    function addToWhitelist(address account) external;
    function removeFromWhitelist(address account) external;
    function freezeAddress(address account) external;
    function unfreezeAddress(address account) external;
}
```

### Module 3: ComplianceEngine (~10-12 KiB)
**Responsibility**: Advanced compliance rules and agreement management
**Size Target**: <12 KiB

**Features**:
- Complex compliance rules (holder limits, country restrictions)
- Transfer validation logic
- Agreement acceptance tracking
- Compliance rule management
- Regulatory reporting hooks
- Custom compliance logic

**Interface**:
```solidity
interface IComplianceEngine {
    function canTransfer(address from, address to, uint256 amount) external view returns (bool);
    function transferred(address from, address to, uint256 amount) external;
    function destroyed(address from, uint256 amount) external;
    function hasAcceptedAgreement(address account) external view returns (bool);
    function acceptAgreement() external;
}
```

### Module 4: TransferController (~8-10 KiB)
**Responsibility**: Advanced transfer controls and force transfers
**Size Target**: <10 KiB

**Features**:
- Force transfer functionality
- Conditional transfers (approval flow)
- Transfer whitelisting
- Transfer fees and collection
- Transfer restrictions
- Omnibus account support for DEX

**Interface**:
```solidity
interface ITransferController {
    function forcedTransfer(address from, address to, uint256 amount) external returns (bool);
    function enableConditionalTransfers(bool enabled) external;
    function approveTransfer(address from, address to, uint256 amount) external;
    function executeApprovedTransfer(address from, address to, uint256 amount) external;
    function setTransferFees(uint256 feePercentage, address collector) external;
}
```

### Module 5: AgentManager (~6-8 KiB)
**Responsibility**: Agent role management and tracking
**Size Target**: <8 KiB

**Features**:
- Agent role assignment and revocation
- Agent list management and enumeration
- Agent permission validation
- Role-based access control delegation
- Agent activity tracking

**Interface**:
```solidity
interface IAgentManager {
    function addAgent(address agent) external;
    function removeAgent(address agent) external;
    function isAgent(address account) external view returns (bool);
    function getAgentCount() external view returns (uint256);
    function getAllAgents() external view returns (address[] memory);
}
```

### Module 6: EmergencyManager (~6-8 KiB)
**Responsibility**: Emergency controls and system-wide pausing
**Size Target**: <8 KiB

**Features**:
- Global pause/unpause
- Function-level pausing
- Emergency stop mechanisms
- Recovery procedures
- Time-locked emergency actions
- Circuit breaker patterns

**Interface**:
```solidity
interface IEmergencyManager {
    function emergencyPause() external;
    function emergencyUnpause() external;
    function pauseFunction(bytes4 selector) external;
    function unpauseFunction(bytes4 selector) external;
    function isEmergencyPaused() external view returns (bool);
    function isFunctionPaused(bytes4 selector) external view returns (bool);
}
```

## 🔗 INTER-MODULE COMMUNICATION

### Communication Pattern
- **Hub-Spoke Model**: SecurityTokenCore acts as the central hub
- **Interface-Based**: All modules communicate through well-defined interfaces
- **Event-Driven**: Modules emit events for cross-module notifications
- **Delegated Calls**: Core token delegates specific operations to modules

### Module Registry
```solidity
contract ModuleRegistry {
    mapping(bytes32 => address) public modules;
    
    function setModule(bytes32 moduleId, address moduleAddress) external;
    function getModule(bytes32 moduleId) external view returns (address);
}
```

### Security Model
- **Access Control**: Each module has its own access control
- **Cross-Module Auth**: Modules can validate calls from other modules
- **Upgrade Safety**: Individual module upgrades with compatibility checks

## 📦 DEPLOYMENT STRATEGY

### Factory Enhancement
The SecurityTokenFactory will be enhanced to:
1. Deploy all 6 modules as separate proxy contracts
2. Initialize each module with proper configuration
3. Link modules together through the registry
4. Configure inter-module permissions
5. Enable/disable features based on deployment requirements

### Upgrade Strategy
- **Independent Upgrades**: Each module can be upgraded separately
- **Coordinated Upgrades**: Multi-module upgrades when needed
- **Backward Compatibility**: Maintain interface compatibility
- **State Migration**: Handle state migration between versions

## 🎛️ CONFIGURATION OPTIONS

### Deployment Configurations
1. **Minimal**: Core + Identity + Compliance (basic functionality)
2. **Standard**: All modules except advanced transfer controls
3. **Enterprise**: All modules with full feature set
4. **Custom**: Pick and choose specific modules

### Feature Toggles
- Each module can be enabled/disabled at deployment
- Runtime feature toggling for supported functions
- Granular control over functionality

## 📊 SIZE PROJECTIONS

| Module | Estimated Size | Features |
|--------|---------------|----------|
| SecurityTokenCore | 15-18 KiB | ERC20 + basic compliance |
| IdentityManager | 10-12 KiB | KYC + whitelist + freeze |
| ComplianceEngine | 10-12 KiB | Rules + agreements |
| TransferController | 8-10 KiB | Force transfer + controls |
| AgentManager | 6-8 KiB | Agent management |
| EmergencyManager | 6-8 KiB | Emergency controls |
| **Total** | **55-68 KiB** | **All features** |

## ✅ BENEFITS

1. **No Size Limits**: Each module well under 24.576 KiB limit
2. **Full Functionality**: ALL current features preserved
3. **Upgradeable**: Independent module upgrades
4. **Configurable**: Deploy only needed modules
5. **Maintainable**: Clear separation of concerns
6. **Extensible**: Easy to add new modules
7. **Gas Efficient**: Only load needed functionality

## 🚀 NEXT STEPS

1. Implement SecurityTokenCore (Phase 2, Task 1)
2. Implement IdentityManager (Phase 2, Task 2)
3. Implement ComplianceEngine (Phase 2, Task 3)
4. Implement TransferController (Phase 2, Task 4)
5. Implement AgentManager (Phase 2, Task 5)
6. Implement EmergencyManager (Phase 2, Task 6)
7. Enhance Factory for modular deployment (Phase 3)
8. Comprehensive testing and integration (Phase 5)
