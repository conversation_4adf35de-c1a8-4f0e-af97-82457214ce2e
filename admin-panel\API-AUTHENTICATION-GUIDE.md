# 🔒 API Authentication Guide

## 📋 Overview

The ERC-3643 Security Token System provides secure API access through Bear<PERSON> token authentication with API keys. This guide covers how to authenticate with our APIs and manage access permissions.

## 🔑 Authentication Methods

### **Bearer Token Authentication**

All external API endpoints use Bearer token authentication:

```bash
Authorization: Bearer <api_key>
```

**Example:**
```bash
curl -H "Authorization: Bearer ak_your_api_key_here" \
  https://your-domain.com/api/external/tokens
```

## 🚀 Getting Started

### **1. Generate an API Key**

1. Navigate to the [API Keys Management](/api-keys) page
2. Click "Generate API Key"
3. Provide a descriptive name (e.g., "Production Integration", "Mobile App")
4. Select appropriate permissions
5. Save the generated API key securely

### **2. Use the API Key**

Include the API key in the `Authorization` header of your requests:

```javascript
// JavaScript/Node.js
const response = await fetch('https://your-domain.com/api/external/tokens', {
  headers: {
    'Authorization': 'Bearer ak_your_api_key_here',
    'Content-Type': 'application/json'
  }
});
```

```python
# Python
import requests

headers = {
    'Authorization': 'Bearer ak_your_api_key_here',
    'Content-Type': 'application/json'
}

response = requests.get('https://your-domain.com/api/external/tokens', headers=headers)
```

```bash
# cURL
curl -X GET "https://your-domain.com/api/external/tokens" \
  -H "Authorization: Bearer ak_your_api_key_here" \
  -H "Content-Type: application/json"
```

## 🔐 Permission Levels

### **Read Permission**
- Access to basic token and investor information
- Suitable for public integrations and dashboards
- Rate limited to 100 requests per minute

### **Write Permission**
- Full access to all data including detailed statistics
- Access to sensitive investor information
- Suitable for administrative integrations

### **Admin Permission**
- All permissions including API key management
- Access to system administration functions
- Suitable for internal tools and management systems

## 📊 Public vs Authenticated Access

### **Public Access (No Authentication)**
```bash
GET /api/external/tokens
# Returns basic token information
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tokens": [
      {
        "address": "0x...",
        "name": "Token Name",
        "symbol": "TKN",
        "totalSupply": "1000000",
        "isPaused": false
      }
    ]
  }
}
```

### **Authenticated Access (With API Key)**
```bash
GET /api/external/tokens?detailed=true
Authorization: Bearer ak_your_api_key_here
# Returns comprehensive token information
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tokens": [
      {
        "address": "0x...",
        "name": "Token Name",
        "symbol": "TKN",
        "totalSupply": "1000000",
        "isPaused": false,
        "issuer": {
          "name": "Company Name",
          "address": "0x...",
          "website": "https://..."
        },
        "compliance": {
          "kycRequired": true,
          "whitelistEnabled": true
        },
        "statistics": {
          "totalTransactions": 150,
          "activeInvestors": 25
        }
      }
    ]
  }
}
```

## ⚡ Rate Limiting

- **Rate Limit:** 100 requests per minute per API key
- **Window:** 60 seconds rolling window
- **Headers:** Rate limit information included in response headers

**Rate Limit Headers:**
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## ❌ Error Responses

### **401 Unauthorized**
```json
{
  "success": false,
  "error": "Authentication required",
  "message": "API key required for detailed data",
  "code": "AUTH_REQUIRED",
  "hint": "Add Authorization: Bearer <api_key> header"
}
```

### **403 Forbidden**
```json
{
  "success": false,
  "error": "Insufficient permissions",
  "message": "API key does not have read permission",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

### **429 Too Many Requests**
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "message": "Maximum 100 requests per minute",
  "code": "RATE_LIMIT_EXCEEDED"
}
```

## 🔧 API Key Management

### **Generate New API Key**
```bash
POST /api/auth/generate-key
Content-Type: application/json

{
  "name": "Production Integration",
  "permissions": ["read", "write"]
}
```

### **List API Keys**
```bash
GET /api/auth/generate-key
```

### **API Key Format**
- **Prefix:** `ak_` (API Key)
- **Length:** 64 characters
- **Example:** `ak_1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef`

## 🛡️ Security Best Practices

### **API Key Security**
1. **Store Securely:** Never commit API keys to version control
2. **Environment Variables:** Use environment variables for API keys
3. **Rotate Regularly:** Generate new keys periodically
4. **Minimum Permissions:** Use the least privileged permission level
5. **Monitor Usage:** Track API key usage and activity

### **Request Security**
1. **HTTPS Only:** Always use HTTPS in production
2. **Validate Responses:** Check response status and structure
3. **Handle Errors:** Implement proper error handling
4. **Retry Logic:** Implement exponential backoff for retries

## 📚 Integration Examples

### **Node.js/Express Integration**
```javascript
const express = require('express');
const fetch = require('node-fetch');

const app = express();
const API_KEY = process.env.TOKEN_API_KEY;

app.get('/tokens', async (req, res) => {
  try {
    const response = await fetch('https://your-domain.com/api/external/tokens?detailed=true', {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch tokens' });
  }
});
```

### **Python/Django Integration**
```python
import os
import requests
from django.http import JsonResponse

def get_tokens(request):
    api_key = os.environ.get('TOKEN_API_KEY')
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(
            'https://your-domain.com/api/external/tokens?detailed=true',
            headers=headers
        )
        response.raise_for_status()
        return JsonResponse(response.json())
    except requests.RequestException as e:
        return JsonResponse({'error': 'Failed to fetch tokens'}, status=500)
```

## 🔗 Related Resources

- **[API Keys Management](/api-keys)** - Generate and manage API keys
- **[External API Documentation](/external-api-docs)** - Complete API reference
- **[API Integration Testing](/api-integration)** - Test API endpoints
- **[Deployment Dashboard](/deployment-dashboard)** - System monitoring

## 📞 Support

For API authentication issues or questions:
1. Check the [API Integration Testing](/api-integration) page
2. Review error responses and status codes
3. Verify API key permissions and rate limits
4. Contact support with specific error messages

**The API authentication system provides secure, scalable access to the ERC-3643 Security Token System with comprehensive permission management and rate limiting.**
