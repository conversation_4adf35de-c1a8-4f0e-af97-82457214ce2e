const { ethers } = require("hardhat");

// Token address to test
const TOKEN_ADDRESS = "******************************************";

async function main() {
  console.log("🧪 TESTING WHITELIST API ENDPOINT");
  console.log("Token Address:", TOKEN_ADDRESS);

  try {
    // Test the API endpoint
    console.log("\n🌐 Testing API endpoint...");
    
    const response = await fetch(`http://localhost:6677/api/contracts/token/whitelist?tokenAddress=${TOKEN_ADDRESS}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log("✅ API Response:", JSON.stringify(data, null, 2));
      
      if (data.success && data.whitelistedAddresses) {
        console.log(`\n📊 Summary:`);
        console.log(`  Total addresses: ${data.totalAddresses}`);
        console.log(`  Method used: ${data.method}`);
        
        for (const addr of data.whitelistedAddresses) {
          console.log(`\n  Address: ${addr.address}`);
          console.log(`    Balance: ${addr.balance} tokens`);
          console.log(`    Whitelisted: ${addr.isWhitelisted}`);
          console.log(`    Verified: ${addr.isVerified}`);
        }
        
        if (data.totalAddresses >= 2) {
          console.log("\n✅ SUCCESS: API returns the expected 2+ whitelisted addresses!");
        } else {
          console.log("\n❌ ISSUE: API should return 2+ addresses but only returned", data.totalAddresses);
        }
      } else {
        console.log("❌ API returned success=false or no addresses");
      }
    } else {
      console.log("❌ API request failed:", response.status, response.statusText);
      const errorText = await response.text();
      console.log("Error details:", errorText);
    }

    // Also test direct contract calls for comparison
    console.log("\n🔍 Direct contract verification:");
    
    const [deployer] = await ethers.getSigners();
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);
    
    const testAddresses = [
      "******************************************",
      "******************************************"
    ];
    
    for (const address of testAddresses) {
      const isWhitelisted = await tokenContract.isWhitelisted(address);
      const isVerified = await tokenContract.isVerified(address);
      const balance = await tokenContract.balanceOf(address);
      const decimals = await tokenContract.decimals();
      
      console.log(`\n  ${address}:`);
      console.log(`    Contract says whitelisted: ${isWhitelisted}`);
      console.log(`    Contract says verified: ${isVerified}`);
      console.log(`    Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
    }

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
