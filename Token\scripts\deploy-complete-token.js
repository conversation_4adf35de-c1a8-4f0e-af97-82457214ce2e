const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Complete SecurityTokenCore with All Admin Functions...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  try {
    // Deploy complete SecurityTokenCore with all admin functions
    console.log("📦 Deploying complete SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const tokenCore = await upgrades.deployProxy(SecurityTokenCore, [
      "Augment Complete Security Token",  // name
      "ACST",                            // symbol
      0,                                // decimals (whole tokens)
      ethers.parseUnits("********", 0), // maxSupply (25M tokens)
      deployer.address,                 // admin
      "5.00 USD",                       // tokenPrice
      "Early Bird: 40%, Standard: 25%, Late: 15%", // bonusTiers
      "Complete ERC-3643 security token with full admin controls: price updates, metadata management, freeze/unfreeze, pause/unpause, force transfers, KYC Claims integration, and on-chain compliance verification", // tokenDetails
      "https://augment.finance/assets/complete-token-logo.png" // tokenImageUrl
    ], {
      initializer: 'initialize',
      kind: 'uups'
    });

    await tokenCore.waitForDeployment();
    const tokenCoreAddress = await tokenCore.getAddress();
    console.log("✅ Complete SecurityTokenCore deployed to:", tokenCoreAddress);

    // Verify token configuration
    console.log("\n🔍 Verifying token configuration...");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const decimals = await tokenCore.decimals();
    const maxSupply = await tokenCore.maxSupply();
    const version = await tokenCore.version();
    const paused = await tokenCore.paused();
    
    console.log(`✅ Name: ${name}`);
    console.log(`✅ Symbol: ${symbol}`);
    console.log(`✅ Decimals: ${decimals}`);
    console.log(`✅ Max Supply: ${maxSupply.toString()}`);
    console.log(`✅ Version: ${version}`);
    console.log(`✅ Paused: ${paused}`);

    // Check metadata
    console.log("\n📊 Checking token metadata...");
    const metadata = await tokenCore.getTokenMetadata();
    console.log("✅ Token metadata:", {
      tokenPrice: metadata[0],
      bonusTiers: metadata[1],
      tokenDetails: metadata[2],
      tokenImageUrl: metadata[3]
    });

    // Verify admin roles
    console.log("\n🔑 Verifying admin roles...");
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    const TRANSFER_MANAGER_ROLE = await tokenCore.TRANSFER_MANAGER_ROLE();
    
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    const hasTransferManager = await tokenCore.hasRole(TRANSFER_MANAGER_ROLE, deployer.address);
    
    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);
    console.log(`✅ TRANSFER_MANAGER_ROLE: ${hasTransferManager}`);

    // Test new admin functions
    console.log("\n🧪 Testing new admin functions...");
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    
    try {
      // Test price update
      console.log("Testing price update...");
      const priceUpdateTx = await tokenCore.updateTokenPrice("6.25 USD");
      await priceUpdateTx.wait();
      console.log("✅ Price update successful!");

      // Test bonus tiers update
      console.log("Testing bonus tiers update...");
      const bonusUpdateTx = await tokenCore.updateBonusTiers("Super Early: 50%, Early: 35%, Standard: 20%, Late: 10%");
      await bonusUpdateTx.wait();
      console.log("✅ Bonus tiers update successful!");

      // Test max supply update
      console.log("Testing max supply update...");
      const maxSupplyUpdateTx = await tokenCore.updateMaxSupply(ethers.parseUnits("50000000", 0));
      await maxSupplyUpdateTx.wait();
      console.log("✅ Max supply update successful!");

      // Verify updates
      const updatedMetadata = await tokenCore.getTokenMetadata();
      const updatedMaxSupply = await tokenCore.maxSupply();
      console.log("✅ Updated metadata:", {
        tokenPrice: updatedMetadata[0],
        bonusTiers: updatedMetadata[1],
        maxSupply: updatedMaxSupply.toString()
      });

      // Test pause functionality
      console.log("Testing pause functionality...");
      const pauseTx = await tokenCore.pause();
      await pauseTx.wait();
      const isPaused = await tokenCore.paused();
      console.log(`✅ Token paused: ${isPaused}`);

      const unpauseTx = await tokenCore.unpause();
      await unpauseTx.wait();
      const isUnpaused = await tokenCore.paused();
      console.log(`✅ Token unpaused: ${!isUnpaused}`);

      // Test whitelist functionality (if modules are available)
      console.log("Testing whitelist functionality...");
      try {
        const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
        await whitelistTx.wait();
        console.log("✅ Whitelist addition successful!");

        const isWhitelisted = await tokenCore.isWhitelisted(TEST_USER);
        console.log(`✅ User whitelisted: ${isWhitelisted}`);
      } catch (whitelistError) {
        console.log("⚠️ Whitelist test skipped (module not registered):", whitelistError.message);
      }

      // Test minting
      console.log("Testing token minting...");
      try {
        const mintTx = await tokenCore.mint(TEST_USER, 500);
        await mintTx.wait();
        console.log("✅ Token minting successful!");

        const balance = await tokenCore.balanceOf(TEST_USER);
        const totalSupply = await tokenCore.totalSupply();
        console.log(`✅ User balance: ${balance.toString()} tokens`);
        console.log(`✅ Total supply: ${totalSupply.toString()} tokens`);
      } catch (mintError) {
        console.log("⚠️ Minting test failed (expected if modules not configured):", mintError.message);
      }

    } catch (testError) {
      console.log("❌ Some admin function tests failed:", testError.message);
    }

    console.log("\n🎉 Complete SecurityTokenCore deployment successful!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(70));
    console.log(`Complete SecurityTokenCore: ${tokenCoreAddress}`);
    console.log(`Name: ${name}`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Decimals: ${decimals}`);
    console.log(`Max Supply: ${updatedMaxSupply ? updatedMaxSupply.toString() : maxSupply.toString()}`);
    console.log(`Admin: ${deployer.address}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log("=" .repeat(70));

    console.log("\n🎯 AVAILABLE ADMIN FUNCTIONS:");
    console.log("✅ updateTokenPrice(string) - Update token price");
    console.log("✅ updateBonusTiers(string) - Update bonus tiers");
    console.log("✅ updateTokenMetadata(string,string,string) - Update all metadata");
    console.log("✅ updateTokenImageUrl(string) - Update token image");
    console.log("✅ updateMaxSupply(uint256) - Update maximum supply");
    console.log("✅ pause() / unpause() - Pause/unpause transfers");
    console.log("✅ mint(address,uint256) - Mint tokens");
    console.log("✅ burn(uint256) - Burn tokens");
    console.log("✅ addToWhitelist(address) - Add to whitelist");
    console.log("✅ removeFromWhitelist(address) - Remove from whitelist");
    console.log("✅ freezeAddress(address) - Freeze address");
    console.log("✅ unfreezeAddress(address) - Unfreeze address");
    console.log("✅ forcedTransfer(address,address,uint256) - Force transfer");

    console.log("\n💡 NEXT STEPS:");
    console.log("1. Update admin panel .env.local with new token address:");
    console.log(`   AMOY_SECURITY_TOKEN_CORE_ADDRESS=${tokenCoreAddress}`);
    console.log("2. Add admin functions to admin panel UI");
    console.log("3. Test all functionality including price updates!");

    return {
      tokenCoreAddress,
      name,
      symbol,
      decimals: decimals.toString(),
      maxSupply: (updatedMaxSupply || maxSupply).toString(),
      admin: deployer.address
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
