const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Fresh KYC Claims Module...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);

  const CLAIM_REGISTRY_ADDRESS = "******************************************";
  const NEW_TOKEN_ADDRESS = "******************************************";

  try {
    // Deploy fresh KYCClaimsModule
    console.log("📦 Deploying fresh KYCClaimsModule...");
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    
    const kycModule = await KYCClaimsModule.deploy();
    await kycModule.waitForDeployment();
    
    const kycModuleAddress = await kycModule.getAddress();
    console.log("✅ Fresh KYCClaimsModule deployed to:", kycModuleAddress);

    // Initialize the module properly
    console.log("\n🔧 Initializing KYCClaimsModule...");
    const initTx = await kycModule.initialize(
      CLAIM_REGISTRY_ADDRESS,
      deployer.address
    );
    await initTx.wait();
    console.log("✅ KYCClaimsModule initialized successfully!");

    // Verify initialization
    console.log("\n🔍 Verifying initialization...");
    const claimRegistry = await kycModule.claimRegistry();
    const hasAdminRole = await kycModule.hasRole(await kycModule.DEFAULT_ADMIN_ROLE(), deployer.address);
    const hasModuleAdminRole = await kycModule.hasRole(await kycModule.MODULE_ADMIN_ROLE(), deployer.address);
    const hasClaimIssuerRole = await kycModule.hasRole(await kycModule.CLAIM_ISSUER_ROLE(), deployer.address);
    
    console.log("✅ Claim registry address:", claimRegistry);
    console.log("✅ Has admin role:", hasAdminRole);
    console.log("✅ Has module admin role:", hasModuleAdminRole);
    console.log("✅ Has claim issuer role:", hasClaimIssuerRole);

    // Register with SecurityTokenCore
    console.log("\n🔗 Registering with SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const tokenCore = SecurityTokenCore.attach(NEW_TOKEN_ADDRESS);

    const KYC_CLAIMS_MODULE_ID = ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"));
    
    // Unregister old module first
    console.log("Unregistering old module...");
    try {
      const unregisterTx = await tokenCore.unregisterModule(KYC_CLAIMS_MODULE_ID);
      await unregisterTx.wait();
      console.log("✅ Old module unregistered");
    } catch (error) {
      console.log("⚠️ No old module to unregister or unregister failed");
    }

    // Register new module
    console.log("Registering new module...");
    const registerTx = await tokenCore.registerModule(KYC_CLAIMS_MODULE_ID, kycModuleAddress);
    await registerTx.wait();
    console.log("✅ New KYC Claims Module registered successfully!");

    // Verify registration
    const registeredModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
    const isAuthorized = await tokenCore.isAuthorizedModule(kycModuleAddress);
    
    console.log(`✅ Registered module: ${registeredModule}`);
    console.log(`✅ Is authorized: ${isAuthorized}`);

    // Test the complete integration
    console.log("\n🧪 Testing complete integration...");
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    
    try {
      // Test KYC approval through token
      console.log(`Testing KYC approval through token for ${TEST_USER}...`);
      const kycTx = await tokenCore.approveKYC(TEST_USER);
      await kycTx.wait();
      console.log("✅ KYC approval through token successful!");

      // Test whitelist addition through token
      console.log(`Testing whitelist addition through token for ${TEST_USER}...`);
      const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
      await whitelistTx.wait();
      console.log("✅ Whitelist addition through token successful!");

      // Check verification status
      console.log("Checking verification status...");
      const status = await tokenCore.getVerificationStatus(TEST_USER);
      console.log("✅ Verification status:", {
        kycApproved: status[0],
        whitelisted: status[1],
        eligible: status[2],
        method: status[3]
      });

      // Test minting to verified user
      console.log(`Testing token minting to verified user...`);
      const mintTx = await tokenCore.mint(TEST_USER, 50);
      await mintTx.wait();
      console.log("✅ Token minting successful!");

      const balance = await tokenCore.balanceOf(TEST_USER);
      console.log(`✅ User balance: ${balance.toString()} tokens`);

    } catch (testError) {
      console.log("❌ Integration test failed:", testError.message);
    }

    console.log("\n🎉 Fresh KYC Claims Module deployment completed successfully!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(60));
    console.log(`NEW KYCClaimsModule: ${kycModuleAddress}`);
    console.log(`SecurityTokenCore: ${NEW_TOKEN_ADDRESS}`);
    console.log(`ClaimRegistry: ${CLAIM_REGISTRY_ADDRESS}`);
    console.log(`Module ID: ${KYC_CLAIMS_MODULE_ID}`);
    console.log(`Admin: ${deployer.address}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log("=" .repeat(60));

    console.log("\n💡 NEXT STEPS:");
    console.log("1. Update admin panel .env.local with new KYC module address:");
    console.log(`   AMOY_KYC_CLAIMS_MODULE_ADDRESS=${kycModuleAddress}`);
    console.log("2. Test all KYC & Claims functionality in admin panel");
    console.log("3. All buttons should now work properly!");

    return {
      kycModuleAddress,
      tokenCoreAddress: NEW_TOKEN_ADDRESS,
      claimRegistryAddress: CLAIM_REGISTRY_ADDRESS,
      admin: deployer.address
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
