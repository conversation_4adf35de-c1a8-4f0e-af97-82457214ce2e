import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function updateBonusTiers(string memory bonusTiers_) external",
  "function getTokenMetadata() external view returns (string memory tokenPrice, string memory bonusTiers, string memory tokenDetails, string memory tokenImageUrl)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, newBonusTiers } = await request.json();

    if (!tokenAddress || !newBonusTiers) {
      return NextResponse.json(
        { error: 'Token address and new bonus tiers are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress)) {
      return NextResponse.json(
        { error: 'Invalid token address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Get current metadata for comparison
    const currentMetadata = await tokenContract.getTokenMetadata();
    console.log('Current bonus tiers:', currentMetadata[1]);
    console.log('New bonus tiers:', newBonusTiers);

    // Update bonus tiers
    const tx = await tokenContract.updateBonusTiers(newBonusTiers);
    await tx.wait();

    // Verify update
    const updatedMetadata = await tokenContract.getTokenMetadata();

    console.log('Bonus tiers updated successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'Bonus tiers updated successfully',
      txHash: tx.hash,
      oldBonusTiers: currentMetadata[1],
      newBonusTiers: updatedMetadata[1],
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error updating bonus tiers:', error);
    return NextResponse.json(
      { error: `Failed to update bonus tiers: ${error.message}` },
      { status: 500 }
    );
  }
}
