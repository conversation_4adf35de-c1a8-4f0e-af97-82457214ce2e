const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🧪 Testing Upgrade Workflow on Amoy Testnet");
  console.log("===========================================");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));
  console.log("Network:", await deployer.provider.getNetwork());

  // Contract addresses from deployment
  const SECURITY_TOKEN_CORE_ADDRESS = "******************************************";
  const UPGRADE_MANAGER_ADDRESS = "******************************************";
  const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));

  try {
    // Step 1: Connect to deployed contracts
    console.log("\n📋 Step 1: Connecting to deployed contracts...");
    
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const securityTokenCore = SecurityTokenCore.attach(SECURITY_TOKEN_CORE_ADDRESS);
    
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    const upgradeManager = UpgradeManager.attach(UPGRADE_MANAGER_ADDRESS);
    
    console.log("✅ Connected to SecurityTokenCore:", SECURITY_TOKEN_CORE_ADDRESS);
    console.log("✅ Connected to UpgradeManager:", UPGRADE_MANAGER_ADDRESS);

    // Step 2: Verify current state
    console.log("\n📋 Step 2: Verifying current state...");
    
    console.log("Token name:", await securityTokenCore.name());
    console.log("Token version:", await securityTokenCore.version());
    console.log("Total supply:", ethers.formatUnits(await securityTokenCore.totalSupply(), 0));
    console.log("Deployer balance:", ethers.formatUnits(await securityTokenCore.balanceOf(deployer.address), 0));
    
    const registeredAddress = await upgradeManager.moduleProxies(SECURITY_TOKEN_CORE_ID);
    console.log("Module registered:", registeredAddress === SECURITY_TOKEN_CORE_ADDRESS);
    console.log("Emergency mode active:", await upgradeManager.isEmergencyModeActive());

    // Step 3: Deploy new implementation
    console.log("\n📋 Step 3: Deploying new implementation...");
    
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    const newImplAddress = await newImplementation.getAddress();
    
    console.log("✅ New implementation deployed to:", newImplAddress);
    console.log("Explorer link:", `https://amoy.polygonscan.com/address/${newImplAddress}`);

    // Step 4: Schedule upgrade with timelock
    console.log("\n📋 Step 4: Scheduling upgrade with timelock...");
    
    const scheduleTx = await upgradeManager.scheduleUpgrade(
      SECURITY_TOKEN_CORE_ID,
      newImplAddress,
      "Test upgrade on Amoy testnet - demonstrating timelock workflow"
    );
    
    console.log("Transaction hash:", scheduleTx.hash);
    console.log("Waiting for confirmation...");
    
    const receipt = await scheduleTx.wait();
    console.log("✅ Upgrade scheduled! Gas used:", receipt.gasUsed.toString());
    
    // Extract upgrade ID from events
    const upgradeScheduledEvent = receipt.logs.find(log => {
      try {
        const parsed = upgradeManager.interface.parseLog(log);
        return parsed.name === "UpgradeScheduled";
      } catch {
        return false;
      }
    });
    
    if (upgradeScheduledEvent) {
      const parsedEvent = upgradeManager.interface.parseLog(upgradeScheduledEvent);
      const upgradeId = parsedEvent.args.upgradeId;
      const executeTime = parsedEvent.args.executeTime;
      
      console.log("\n📋 Scheduled Upgrade Details:");
      console.log("Upgrade ID:", upgradeId);
      console.log("Execute time:", new Date(Number(executeTime) * 1000).toISOString());
      console.log("Current time:", new Date().toISOString());
      
      const timeUntilExecution = Number(executeTime) - Math.floor(Date.now() / 1000);
      const hours = Math.floor(timeUntilExecution / 3600);
      const minutes = Math.floor((timeUntilExecution % 3600) / 60);
      console.log(`⏰ Time until execution: ${hours}h ${minutes}m`);
      
      // Check pending upgrade details
      const pendingUpgrade = await upgradeManager.pendingUpgrades(upgradeId);
      console.log("\nPending upgrade verification:");
      console.log("- Module ID matches:", pendingUpgrade.moduleId === SECURITY_TOKEN_CORE_ID);
      console.log("- Proxy matches:", pendingUpgrade.proxy === SECURITY_TOKEN_CORE_ADDRESS);
      console.log("- New implementation matches:", pendingUpgrade.newImplementation === newImplAddress);
      console.log("- Executed:", pendingUpgrade.executed);
      console.log("- Cancelled:", pendingUpgrade.cancelled);
      console.log("- Description:", pendingUpgrade.description);
    }

    // Step 5: Test emergency upgrade workflow
    console.log("\n📋 Step 5: Testing emergency upgrade workflow...");
    
    // Deploy another implementation for emergency test
    const emergencyImplementation = await SecurityTokenCore.deploy();
    await emergencyImplementation.waitForDeployment();
    const emergencyImplAddress = await emergencyImplementation.getAddress();
    
    console.log("Emergency implementation deployed to:", emergencyImplAddress);
    
    // Activate emergency mode
    console.log("Activating emergency mode...");
    const emergencyActivateTx = await upgradeManager.activateEmergencyMode();
    await emergencyActivateTx.wait();
    
    console.log("✅ Emergency mode activated");
    console.log("Emergency mode active:", await upgradeManager.isEmergencyModeActive());
    
    // Note: We won't actually execute the emergency upgrade to avoid disrupting the test token
    console.log("\n⚠️ Emergency upgrade ready but not executed (preserving test token state)");
    console.log("Emergency upgrade command would be:");
    console.log(`await upgradeManager.emergencyUpgrade("${SECURITY_TOKEN_CORE_ID}", "${emergencyImplAddress}", "Emergency test");`);
    
    // Deactivate emergency mode
    console.log("Deactivating emergency mode...");
    const emergencyDeactivateTx = await upgradeManager.deactivateEmergencyMode();
    await emergencyDeactivateTx.wait();
    console.log("✅ Emergency mode deactivated");

    // Step 6: Test module management functions
    console.log("\n📋 Step 6: Testing module management...");
    
    const registeredModules = await upgradeManager.getRegisteredModules();
    console.log("Registered modules count:", registeredModules.length);
    console.log("Registered modules:", registeredModules);
    
    const pendingUpgradeIds = await upgradeManager.getPendingUpgradeIds();
    console.log("Pending upgrades count:", pendingUpgradeIds.length);
    
    const upgradeHistory = await upgradeManager.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);
    console.log("Upgrade history count:", upgradeHistory.length);

    // Step 7: Test token functionality
    console.log("\n📋 Step 7: Testing token functionality...");
    
    // Test minting more tokens
    console.log("Minting additional 500 tokens...");
    const mintTx = await securityTokenCore.mint(deployer.address, ethers.parseUnits("500", 0));
    await mintTx.wait();
    
    const newBalance = await securityTokenCore.balanceOf(deployer.address);
    console.log("New deployer balance:", ethers.formatUnits(newBalance, 0));
    
    // Test transfer validation
    const canTransfer = await securityTokenCore.canTransfer(
      deployer.address,
      "******************************************",
      ethers.parseUnits("100", 0)
    );
    console.log("Can transfer 100 tokens:", canTransfer);

    // Step 8: Summary and results
    console.log("\n🎯 Amoy Testnet Test Summary");
    console.log("===========================");
    
    console.log("✅ Contract connections working");
    console.log("✅ State verification successful");
    console.log("✅ New implementation deployment working");
    console.log("✅ Upgrade scheduling working (timelock)");
    console.log("✅ Emergency mode activation/deactivation working");
    console.log("✅ Module management functions working");
    console.log("✅ Token functionality working");
    
    console.log("\n🌐 Live Contract Addresses:");
    console.log(`SecurityTokenCore: ${SECURITY_TOKEN_CORE_ADDRESS}`);
    console.log(`UpgradeManager: ${UPGRADE_MANAGER_ADDRESS}`);
    console.log(`New Implementation: ${newImplAddress}`);
    console.log(`Emergency Implementation: ${emergencyImplAddress}`);
    
    console.log("\n🔍 Explorer Links:");
    console.log(`SecurityTokenCore: https://amoy.polygonscan.com/address/${SECURITY_TOKEN_CORE_ADDRESS}`);
    console.log(`UpgradeManager: https://amoy.polygonscan.com/address/${UPGRADE_MANAGER_ADDRESS}`);
    console.log(`New Implementation: https://amoy.polygonscan.com/address/${newImplAddress}`);
    console.log(`Emergency Implementation: https://amoy.polygonscan.com/address/${emergencyImplAddress}`);
    
    console.log("\n📋 Manual Testing Commands:");
    console.log("Execute scheduled upgrade (after timelock):");
    console.log(`UPGRADE_ID=<upgrade_id> UPGRADE_MANAGER_ADDRESS=${UPGRADE_MANAGER_ADDRESS} npx hardhat run scripts/execute-upgrade.js --network amoy`);
    
    console.log("\nSchedule another upgrade:");
    console.log(`MODULE_ID=SECURITY_TOKEN_CORE NEW_IMPLEMENTATION_ADDRESS=${newImplAddress} UPGRADE_MANAGER_ADDRESS=${UPGRADE_MANAGER_ADDRESS} npx hardhat run scripts/upgrade-module.js --network amoy`);

    console.log("\n✅ Amoy testnet upgrade workflow test completed successfully!");

  } catch (error) {
    console.error("\n❌ Test failed:");
    console.error("Error message:", error.message);
    
    if (error.data) {
      console.error("Error data:", error.data);
    }
    
    if (error.transaction) {
      console.error("Transaction hash:", error.transaction.hash);
    }
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
