const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 Decoding access control error...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deployer:", deployer.address);

  const FACTORY_ADDRESS = "******************************************";
  const IMPLEMENTATION_ADDRESS = "******************************************";

  try {
    // Get contracts
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(FACTORY_ADDRESS);

    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const implementation = SecurityTokenCore.attach(IMPLEMENTATION_ADDRESS);

    // Check factory roles
    console.log("📋 Factory Role Check:");
    const DEPLOYER_ROLE = await factory.DEPLOYER_ROLE();
    const DEFAULT_ADMIN_ROLE = await factory.DEFAULT_ADMIN_ROLE();
    
    console.log("DEPLOYER_ROLE:", DEPLOYER_ROLE);
    console.log("DEFAULT_ADMIN_ROLE:", DEFAULT_ADMIN_ROLE);
    
    const hasDeployerRole = await factory.hasRole(DEPLOYER_ROLE, deployer.address);
    const hasAdminRole = await factory.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    console.log("Has DEPLOYER_ROLE:", hasDeployerRole);
    console.log("Has DEFAULT_ADMIN_ROLE:", hasAdminRole);

    // Check implementation contract roles
    console.log("\n📋 Implementation Role Check:");
    try {
      const implDefaultAdminRole = await implementation.DEFAULT_ADMIN_ROLE();
      const implAgentRole = await implementation.AGENT_ROLE();
      
      console.log("Implementation DEFAULT_ADMIN_ROLE:", implDefaultAdminRole);
      console.log("Implementation AGENT_ROLE:", implAgentRole);
      
      const hasImplAdminRole = await implementation.hasRole(implDefaultAdminRole, deployer.address);
      const hasImplAgentRole = await implementation.hasRole(implAgentRole, deployer.address);
      
      console.log("Has implementation DEFAULT_ADMIN_ROLE:", hasImplAdminRole);
      console.log("Has implementation AGENT_ROLE:", hasImplAgentRole);
    } catch (e) {
      console.log("Could not check implementation roles (expected for proxy)");
    }

    // Try to manually call the initialize function to see what fails
    console.log("\n🧪 Testing manual initialization...");
    
    // Create initialization data
    const initData = implementation.interface.encodeFunctionData("initialize", [
      "TestToken",
      "TT", 
      0,
      ethers.parseUnits("1000", 0),
      deployer.address,
      "1 USD",
      "None",
      "Test token",
      ""
    ]);

    console.log("Initialization data:", initData);

    // Try to estimate gas for proxy deployment
    console.log("\n🚀 Testing proxy deployment...");
    
    try {
      // Get the ERC1967Proxy contract factory
      const ERC1967Proxy = await ethers.getContractFactory("ERC1967Proxy");
      
      const gasEstimate = await ERC1967Proxy.getDeployTransaction(
        IMPLEMENTATION_ADDRESS,
        initData
      ).estimateGas();
      
      console.log("✅ Proxy deployment gas estimate:", gasEstimate.toString());
      
      // Try actual deployment
      const proxy = await ERC1967Proxy.deploy(IMPLEMENTATION_ADDRESS, initData);
      await proxy.waitForDeployment();
      
      const proxyAddress = await proxy.getAddress();
      console.log("✅ Proxy deployed to:", proxyAddress);
      
      // Test the proxy
      const tokenProxy = SecurityTokenCore.attach(proxyAddress);
      const name = await tokenProxy.name();
      const symbol = await tokenProxy.symbol();
      
      console.log("✅ Token name:", name);
      console.log("✅ Token symbol:", symbol);
      
    } catch (proxyError) {
      console.log("❌ Proxy deployment failed:", proxyError.message);
      
      if (proxyError.data) {
        console.log("Proxy error data:", proxyError.data);
        
        // Decode the error
        if (proxyError.data === "0xd6bda275") {
          console.log("🔍 This is AccessControlUnauthorizedAccount error in initialization");
          
          // The error might be coming from the implementation contract's initialize function
          // Let's check what roles are being set up
          console.log("\n💡 The error is likely in the _setupRoles function");
          console.log("The implementation contract might be trying to grant roles to itself");
          console.log("or there might be a conflict in the access control setup");
        }
      }
    }

  } catch (error) {
    console.error("❌ Error:", error);
  }
}

// Execute the script
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
