const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Complete KYC & Claims System with Proper Token Configuration...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  try {
    // Step 1: Deploy a new ClaimRegistry that supports all Topic IDs
    console.log("📦 Deploying new ClaimRegistry with full Topic ID support...");
    const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
    const claimRegistry = await ClaimRegistry.deploy();
    await claimRegistry.waitForDeployment();
    const claimRegistryAddress = await claimRegistry.getAddress();
    console.log("✅ ClaimRegistry deployed to:", claimRegistryAddress);

    // Step 2: Deploy fresh KYCClaimsModule
    console.log("\n📦 Deploying fresh KYCClaimsModule...");
    const KYCClaimsModule = await ethers.getContractFactory("KYCClaimsModule");
    const kycModule = await KYCClaimsModule.deploy();
    await kycModule.waitForDeployment();
    const kycModuleAddress = await kycModule.getAddress();
    console.log("✅ KYCClaimsModule deployed to:", kycModuleAddress);

    // Step 3: Initialize KYCClaimsModule with new ClaimRegistry
    console.log("\n🔧 Initializing KYCClaimsModule...");
    const initTx = await kycModule.initialize(claimRegistryAddress, deployer.address);
    await initTx.wait();
    console.log("✅ KYCClaimsModule initialized successfully!");

    // Step 4: Deploy properly configured SecurityTokenCore
    console.log("\n📦 Deploying SecurityTokenCore with proper configuration...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const tokenCore = await upgrades.deployProxy(SecurityTokenCore, [
      "Augment Premium Token",           // name
      "APT",                            // symbol
      0,                               // decimals (whole tokens)
      ethers.parseUnits("5000000", 0), // maxSupply (5M tokens)
      deployer.address,                // admin
      "2.50 USD",                      // tokenPrice
      "Early Bird: 25%, Standard: 15%, Late: 5%", // bonusTiers
      "Premium ERC-3643 security token with full KYC Claims integration and on-chain compliance verification", // tokenDetails
      "https://augment.finance/token-logo.png" // tokenImageUrl
    ], {
      initializer: 'initialize',
      kind: 'uups'
    });

    await tokenCore.waitForDeployment();
    const tokenCoreAddress = await tokenCore.getAddress();
    console.log("✅ SecurityTokenCore deployed to:", tokenCoreAddress);

    // Step 5: Verify token configuration
    console.log("\n🔍 Verifying token configuration...");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const decimals = await tokenCore.decimals();
    const maxSupply = await tokenCore.maxSupply();
    const version = await tokenCore.version();
    
    console.log(`✅ Name: ${name}`);
    console.log(`✅ Symbol: ${symbol}`);
    console.log(`✅ Decimals: ${decimals}`);
    console.log(`✅ Max Supply: ${maxSupply.toString()}`);
    console.log(`✅ Version: ${version}`);

    // Step 6: Verify admin roles
    console.log("\n🔑 Verifying admin roles...");
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    
    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);

    // Step 7: Register KYC Claims Module
    console.log("\n🔗 Registering KYC Claims Module...");
    const KYC_CLAIMS_MODULE_ID = ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"));
    
    const registerTx = await tokenCore.registerModule(KYC_CLAIMS_MODULE_ID, kycModuleAddress);
    await registerTx.wait();
    console.log("✅ KYC Claims Module registered successfully!");

    // Step 8: Verify module registration
    const registeredModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
    const isAuthorized = await tokenCore.isAuthorizedModule(kycModuleAddress);
    
    console.log(`✅ Registered module: ${registeredModule}`);
    console.log(`✅ Is authorized: ${isAuthorized}`);

    // Step 9: Test complete system integration
    console.log("\n🧪 Testing complete system integration...");
    const TEST_USER = "******************************************";
    
    try {
      // Test KYC approval through token
      console.log(`Testing KYC approval through token for ${TEST_USER}...`);
      const kycTx = await tokenCore.approveKYC(TEST_USER);
      await kycTx.wait();
      console.log("✅ KYC approval through token successful!");

      // Test whitelist addition through token
      console.log(`Testing whitelist addition through token for ${TEST_USER}...`);
      const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
      await whitelistTx.wait();
      console.log("✅ Whitelist addition through token successful!");

      // Check verification status
      console.log("Checking verification status...");
      const status = await tokenCore.getVerificationStatus(TEST_USER);
      console.log("✅ Verification status:", {
        kycApproved: status[0],
        whitelisted: status[1],
        eligible: status[2],
        method: status[3]
      });

      // Test custom claims through module
      console.log("Testing custom claims issuance...");
      const claimTx = await kycModule.issueCustomClaim(
        TEST_USER,
        10101010000648, // Custom KYC Status
        ethers.AbiCoder.defaultAbiCoder().encode(['string'], ['Premium Tier Verified']),
        "https://augment.finance/kyc-verification",
        0 // Never expires
      );
      await claimTx.wait();
      console.log("✅ Custom claim issuance successful!");

      // Test minting to verified user
      console.log(`Testing token minting to verified user...`);
      const mintTx = await tokenCore.mint(TEST_USER, 100);
      await mintTx.wait();
      console.log("✅ Token minting successful!");

      const balance = await tokenCore.balanceOf(TEST_USER);
      console.log(`✅ User balance: ${balance.toString()} tokens`);

    } catch (testError) {
      console.log("❌ Some integration tests failed:", testError.message);
    }

    console.log("\n🎉 Complete KYC & Claims System deployment successful!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(70));
    console.log(`SecurityTokenCore: ${tokenCoreAddress}`);
    console.log(`KYCClaimsModule: ${kycModuleAddress}`);
    console.log(`ClaimRegistry: ${claimRegistryAddress}`);
    console.log(`Module ID: ${KYC_CLAIMS_MODULE_ID}`);
    console.log(`Admin: ${deployer.address}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log("=" .repeat(70));

    console.log("\n💡 NEXT STEPS:");
    console.log("1. Update admin panel .env.local with new addresses:");
    console.log(`   AMOY_SECURITY_TOKEN_CORE_ADDRESS=${tokenCoreAddress}`);
    console.log(`   AMOY_KYC_CLAIMS_MODULE_ADDRESS=${kycModuleAddress}`);
    console.log(`   CLAIM_REGISTRY_ADDRESS=${claimRegistryAddress}`);
    console.log("2. Test all functionality in admin panel");
    console.log("3. All KYC & Claims features should now work perfectly!");

    return {
      tokenCoreAddress,
      kycModuleAddress,
      claimRegistryAddress,
      admin: deployer.address
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
