import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const KYC_CLAIMS_MODULE_ABI = [
  "function issueCustomClaim(address user, uint256 claimType, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)",
  "function issueKYCClaim(address user, bytes calldata data) external returns (bytes32)"
];

const CLAIM_REGISTRY_ABI = [
  "function issueClaim(address subject, uint256 claimType, bytes calldata signature, bytes calldata data, string calldata uri, uint256 expiresAt) external returns (bytes32)"
];

export async function POST(request: NextRequest) {
  try {
    const { userAddress, topicId, data = '', uri = '', expiresAt = 0 } = await request.json();

    if (!userAddress || !topicId) {
      return NextResponse.json(
        { error: 'User address and topic ID are required' },
        { status: 400 }
      );
    }

    // Validate address
    if (!ethers.isAddress(userAddress)) {
      return NextResponse.json(
        { error: 'Invalid user address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;
    const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;
    const claimRegistryAddress = process.env.CLAIM_REGISTRY_ADDRESS;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Encode claim data
    const encodedData = data ? ethers.AbiCoder.defaultAbiCoder().encode(['string'], [data]) : '0x';
    
    let txHash: string;
    let claimId: string;

    if (kycModuleAddress && ethers.isAddress(kycModuleAddress)) {
      // Use KYC Claims Module if available
      console.log('Using KYC Claims Module for claim issuance');
      const kycModule = new ethers.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, signer);
      
      try {
        let tx;
        if (topicId === '10101010000001') {
          // Special handling for KYC verification claim
          tx = await kycModule.issueKYCClaim(userAddress, encodedData);
        } else {
          // Custom claim
          tx = await kycModule.issueCustomClaim(
            userAddress,
            topicId,
            encodedData,
            uri,
            expiresAt
          );
        }
        
        const receipt = await tx.wait();
        txHash = tx.hash;
        
        // Extract claim ID from events
        const claimEvent = receipt.logs.find((log: any) => {
          try {
            const parsed = kycModule.interface.parseLog(log);
            return parsed?.name === 'ClaimIssued';
          } catch {
            return false;
          }
        });
        
        if (claimEvent) {
          const parsedEvent = kycModule.interface.parseLog(claimEvent);
          claimId = parsedEvent?.args?.claimId || 'Unknown';
        } else {
          claimId = 'Generated';
        }
        
        console.log('Claim issued via KYC Claims Module:', txHash);
      } catch (moduleError) {
        console.log('KYC Claims Module failed, falling back to direct ClaimRegistry call:', moduleError);
        
        if (!claimRegistryAddress || !ethers.isAddress(claimRegistryAddress)) {
          throw new Error('ClaimRegistry address not configured');
        }
        
        // Fallback to direct ClaimRegistry call
        const claimRegistry = new ethers.Contract(claimRegistryAddress, CLAIM_REGISTRY_ABI, signer);
        const tx = await claimRegistry.issueClaim(
          userAddress,
          topicId,
          '0x', // Empty signature for admin-issued claims
          encodedData,
          uri,
          expiresAt
        );
        const receipt = await tx.wait();
        txHash = tx.hash;
        claimId = 'Generated via ClaimRegistry';
        console.log('Claim issued via direct ClaimRegistry call:', txHash);
      }
    } else {
      // Direct ClaimRegistry call
      if (!claimRegistryAddress || !ethers.isAddress(claimRegistryAddress)) {
        return NextResponse.json(
          { error: 'ClaimRegistry address not configured' },
          { status: 500 }
        );
      }
      
      console.log('Using direct ClaimRegistry call for claim issuance');
      const claimRegistry = new ethers.Contract(claimRegistryAddress, CLAIM_REGISTRY_ABI, signer);
      const tx = await claimRegistry.issueClaim(
        userAddress,
        topicId,
        '0x', // Empty signature for admin-issued claims
        encodedData,
        uri,
        expiresAt
      );
      const receipt = await tx.wait();
      txHash = tx.hash;
      claimId = 'Generated via ClaimRegistry';
      console.log('Claim issued via direct ClaimRegistry call:', txHash);
    }

    return NextResponse.json({
      success: true,
      message: 'Claim issued successfully',
      txHash,
      claimId,
      userAddress,
      topicId,
      data
    });

  } catch (error: any) {
    console.error('Error issuing claim:', error);
    return NextResponse.json(
      { error: `Failed to issue claim: ${error.message}` },
      { status: 500 }
    );
  }
}
