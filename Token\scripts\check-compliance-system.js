const { ethers } = require("hardhat");

// Token address to check
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🔍 CHECKING COMPLIANCE SYSTEM STATUS");
  console.log("Token Address:", TOKEN_ADDRESS);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    console.log("\n📋 CHECKING REGISTERED MODULES:");
    
    // Check what modules are registered
    const moduleIds = {
      IDENTITY_MANAGER: ethers.keccak256(ethers.toUtf8Bytes("IDENTITY_MANAGER")),
      COMPLIANCE_ENGINE: ethers.keccak256(ethers.toUtf8Bytes("COMPLIANCE_ENGINE")),
      TRANSFER_CONTROLLER: ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_CONTROLLER")),
      AGENT_MANAGER: ethers.keccak256(ethers.toUtf8Bytes("AGENT_MANAGER")),
      EMERGENCY_MANAGER: ethers.keccak256(ethers.toUtf8Bytes("EMERGENCY_MANAGER"))
    };

    for (const [name, id] of Object.entries(moduleIds)) {
      try {
        // Try to get module address (this might not be a public function)
        console.log(`Checking ${name}...`);
        // We can't directly check modules, so let's test functionality instead
      } catch (error) {
        console.log(`❌ Cannot check ${name}:`, error.message);
      }
    }

    console.log("\n🧪 TESTING COMPLIANCE FUNCTIONS:");

    // Test 1: Try to add a completely new address to whitelist
    const testAddress = ethers.Wallet.createRandom().address;
    console.log(`Testing with new address: ${testAddress}`);

    try {
      // Check if this address is whitelisted (should be false)
      const isWhitelisted = await tokenContract.isWhitelisted(testAddress);
      console.log(`New address whitelisted: ${isWhitelisted}`);

      if (!isWhitelisted) {
        // Try to whitelist it
        console.log("Attempting to whitelist new address...");
        try {
          await tokenContract.addToWhitelist.staticCall(testAddress);
          console.log("✅ addToWhitelist would succeed");
        } catch (whitelistError) {
          console.log("❌ addToWhitelist would fail:", whitelistError.message);
        }
      }
    } catch (error) {
      console.log("❌ Error testing whitelist:", error.message);
    }

    // Test 2: Check what functions are available
    console.log("\n📋 CHECKING AVAILABLE FUNCTIONS:");
    
    const availableFunctions = [
      'isWhitelisted',
      'isVerified', 
      'isFrozen',
      'addToWhitelist',
      'removeFromWhitelist',
      'forcedTransfer'
    ];

    for (const funcName of availableFunctions) {
      try {
        const func = tokenContract[funcName];
        if (func) {
          console.log(`✅ ${funcName}: Available`);
        } else {
          console.log(`❌ ${funcName}: Not available`);
        }
      } catch (error) {
        console.log(`❌ ${funcName}: Error -`, error.message);
      }
    }

    // Test 3: Check the actual compliance behavior
    console.log("\n🧪 TESTING ACTUAL COMPLIANCE BEHAVIOR:");

    // Create a truly random address that definitely isn't whitelisted
    const randomAddress = ethers.Wallet.createRandom().address;
    console.log(`Testing transfer to random address: ${randomAddress}`);

    try {
      // Check if random address is whitelisted
      const randomIsWhitelisted = await tokenContract.isWhitelisted(randomAddress);
      const randomIsVerified = await tokenContract.isVerified(randomAddress);
      
      console.log(`Random address whitelisted: ${randomIsWhitelisted}`);
      console.log(`Random address verified: ${randomIsVerified}`);

      if (!randomIsWhitelisted || !randomIsVerified) {
        // Try a normal transfer to this address (should fail)
        try {
          const decimals = await tokenContract.decimals();
          await tokenContract.transfer.staticCall(randomAddress, ethers.parseUnits("1", decimals));
          console.log("❌ CRITICAL: Normal transfer to unverified/non-whitelisted address would SUCCEED!");
        } catch (transferError) {
          console.log("✅ Good: Normal transfer to unverified/non-whitelisted address fails:", transferError.message);
        }

        // Try a force transfer to this address (should also have some restrictions)
        try {
          const decimals = await tokenContract.decimals();
          await tokenContract.forcedTransfer.staticCall(deployer.address, randomAddress, ethers.parseUnits("1", decimals));
          console.log("❌ CRITICAL: Force transfer to unverified/non-whitelisted address would SUCCEED!");
        } catch (forceError) {
          console.log("✅ Good: Force transfer to unverified/non-whitelisted address fails:", forceError.message);
        }
      }
    } catch (error) {
      console.log("❌ Error testing random address:", error.message);
    }

    // Test 4: Check the original "problematic" address more thoroughly
    console.log("\n🔍 RE-EXAMINING THE ORIGINAL ADDRESS:");
    const originalAddress = "******************************************";
    
    const isWhitelisted = await tokenContract.isWhitelisted(originalAddress);
    const isVerified = await tokenContract.isVerified(originalAddress);
    const balance = await tokenContract.balanceOf(originalAddress);
    const decimals = await tokenContract.decimals();
    
    console.log(`Address: ${originalAddress}`);
    console.log(`Whitelisted: ${isWhitelisted}`);
    console.log(`Verified: ${isVerified}`);
    console.log(`Balance: ${ethers.formatUnits(balance, decimals)} tokens`);

    console.log("\n💡 CONCLUSION:");
    if (isWhitelisted && isVerified) {
      console.log("✅ The force transfer was LEGITIMATE - recipient was properly whitelisted and verified");
      console.log("✅ This is NOT a security breach");
      console.log("✅ The compliance system is working correctly for this transfer");
    } else {
      console.log("❌ SECURITY BREACH CONFIRMED - force transfer succeeded to non-compliant address");
    }

  } catch (error) {
    console.error("❌ Compliance check failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
