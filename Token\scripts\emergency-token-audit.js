const { ethers } = require("hardhat");

// Token address to audit
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚨 EMERGENCY TOKEN AUDIT - FINDING MISSING TOKENS");
  console.log("Token Address:", TOKEN_ADDRESS);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    // Get basic token info
    const totalSupply = await tokenContract.totalSupply();
    const decimals = await tokenContract.decimals();
    const name = await tokenContract.name();
    const symbol = await tokenContract.symbol();

    console.log("\n📊 TOKEN OVERVIEW:");
    console.log(`Name: ${name} (${symbol})`);
    console.log(`Total Supply: ${ethers.formatUnits(totalSupply, decimals)} tokens`);
    console.log(`Decimals: ${decimals}`);

    // Check known addresses
    const knownAddresses = [
      "******************************************", // Your main wallet
      "******************************************", // Force transfer recipient
      deployer.address, // Deployer address
      "******************************************", // Test address
    ];

    console.log("\n🔍 CHECKING KNOWN ADDRESSES:");
    let totalKnownBalance = 0n;

    for (const address of knownAddresses) {
      try {
        const balance = await tokenContract.balanceOf(address);
        const isWhitelisted = await tokenContract.isWhitelisted(address);
        const isVerified = await tokenContract.isVerified(address);
        
        if (balance > 0) {
          console.log(`\n${address}:`);
          console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
          console.log(`  Whitelisted: ${isWhitelisted}`);
          console.log(`  Verified: ${isVerified}`);
          totalKnownBalance += balance;
        }
      } catch (error) {
        console.log(`Error checking ${address}: ${error.message}`);
      }
    }

    console.log(`\nTotal in known addresses: ${ethers.formatUnits(totalKnownBalance, decimals)} tokens`);
    const missingTokens = totalSupply - totalKnownBalance;
    console.log(`Missing tokens: ${ethers.formatUnits(missingTokens, decimals)} tokens`);

    // Check for Transfer events to find all addresses that have received tokens
    console.log("\n🔍 ANALYZING TRANSFER EVENTS TO FIND ALL TOKEN HOLDERS:");
    
    try {
      // Get all Transfer events
      const transferFilter = tokenContract.filters.Transfer();
      const transferEvents = await tokenContract.queryFilter(transferFilter, -50000); // Last 50k blocks
      
      console.log(`Found ${transferEvents.length} Transfer events`);
      
      // Track all addresses that have received tokens
      const allAddresses = new Set();
      
      for (const event of transferEvents) {
        if (event.args) {
          const { from, to, value } = event.args;
          if (to !== ethers.ZeroAddress) {
            allAddresses.add(to.toLowerCase());
          }
        }
      }
      
      console.log(`Found ${allAddresses.size} unique addresses that received tokens`);
      
      // Check balance of each address
      console.log("\n📋 ALL TOKEN HOLDERS:");
      let totalFoundBalance = 0n;
      let holderCount = 0;
      
      for (const address of allAddresses) {
        try {
          const balance = await tokenContract.balanceOf(address);
          if (balance > 0) {
            const isWhitelisted = await tokenContract.isWhitelisted(address);
            const isVerified = await tokenContract.isVerified(address);
            
            console.log(`\n${address}:`);
            console.log(`  Balance: ${ethers.formatUnits(balance, decimals)} tokens`);
            console.log(`  Whitelisted: ${isWhitelisted}`);
            console.log(`  Verified: ${isVerified}`);
            
            if (!isWhitelisted || !isVerified) {
              console.log(`  ⚠️ WARNING: Has tokens but not properly whitelisted/verified!`);
            }
            
            totalFoundBalance += balance;
            holderCount++;
          }
        } catch (error) {
          console.log(`Error checking ${address}: ${error.message}`);
        }
      }
      
      console.log(`\n📊 AUDIT SUMMARY:`);
      console.log(`Total Supply: ${ethers.formatUnits(totalSupply, decimals)} tokens`);
      console.log(`Total Found in Holders: ${ethers.formatUnits(totalFoundBalance, decimals)} tokens`);
      console.log(`Number of Holders: ${holderCount}`);
      console.log(`Whitelisted Holders: 2 (only showing in admin panel)`);
      console.log(`Non-Whitelisted Holders: ${holderCount - 2}`);
      
      if (totalFoundBalance === totalSupply) {
        console.log("✅ All tokens accounted for!");
      } else {
        const stillMissing = totalSupply - totalFoundBalance;
        console.log(`❌ Still missing: ${ethers.formatUnits(stillMissing, decimals)} tokens`);
      }
      
      // Check if there are any frozen/locked tokens
      console.log("\n🔍 CHECKING FOR FROZEN/LOCKED TOKENS:");
      
      // Check if there's a vault address or admin address holding tokens
      const adminAddresses = [
        deployer.address,
        "******************************************", // Common burn address
        "******************************************", // Dead address
      ];
      
      for (const adminAddr of adminAddresses) {
        try {
          const balance = await tokenContract.balanceOf(adminAddr);
          if (balance > 0) {
            console.log(`Admin/Vault ${adminAddr}: ${ethers.formatUnits(balance, decimals)} tokens`);
          }
        } catch (error) {
          // Ignore errors for special addresses
        }
      }
      
    } catch (eventError) {
      console.error("Error analyzing transfer events:", eventError.message);
    }

    // Check for any minting events
    console.log("\n🔍 CHECKING MINT EVENTS:");
    
    try {
      const mintFilter = tokenContract.filters.Transfer(ethers.ZeroAddress);
      const mintEvents = await tokenContract.queryFilter(mintFilter, -50000);
      
      console.log(`Found ${mintEvents.length} mint events`);
      let totalMinted = 0n;
      
      for (const event of mintEvents) {
        if (event.args) {
          const { to, value } = event.args;
          console.log(`Minted ${ethers.formatUnits(value, decimals)} tokens to ${to}`);
          totalMinted += value;
        }
      }
      
      console.log(`Total minted: ${ethers.formatUnits(totalMinted, decimals)} tokens`);
      
    } catch (mintError) {
      console.error("Error checking mint events:", mintError.message);
    }

  } catch (error) {
    console.error("❌ Audit failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
