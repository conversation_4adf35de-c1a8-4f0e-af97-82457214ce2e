/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/claim-types/route";
exports.ids = ["app/api/claim-types/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaim-types%2Froute&page=%2Fapi%2Fclaim-types%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaim-types%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaim-types%2Froute&page=%2Fapi%2Fclaim-types%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaim-types%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_claim_types_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/claim-types/route.ts */ \"(rsc)/./src/app/api/claim-types/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/claim-types/route\",\n        pathname: \"/api/claim-types\",\n        filename: \"route\",\n        bundlePath: \"app/api/claim-types/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\claim-types\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_claim_types_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaim-types%2Froute&page=%2Fapi%2Fclaim-types%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaim-types%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/claim-types/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/claim-types/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n\n\n// GET - Fetch all claim types\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const offset = parseInt(searchParams.get('offset') || '0');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const claimRegistryAddress = \"******************************************\";\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\");\n        const claimRegistryABI = [\n            \"function getActiveClaimTypes(uint256 offset, uint256 limit) external view returns (tuple(uint256 id, string name, string description, address creator, uint256 createdAt, bool active)[])\",\n            \"function getTotalClaimTypes() external view returns (uint256)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(claimRegistryAddress, claimRegistryABI, provider);\n        const [claimTypes, totalCount] = await Promise.all([\n            claimRegistry.getActiveClaimTypes(offset, limit),\n            claimRegistry.getTotalClaimTypes()\n        ]);\n        const formattedClaimTypes = claimTypes.map((ct)=>({\n                id: ct.id.toString(),\n                name: ct.name,\n                description: ct.description,\n                creator: ct.creator,\n                createdAt: new Date(Number(ct.createdAt) * 1000).toISOString(),\n                active: ct.active\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            claimTypes: formattedClaimTypes,\n            pagination: {\n                offset,\n                limit,\n                total: Number(totalCount)\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching claim types:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch claim types'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new claim type\nasync function POST(request) {\n    try {\n        const { name, description } = await request.json();\n        if (!name || !description) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name and description are required'\n            }, {\n                status: 400\n            });\n        }\n        const claimRegistryAddress = \"******************************************\";\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\");\n        const adminWallet = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n        const claimRegistryABI = [\n            \"function createClaimType(string calldata name, string calldata description) external returns (uint256)\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);\n        const tx = await claimRegistry.createClaimType(name, description);\n        const receipt = await tx.wait();\n        // Extract the claim type ID from the transaction logs\n        const claimTypeCreatedEvent = receipt.logs.find((log)=>{\n            try {\n                const parsed = claimRegistry.interface.parseLog(log);\n                return parsed?.name === 'ClaimTypeCreated';\n            } catch  {\n                return false;\n            }\n        });\n        let claimTypeId = null;\n        if (claimTypeCreatedEvent) {\n            const parsed = claimRegistry.interface.parseLog(claimTypeCreatedEvent);\n            claimTypeId = parsed?.args?.claimTypeId?.toString();\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            claimTypeId,\n            transactionHash: receipt.transactionHash,\n            name,\n            description,\n            message: 'Claim type created successfully'\n        });\n    } catch (error) {\n        console.error('Error creating claim type:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create claim type'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Update claim type\nasync function PUT(request) {\n    try {\n        const { claimTypeId, name, description, active } = await request.json();\n        if (!claimTypeId || !name || !description || active === undefined) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'All fields are required'\n            }, {\n                status: 400\n            });\n        }\n        const claimRegistryAddress = \"******************************************\";\n        if (!claimRegistryAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Claim registry not configured'\n            }, {\n                status: 500\n            });\n        }\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(process.env.POLYGON_AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\");\n        const adminWallet = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(process.env.CONTRACT_ADMIN_PRIVATE_KEY, provider);\n        const claimRegistryABI = [\n            \"function updateClaimType(uint256 claimTypeId, string calldata name, string calldata description, bool active) external\"\n        ];\n        const claimRegistry = new ethers__WEBPACK_IMPORTED_MODULE_2__.Contract(claimRegistryAddress, claimRegistryABI, adminWallet);\n        const tx = await claimRegistry.updateClaimType(claimTypeId, name, description, active);\n        const receipt = await tx.wait();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            transactionHash: receipt.transactionHash,\n            claimTypeId,\n            name,\n            description,\n            active,\n            message: 'Claim type updated successfully'\n        });\n    } catch (error) {\n        console.error('Error updating claim type:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update claim type'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/claim-types/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fclaim-types%2Froute&page=%2Fapi%2Fclaim-types%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fclaim-types%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();