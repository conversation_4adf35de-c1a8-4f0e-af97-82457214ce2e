import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@auth0/nextjs-auth0';

export async function GET(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const userEmail = session.user.email;

    // Try to call admin panel API, fallback to default if it fails
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';
    const params = new URLSearchParams({
      clientEmail: userEmail,
    });

    if (tokenAddress) {
      params.append('tokenAddress', tokenAddress);
    }

    try {
      const response = await fetch(`${adminApiUrl}/qualification-progress?${params.toString()}`);

      if (response.ok) {
        const progressData = await response.json();

        console.log('📊 Retrieved qualification progress from admin API:', {
          userEmail,
          tokenAddress,
          currentStep: progressData.currentStep,
          completedSteps: progressData.completedSteps,
          qualificationStatus: progressData.qualificationStatus
        });

        return NextResponse.json(progressData);
      } else {
        console.warn('⚠️ Admin API not available, using fallback');
      }
    } catch (error) {
      console.warn('⚠️ Admin API error, using fallback:', error);
    }

    // Fallback: Check if user has existing profile and create default progress
    const defaultProgress = {
      country: '',
      countryCompleted: false,
      agreementAccepted: false,
      profileCompleted: false,
      walletConnected: false,
      kycCompleted: false,
      currentStep: 0,
      completedSteps: 0,
      tokenAddress: tokenAddress,
      clientEmail: userEmail,
      lastUpdated: new Date().toISOString(),
    };

    // Check if user has existing profile (affects profileCompleted status)
    try {
      const profileResponse = await fetch(`${adminApiUrl}/clients?search=${encodeURIComponent(userEmail)}&limit=1`);
      if (profileResponse.ok) {
        const profileData = await profileResponse.json();
        if (profileData.clients && profileData.clients.length > 0) {
          const client = profileData.clients[0];
          defaultProgress.profileCompleted = true;
          defaultProgress.walletConnected = !!client.walletAddress;
          defaultProgress.kycCompleted = client.kycStatus === 'APPROVED';

          // Update current step based on completion status
          if (defaultProgress.kycCompleted) {
            defaultProgress.currentStep = 5; // All completed
            defaultProgress.completedSteps = 5;
          } else if (defaultProgress.walletConnected) {
            defaultProgress.currentStep = 4; // On KYC step
            defaultProgress.completedSteps = 4;
          } else if (defaultProgress.profileCompleted) {
            defaultProgress.currentStep = 3; // On wallet step
            defaultProgress.completedSteps = 3;
          }
        }
      }
    } catch (error) {
      console.error('Error checking profile status:', error);
    }

    console.log('📊 Using fallback qualification progress:', defaultProgress);
    return NextResponse.json(defaultProgress);
  } catch (error) {
    console.error('Error fetching qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to fetch qualification progress' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getSession(request, NextResponse.next());
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const userEmail = session.user.email;

    // Try to call admin panel API to save qualification progress
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';

    const requestData = {
      ...body,
      clientEmail: userEmail,
    };

    try {
      const response = await fetch(`${adminApiUrl}/qualification-progress`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const result = await response.json();

        console.log('💾 Saved qualification progress via admin API:', {
          userEmail,
          tokenAddress: body.tokenAddress,
          currentStep: body.currentStep,
          completedSteps: body.completedSteps
        });

        return NextResponse.json(result);
      } else {
        console.warn('⚠️ Admin API not available for saving, using fallback');
      }
    } catch (error) {
      console.warn('⚠️ Admin API error for saving, using fallback:', error);
    }

    // Fallback: Log the progress and return success
    console.log('💾 Fallback: Logging qualification progress:', {
      userEmail,
      tokenAddress: body.tokenAddress,
      currentStep: body.currentStep,
      completedSteps: body.completedSteps,
      data: requestData
    });

    return NextResponse.json({
      success: true,
      message: 'Qualification progress saved successfully (fallback mode)',
      data: requestData
    });
  } catch (error) {
    console.error('Error saving qualification progress:', error);
    return NextResponse.json(
      { error: 'Failed to save qualification progress' },
      { status: 500 }
    );
  }
}

// PUT endpoint to fix qualification progress flags
export async function PUT(request: Request) {
  try {
    const { userEmail, tokenAddress } = await request.json();

    if (!userEmail || !tokenAddress) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('🔧 Fixing qualification progress flags via client API for:', { userEmail, tokenAddress });

    // Forward the request to the admin API
    const adminApiUrl = process.env.ADMIN_API_URL || 'http://localhost:6677/api';
    const response = await fetch(`${adminApiUrl}/qualification-progress`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userEmail, tokenAddress }),
    });

    if (!response.ok) {
      throw new Error(`Admin API responded with status: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ Qualification progress flags fixed successfully via admin API:', result);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Error fixing qualification progress flags:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
