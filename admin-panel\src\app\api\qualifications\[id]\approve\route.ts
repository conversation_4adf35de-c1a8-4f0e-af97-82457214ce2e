import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { forceApprove = false, adminId = 'system' } = body;

    // Find the qualification
    const qualification = await prisma.qualificationProgress.findUnique({
      where: { id },
      include: {
        client: true,
        token: true,
      }
    });

    if (!qualification) {
      return NextResponse.json({ error: 'Qualification not found' }, { status: 404 });
    }

    // Check if all steps are completed (unless force approve)
    const allStepsCompleted = qualification.countrySelected &&
                             qualification.agreementAccepted &&
                             qualification.profileCompleted &&
                             qualification.walletConnected &&
                             qualification.kycCompleted;

    if (!forceApprove && !allStepsCompleted) {
      return NextResponse.json(
        { 
          error: 'Cannot approve qualification - not all steps completed',
          missingSteps: {
            country: !qualification.countrySelected,
            agreement: !qualification.agreementAccepted,
            profile: !qualification.profileCompleted,
            wallet: !qualification.walletConnected,
            kyc: !qualification.kycCompleted,
          }
        },
        { status: 400 }
      );
    }

    // Update qualification status
    const updatedQualification = await prisma.qualificationProgress.update({
      where: { id },
      data: {
        qualificationStatus: forceApprove ? 'FORCE_APPROVED' : 'APPROVED',
        approvedBy: adminId,
        approvedAt: new Date(),
        rejectedReason: null, // Clear any previous rejection reason
      },
      include: {
        client: true,
        token: true,
      }
    });

    // If this is a token-specific qualification, also update the client's whitelist status
    if (qualification.tokenId && qualification.token && qualification.client.walletAddress) {
      try {
        // 1. Add client to token whitelist in the database
        await prisma.client.update({
          where: { id: qualification.clientId },
          data: {
            isWhitelisted: true,
            whitelistedAt: new Date(),
          }
        });

        // 2. Create/update TokenClientApproval record
        await prisma.tokenClientApproval.upsert({
          where: {
            tokenId_clientId: {
              tokenId: qualification.tokenId,
              clientId: qualification.clientId
            }
          },
          update: {
            approvalStatus: 'APPROVED',
            kycApproved: true,
            whitelistApproved: true,
            approvedBy: adminId,
            approvedAt: new Date(),
            notes: `Auto-approved via qualification ${forceApprove ? 'force approval' : 'approval'}`
          },
          create: {
            tokenId: qualification.tokenId,
            clientId: qualification.clientId,
            approvalStatus: 'APPROVED',
            kycApproved: true,
            whitelistApproved: true,
            approvedBy: adminId,
            approvedAt: new Date(),
            notes: `Auto-approved via qualification ${forceApprove ? 'force approval' : 'approval'}`
          }
        });

        // 3. Add client to blockchain whitelist (if token has whitelist address)
        if (qualification.token.whitelistAddress && qualification.token.whitelistAddress !== '******************************************') {
          try {
            // Import whitelist utilities
            const WhitelistUtils = await import('../../../../../utils/whitelist');

            const result = await WhitelistUtils.addToWhitelist(
              qualification.token.whitelistAddress,
              qualification.client.walletAddress,
              qualification.token.network || 'amoy'
            );

            console.log('Successfully added client to blockchain whitelist:', {
              clientEmail: qualification.client.email,
              walletAddress: qualification.client.walletAddress,
              tokenName: qualification.token.name,
              tokenAddress: qualification.token.address,
              whitelistAddress: qualification.token.whitelistAddress,
              txHash: result.txHash
            });
          } catch (whitelistError) {
            console.error('Error adding client to blockchain whitelist:', whitelistError);
            // Don't fail the approval if blockchain whitelist fails, but log it
            console.warn('Qualification approved but blockchain whitelisting failed. Client may need manual whitelisting.');
          }
        } else {
          console.log('Token has no whitelist address, skipping blockchain whitelisting');
        }

        console.log('Updated client whitelist status for token:', {
          clientEmail: qualification.client.email,
          tokenName: qualification.token.name,
          tokenAddress: qualification.token.address,
        });
      } catch (error) {
        console.error('Error updating client whitelist status:', error);
        // Don't fail the approval if whitelist update fails
      }
    }

    console.log('Approved qualification:', {
      id: updatedQualification.id,
      clientEmail: updatedQualification.client.email,
      tokenName: updatedQualification.token?.name || 'Global',
      status: updatedQualification.qualificationStatus,
      forceApprove,
      approvedBy: adminId,
    });

    return NextResponse.json({
      success: true,
      message: `Qualification ${forceApprove ? 'force ' : ''}approved successfully`,
      qualification: updatedQualification,
    });

  } catch (error) {
    console.error('Error approving qualification:', error);
    return NextResponse.json(
      { error: 'Failed to approve qualification' },
      { status: 500 }
    );
  }
}
