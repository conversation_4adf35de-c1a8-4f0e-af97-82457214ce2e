const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying to Amoy Testnet");
  console.log("============================");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));
  console.log("Network:", await deployer.provider.getNetwork());

  try {
    // Step 1: Deploy SecurityTokenCore
    console.log("\n📋 Step 1: Deploying SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const securityTokenCore = await upgrades.deployProxy(
      SecurityTokenCore,
      [
        "Augment Security Token", // name
        "AST", // symbol
        0, // decimals
        ethers.parseUnits("********", 0), // maxSupply (10M tokens with 0 decimals)
        deployer.address, // admin
        "1 USD", // tokenPrice
        "Early: 15%, Regular: 10%, Late: 5%", // bonusTiers
        "Augment modular ERC-3643 compliant security token with upgradeable architecture", // tokenDetails
        "https://augment.com/ast-logo.png" // tokenImageUrl
      ],
      {
        initializer: "initialize",
        kind: "uups",
        timeout: 300000 // 5 minutes timeout
      }
    );

    await securityTokenCore.waitForDeployment();
    const tokenAddress = await securityTokenCore.getAddress();
    console.log("✅ SecurityTokenCore deployed to:", tokenAddress);

    // Step 2: Deploy UpgradeManager
    console.log("\n📋 Step 2: Deploying UpgradeManager...");
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    
    const upgradeManager = await upgrades.deployProxy(
      UpgradeManager,
      [deployer.address],
      {
        initializer: "initialize",
        kind: "uups",
        timeout: 300000 // 5 minutes timeout
      }
    );

    await upgradeManager.waitForDeployment();
    const upgradeManagerAddress = await upgradeManager.getAddress();
    console.log("✅ UpgradeManager deployed to:", upgradeManagerAddress);

    // Step 3: Basic verification
    console.log("\n📋 Step 3: Basic verification...");
    
    // Check SecurityTokenCore
    console.log("Token name:", await securityTokenCore.name());
    console.log("Token symbol:", await securityTokenCore.symbol());
    console.log("Token decimals:", await securityTokenCore.decimals());
    console.log("Max supply:", ethers.formatUnits(await securityTokenCore.maxSupply(), 0));
    console.log("Version:", await securityTokenCore.version());
    
    // Check UpgradeManager
    console.log("Upgrade delay:", await upgradeManager.UPGRADE_DELAY(), "seconds");
    console.log("Emergency mode duration:", await upgradeManager.EMERGENCY_MODE_DURATION(), "seconds");
    console.log("Emergency mode active:", await upgradeManager.isEmergencyModeActive());

    // Step 4: Register SecurityTokenCore with UpgradeManager
    console.log("\n📋 Step 4: Registering SecurityTokenCore with UpgradeManager...");
    const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
    
    const registerTx = await upgradeManager.registerModule(SECURITY_TOKEN_CORE_ID, tokenAddress);
    await registerTx.wait();
    console.log("✅ SecurityTokenCore registered with UpgradeManager");
    
    // Verify registration
    const registeredAddress = await upgradeManager.moduleProxies(SECURITY_TOKEN_CORE_ID);
    console.log("Registration verified:", registeredAddress === tokenAddress);

    // Step 5: Test basic functionality
    console.log("\n📋 Step 5: Testing basic functionality...");
    
    // Test minting
    console.log("Minting 1000 tokens to deployer...");
    const mintTx = await securityTokenCore.mint(deployer.address, ethers.parseUnits("1000", 0));
    await mintTx.wait();
    console.log("Deployer balance:", ethers.formatUnits(await securityTokenCore.balanceOf(deployer.address), 0));
    
    // Test transfer validation
    const canTransfer = await securityTokenCore.canTransfer(
      deployer.address, 
      "******************************************", // dummy address
      ethers.parseUnits("100", 0)
    );
    console.log("Can transfer 100 tokens:", canTransfer);

    // Step 6: Summary
    console.log("\n🎯 Deployment Summary");
    console.log("====================");
    console.log(`✅ SecurityTokenCore: ${tokenAddress}`);
    console.log(`✅ UpgradeManager: ${upgradeManagerAddress}`);
    console.log(`✅ Network: Amoy Testnet (Chain ID: ${(await deployer.provider.getNetwork()).chainId})`);
    
    // Step 7: Explorer links
    console.log("\n🔍 Explorer Links:");
    console.log(`SecurityTokenCore: https://amoy.polygonscan.com/address/${tokenAddress}`);
    console.log(`UpgradeManager: https://amoy.polygonscan.com/address/${upgradeManagerAddress}`);
    
    // Step 8: Environment variables
    console.log("\n📝 Environment Variables for .env.local:");
    console.log(`# Amoy Testnet Deployment`);
    console.log(`AMOY_SECURITY_TOKEN_CORE_ADDRESS=${tokenAddress}`);
    console.log(`AMOY_UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress}`);
    
    // Step 9: Next steps
    console.log("\n🚀 Next Steps:");
    console.log("1. Add the addresses to your .env.local file");
    console.log("2. Verify contracts on PolygonScan (if needed)");
    console.log("3. Test upgrade workflows");
    console.log("4. Deploy additional modules");
    console.log("5. Integrate with admin panel");
    
    // Step 10: Test commands
    console.log("\n🧪 Test Commands:");
    console.log("Schedule upgrade:");
    console.log(`MODULE_ID=SECURITY_TOKEN_CORE NEW_IMPLEMENTATION_ADDRESS=0x... UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress} npx hardhat run scripts/upgrade-module.js --network amoy`);
    
    console.log("\nEmergency upgrade:");
    console.log(`MODULE_ID=SECURITY_TOKEN_CORE NEW_IMPLEMENTATION_ADDRESS=0x... UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress} IS_EMERGENCY=true npx hardhat run scripts/upgrade-module.js --network amoy`);

    console.log("\n✅ Amoy testnet deployment completed successfully!");

  } catch (error) {
    console.error("\n❌ Deployment failed:");
    console.error("Error message:", error.message);
    
    if (error.data) {
      console.error("Error data:", error.data);
    }
    
    if (error.transaction) {
      console.error("Transaction hash:", error.transaction.hash);
    }
    
    if (error.receipt) {
      console.error("Transaction receipt:", error.receipt);
    }
    
    // Additional debugging info
    console.error("\nDebugging info:");
    console.error("Error code:", error.code);
    console.error("Error reason:", error.reason);
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
