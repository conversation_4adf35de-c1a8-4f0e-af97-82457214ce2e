const { ethers } = require("hardhat");

// Token address to test
const TOKEN_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();

  // Create test addresses
  const user1 = ethers.Wallet.createRandom();
  const user2 = ethers.Wallet.createRandom();

  console.log("🔄 Testing Force Transfer Functionality");
  console.log("Token Address:", TOKEN_ADDRESS);
  console.log("Deployer:", deployer.address);
  console.log("User1:", user1.address);
  console.log("User2:", user2.address);

  try {
    // Get the token contract
    const tokenContract = await ethers.getContractAt("SecurityTokenCore", TOKEN_ADDRESS);

    // Check current roles
    console.log("\n🔑 Checking roles...");
    const TRANSFER_MANAGER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("TRANSFER_MANAGER_ROLE"));
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    
    const hasTransferManagerRole = await tokenContract.hasRole(TRANSFER_MANAGER_ROLE, deployer.address);
    const hasAdminRole = await tokenContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    console.log(`Deployer has TRANSFER_MANAGER_ROLE: ${hasTransferManagerRole}`);
    console.log(`Deployer has DEFAULT_ADMIN_ROLE: ${hasAdminRole}`);

    if (!hasTransferManagerRole && !hasAdminRole) {
      console.log("❌ Deployer doesn't have required roles for force transfer");
      return;
    }

    // Check initial balances
    console.log("\n📊 Initial balances:");
    const user1Balance = await tokenContract.balanceOf(user1.address);
    const user2Balance = await tokenContract.balanceOf(user2.address);
    const decimals = await tokenContract.decimals();
    
    console.log(`User1 balance: ${ethers.formatUnits(user1Balance, decimals)} tokens`);
    console.log(`User2 balance: ${ethers.formatUnits(user2Balance, decimals)} tokens`);

    // If user1 has no tokens, mint some first
    if (user1Balance === 0n) {
      console.log("\n🪙 Minting tokens to user1 for testing...");
      const mintTx = await tokenContract.mint(user1.address, ethers.parseUnits("100", decimals));
      await mintTx.wait();
      console.log("✅ Minted 100 tokens to user1");
      
      const newUser1Balance = await tokenContract.balanceOf(user1.address);
      console.log(`User1 new balance: ${ethers.formatUnits(newUser1Balance, decimals)} tokens`);
    }

    // Test force transfer
    console.log("\n🔄 Testing force transfer...");
    const transferAmount = ethers.parseUnits("10", decimals);
    
    console.log(`Force transferring 10 tokens from ${user1.address} to ${user2.address}...`);
    
    try {
      const forceTx = await tokenContract.forcedTransfer(user1.address, user2.address, transferAmount);
      await forceTx.wait();
      
      console.log("✅ Force transfer successful!");
      console.log("Transaction hash:", forceTx.hash);
      
      // Check final balances
      console.log("\n📊 Final balances:");
      const finalUser1Balance = await tokenContract.balanceOf(user1.address);
      const finalUser2Balance = await tokenContract.balanceOf(user2.address);
      
      console.log(`User1 balance: ${ethers.formatUnits(finalUser1Balance, decimals)} tokens`);
      console.log(`User2 balance: ${ethers.formatUnits(finalUser2Balance, decimals)} tokens`);
      
      // Verify the transfer worked correctly
      const expectedUser1Balance = user1Balance - transferAmount;
      const expectedUser2Balance = user2Balance + transferAmount;
      
      if (finalUser1Balance === expectedUser1Balance && finalUser2Balance === expectedUser2Balance) {
        console.log("✅ Force transfer amounts are correct!");
      } else {
        console.log("❌ Force transfer amounts don't match expected values");
        console.log(`Expected User1: ${ethers.formatUnits(expectedUser1Balance, decimals)}, Got: ${ethers.formatUnits(finalUser1Balance, decimals)}`);
        console.log(`Expected User2: ${ethers.formatUnits(expectedUser2Balance, decimals)}, Got: ${ethers.formatUnits(finalUser2Balance, decimals)}`);
      }
      
    } catch (forceError) {
      console.error("❌ Force transfer failed:", forceError.message);
      
      if (forceError.message.includes('AccessControl')) {
        console.log("💡 This might be a permissions issue. Check if the deployer has TRANSFER_MANAGER_ROLE.");
      }
      
      if (forceError.message.includes('insufficient balance')) {
        console.log("💡 The source address doesn't have enough tokens.");
      }
    }

    // Test API force transfer
    console.log("\n🌐 Testing API force transfer...");
    
    try {
      const response = await fetch('http://localhost:6677/api/contracts/token/force-transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: TOKEN_ADDRESS,
          fromAddress: user2.address,
          toAddress: user1.address,
          amount: "5",
          network: 'amoy'
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        console.log("✅ API force transfer successful!");
        console.log("Transaction hash:", result.txHash);
        console.log("New balances:", result.newFromBalance, "->", result.newToBalance);
      } else {
        console.log("❌ API force transfer failed:", result.error);
        if (result.details) {
          console.log("Details:", result.details);
        }
      }
      
    } catch (apiError) {
      console.error("❌ API test failed:", apiError.message);
    }

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
