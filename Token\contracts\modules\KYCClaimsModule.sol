// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "../interfaces/ISecurityTokenModule.sol";

// ClaimRegistry interface
interface IClaimRegistry {
    function hasValidClaim(address subject, uint256 claimType) external view returns (bool);
    function issueClaim(
        address subject,
        uint256 claimType,
        bytes calldata signature,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external returns (bytes32);
    function revokeClaim(address subject, uint256 claimType, bytes32 claimId) external;
}

/**
 * @title KYCClaimsModule
 * @dev Module for handling KYC and custom claims verification for modular security tokens
 *
 * This module integrates with the existing ClaimRegistry to provide:
 * - On-chain KYC verification using Tokeny-style Topic IDs
 * - Custom claims that can be shared across multiple tokens
 * - Hybrid verification (traditional + claim-based)
 */
contract KYCClaimsModule is
    Initializable,
    AccessControlUpgradeable,
    UUPSUpgradeable,
    ISecurityTokenModule
{
    bytes32 public constant MODULE_ADMIN_ROLE = keccak256("MODULE_ADMIN_ROLE");
    bytes32 public constant CLAIM_ISSUER_ROLE = keccak256("CLAIM_ISSUER_ROLE");
    
    // Claim registry contract
    IClaimRegistry public claimRegistry;
    
    // Tokeny-style Topic IDs (matching your existing system)
    uint256 public constant KYC_VERIFICATION = 10101010000001;
    uint256 public constant ACCREDITED_INVESTOR = 10101010000002;
    uint256 public constant JURISDICTION_COMPLIANCE = 10101010000003;
    uint256 public constant GENERAL_QUALIFICATION = 10101010000004;
    uint256 public constant SPECIFIC_KYC_STATUS = 10101010000648;
    
    // Token-specific claim requirements
    mapping(address => uint256[]) public tokenRequiredClaims;
    mapping(address => bool) public tokenKYCEnabled;
    mapping(address => bool) public tokenClaimsEnabled;
    
    // Traditional KYC storage (fallback)
    mapping(address => mapping(address => bool)) public traditionalKYC; // token => user => approved
    mapping(address => mapping(address => bool)) public traditionalWhitelist; // token => user => whitelisted
    
    // Events
    event ClaimRegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event TokenClaimsConfigured(address indexed token, uint256[] requiredClaims, bool kycEnabled, bool claimsEnabled);
    event KYCApproved(address indexed token, address indexed user, bool onChain);
    event WhitelistApproved(address indexed token, address indexed user, bool onChain);
    event ClaimIssued(address indexed user, uint256 indexed claimType, bytes32 claimId);
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    function initialize(
        address _claimRegistry,
        address _admin
    ) public initializer {
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        require(_claimRegistry != address(0), "Invalid claim registry");
        require(_admin != address(0), "Invalid admin");
        
        claimRegistry = IClaimRegistry(_claimRegistry);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(MODULE_ADMIN_ROLE, _admin);
        _grantRole(CLAIM_ISSUER_ROLE, _admin);
    }
    
    function _authorizeUpgrade(address newImplementation) internal override onlyRole(DEFAULT_ADMIN_ROLE) {}
    
    /**
     * @dev Set the claim registry address
     */
    function setClaimRegistry(address _claimRegistry) external onlyRole(MODULE_ADMIN_ROLE) {
        require(_claimRegistry != address(0), "Invalid claim registry");
        address oldRegistry = address(claimRegistry);
        claimRegistry = IClaimRegistry(_claimRegistry);
        emit ClaimRegistryUpdated(oldRegistry, _claimRegistry);
    }
    
    /**
     * @dev Configure claims requirements for a token
     */
    function configureTokenClaims(
        address token,
        uint256[] calldata requiredClaims,
        bool kycEnabled,
        bool claimsEnabled
    ) external onlyRole(MODULE_ADMIN_ROLE) {
        require(token != address(0), "Invalid token address");
        
        tokenRequiredClaims[token] = requiredClaims;
        tokenKYCEnabled[token] = kycEnabled;
        tokenClaimsEnabled[token] = claimsEnabled;
        
        emit TokenClaimsConfigured(token, requiredClaims, kycEnabled, claimsEnabled);
    }
    
    /**
     * @dev Check if user is KYC approved for a token (hybrid check)
     */
    function isKYCApproved(address token, address user) public view returns (bool) {
        if (!tokenKYCEnabled[token]) {
            return true; // KYC not required
        }
        
        // Check traditional KYC first
        bool traditionalApproved = traditionalKYC[token][user];
        
        // Check on-chain claims if enabled
        bool claimApproved = false;
        if (tokenClaimsEnabled[token] && address(claimRegistry) != address(0)) {
            claimApproved = claimRegistry.hasValidClaim(user, KYC_VERIFICATION);
        }
        
        return traditionalApproved || claimApproved;
    }
    
    /**
     * @dev Check if user is whitelisted for a token (hybrid check)
     */
    function isWhitelisted(address token, address user) public view returns (bool) {
        // Check traditional whitelist first
        bool traditionalWhitelisted = traditionalWhitelist[token][user];
        
        // Check on-chain claims if enabled
        bool claimWhitelisted = false;
        if (tokenClaimsEnabled[token] && address(claimRegistry) != address(0)) {
            claimWhitelisted = claimRegistry.hasValidClaim(user, GENERAL_QUALIFICATION);
        }
        
        return traditionalWhitelisted || claimWhitelisted;
    }
    
    /**
     * @dev Check if user meets all token requirements
     */
    function isEligible(address token, address user) external view returns (bool) {
        // Check KYC
        if (!isKYCApproved(token, user)) {
            return false;
        }
        
        // Check whitelist
        if (!isWhitelisted(token, user)) {
            return false;
        }
        
        // Check custom claims if any are required
        if (tokenClaimsEnabled[token] && address(claimRegistry) != address(0)) {
            uint256[] memory requiredClaims = tokenRequiredClaims[token];
            for (uint256 i = 0; i < requiredClaims.length; i++) {
                if (!claimRegistry.hasValidClaim(user, requiredClaims[i])) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * @dev Approve KYC for a user (traditional method)
     */
    function approveKYC(address token, address user) external onlyRole(CLAIM_ISSUER_ROLE) {
        require(token != address(0), "Invalid token");
        require(user != address(0), "Invalid user");
        
        traditionalKYC[token][user] = true;
        
        // Also add to whitelist if not already
        if (!traditionalWhitelist[token][user]) {
            traditionalWhitelist[token][user] = true;
            emit WhitelistApproved(token, user, false);
        }
        
        emit KYCApproved(token, user, false);
        
        // Issue on-chain claim if claims are enabled
        if (tokenClaimsEnabled[token] && address(claimRegistry) != address(0)) {
            _issueKYCClaim(user);
        }
    }
    
    /**
     * @dev Add user to whitelist (traditional method)
     */
    function addToWhitelist(address token, address user) external onlyRole(CLAIM_ISSUER_ROLE) {
        require(token != address(0), "Invalid token");
        require(user != address(0), "Invalid user");
        
        traditionalWhitelist[token][user] = true;
        emit WhitelistApproved(token, user, false);
        
        // Issue on-chain claim if claims are enabled
        if (tokenClaimsEnabled[token] && address(claimRegistry) != address(0)) {
            _issueQualificationClaim(user);
        }
    }
    
    /**
     * @dev Issue KYC claim directly (on-chain method)
     */
    function issueKYCClaim(address user, bytes calldata data) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {
        require(user != address(0), "Invalid user");
        require(address(claimRegistry) != address(0), "Claim registry not set");
        
        bytes32 claimId = claimRegistry.issueClaim(
            user,
            KYC_VERIFICATION,
            "",
            data,
            "",
            0 // Never expires
        );
        
        emit ClaimIssued(user, KYC_VERIFICATION, claimId);
        return claimId;
    }
    
    /**
     * @dev Issue custom claim (on-chain method)
     */
    function issueCustomClaim(
        address user,
        uint256 claimType,
        bytes calldata data,
        string calldata uri,
        uint256 expiresAt
    ) external onlyRole(CLAIM_ISSUER_ROLE) returns (bytes32) {
        require(user != address(0), "Invalid user");
        require(address(claimRegistry) != address(0), "Claim registry not set");
        
        bytes32 claimId = claimRegistry.issueClaim(
            user,
            claimType,
            "",
            data,
            uri,
            expiresAt
        );
        
        emit ClaimIssued(user, claimType, claimId);
        return claimId;
    }
    
    /**
     * @dev Internal function to issue KYC claim
     */
    function _issueKYCClaim(address user) internal {
        try claimRegistry.issueClaim(
            user,
            KYC_VERIFICATION,
            "",
            abi.encode("KYC_APPROVED", block.timestamp),
            "",
            0
        ) returns (bytes32 claimId) {
            emit ClaimIssued(user, KYC_VERIFICATION, claimId);
        } catch {
            // Silently fail - traditional system still works
        }
    }
    
    /**
     * @dev Internal function to issue qualification claim
     */
    function _issueQualificationClaim(address user) internal {
        try claimRegistry.issueClaim(
            user,
            GENERAL_QUALIFICATION,
            "",
            abi.encode("QUALIFIED", block.timestamp),
            "",
            0
        ) returns (bytes32 claimId) {
            emit ClaimIssued(user, GENERAL_QUALIFICATION, claimId);
        } catch {
            // Silently fail - traditional system still works
        }
    }
    
    /**
     * @dev Get verification status for a user
     */
    function getVerificationStatus(address token, address user) external view returns (
        bool kycApproved,
        bool whitelisted,
        bool eligible,
        string memory method
    ) {
        kycApproved = isKYCApproved(token, user);
        whitelisted = isWhitelisted(token, user);
        eligible = this.isEligible(token, user);
        
        // Determine method
        bool hasTraditionalKYC = traditionalKYC[token][user];
        bool hasTraditionalWhitelist = traditionalWhitelist[token][user];
        bool hasClaimKYC = tokenClaimsEnabled[token] && address(claimRegistry) != address(0) && 
                          claimRegistry.hasValidClaim(user, KYC_VERIFICATION);
        bool hasClaimWhitelist = tokenClaimsEnabled[token] && address(claimRegistry) != address(0) && 
                                claimRegistry.hasValidClaim(user, GENERAL_QUALIFICATION);
        
        if ((hasTraditionalKYC || hasTraditionalWhitelist) && (hasClaimKYC || hasClaimWhitelist)) {
            method = "HYBRID";
        } else if (hasTraditionalKYC || hasTraditionalWhitelist) {
            method = "TRADITIONAL";
        } else if (hasClaimKYC || hasClaimWhitelist) {
            method = "CLAIM_BASED";
        } else {
            method = "NONE";
        }
    }
    
    // ISecurityTokenModule implementation
    function moduleId() external pure override returns (bytes32) {
        return keccak256("KYC_CLAIMS_MODULE");
    }
    
    function version() external pure override returns (string memory) {
        return "1.0.0";
    }
}
