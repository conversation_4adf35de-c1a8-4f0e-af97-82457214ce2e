import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function isWhitelisted(address account) external view returns (bool)",
  "function isVerified(address account) external view returns (bool)",
  "function getVerificationStatus(address user) external view returns (bool kycApproved, bool whitelisted, bool eligible, string memory method)"
];

const KYC_CLAIMS_MODULE_ABI = [
  "function getVerificationStatus(address token, address user) external view returns (bool kycApproved, bool whitelisted, bool eligible, string memory method)",
  "function isEligible(address token, address user) external view returns (bool)"
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tokenAddress = searchParams.get('tokenAddress');
    const userAddress = searchParams.get('userAddress');

    if (!tokenAddress || !userAddress) {
      return NextResponse.json(
        { error: 'Token address and user address are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(userAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;

    if (!rpcUrl) {
      return NextResponse.json(
        { error: 'RPC URL not configured' },
        { status: 500 }
      );
    }

    // Setup provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    let status = {
      kycApproved: false,
      whitelisted: false,
      eligible: false,
      method: 'UNKNOWN'
    };

    if (kycModuleAddress && ethers.isAddress(kycModuleAddress)) {
      // Try KYC Claims Module first
      console.log('Checking status via KYC Claims Module');
      const kycModule = new ethers.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, provider);
      
      try {
        const result = await kycModule.getVerificationStatus(tokenAddress, userAddress);
        status = {
          kycApproved: result[0],
          whitelisted: result[1],
          eligible: result[2],
          method: result[3] || 'KYC_CLAIMS_MODULE'
        };
        console.log('Status retrieved via KYC Claims Module:', status);
      } catch (moduleError) {
        console.log('KYC Claims Module failed, falling back to direct token call:', moduleError);
        
        // Fallback to direct token call
        const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);
        
        try {
          // Try the new getVerificationStatus method first
          const result = await tokenContract.getVerificationStatus(userAddress);
          status = {
            kycApproved: result[0],
            whitelisted: result[1],
            eligible: result[2],
            method: result[3] || 'SECURITY_TOKEN_CORE'
          };
          console.log('Status retrieved via SecurityTokenCore.getVerificationStatus:', status);
        } catch (newMethodError) {
          console.log('New method failed, using individual calls:', newMethodError);
          
          // Fallback to individual method calls
          try {
            const [isVerified, isWhitelisted] = await Promise.all([
              tokenContract.isVerified(userAddress).catch(() => false),
              tokenContract.isWhitelisted(userAddress).catch(() => false)
            ]);
            
            status = {
              kycApproved: isVerified,
              whitelisted: isWhitelisted,
              eligible: isVerified && isWhitelisted,
              method: 'IDENTITY_MANAGER'
            };
            console.log('Status retrieved via individual calls:', status);
          } catch (individualError) {
            console.log('Individual calls failed:', individualError);
            status.method = 'ERROR';
          }
        }
      }
    } else {
      // Direct token call only
      console.log('Using direct token call for status check');
      const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, provider);
      
      try {
        // Try the new getVerificationStatus method first
        const result = await tokenContract.getVerificationStatus(userAddress);
        status = {
          kycApproved: result[0],
          whitelisted: result[1],
          eligible: result[2],
          method: result[3] || 'SECURITY_TOKEN_CORE'
        };
        console.log('Status retrieved via SecurityTokenCore.getVerificationStatus:', status);
      } catch (newMethodError) {
        console.log('New method failed, using individual calls:', newMethodError);
        
        // Fallback to individual method calls
        try {
          const [isVerified, isWhitelisted] = await Promise.all([
            tokenContract.isVerified(userAddress).catch(() => false),
            tokenContract.isWhitelisted(userAddress).catch(() => false)
          ]);
          
          status = {
            kycApproved: isVerified,
            whitelisted: isWhitelisted,
            eligible: isVerified && isWhitelisted,
            method: 'IDENTITY_MANAGER'
          };
          console.log('Status retrieved via individual calls:', status);
        } catch (individualError) {
          console.log('Individual calls failed:', individualError);
          status.method = 'ERROR';
        }
      }
    }

    return NextResponse.json({
      success: true,
      userAddress,
      tokenAddress,
      ...status
    });

  } catch (error: any) {
    console.error('Error checking verification status:', error);
    return NextResponse.json(
      { error: `Failed to check verification status: ${error.message}` },
      { status: 500 }
    );
  }
}
