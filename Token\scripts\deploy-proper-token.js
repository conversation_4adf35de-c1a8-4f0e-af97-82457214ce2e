const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Properly Configured Token...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH\n");

  try {
    // Deploy properly configured SecurityTokenCore
    console.log("📦 Deploying SecurityTokenCore with proper configuration...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const tokenCore = await upgrades.deployProxy(SecurityTokenCore, [
      "Augment Premium Security Token",  // name
      "APST",                           // symbol
      0,                               // decimals (whole tokens)
      ethers.parseUnits("********", 0), // maxSupply (10M tokens)
      deployer.address,                // admin
      "3.75 USD",                      // tokenPrice
      "Early Bird: 30%, Standard: 20%, Late: 10%", // bonusTiers
      "Premium ERC-3643 security token with advanced KYC Claims integration, on-chain compliance verification, and cross-token eligibility sharing", // tokenDetails
      "https://augment.finance/assets/token-logo.png" // tokenImageUrl
    ], {
      initializer: 'initialize',
      kind: 'uups'
    });

    await tokenCore.waitForDeployment();
    const tokenCoreAddress = await tokenCore.getAddress();
    console.log("✅ SecurityTokenCore deployed to:", tokenCoreAddress);

    // Verify token configuration
    console.log("\n🔍 Verifying token configuration...");
    const name = await tokenCore.name();
    const symbol = await tokenCore.symbol();
    const decimals = await tokenCore.decimals();
    const maxSupply = await tokenCore.maxSupply();
    const version = await tokenCore.version();
    
    console.log(`✅ Name: ${name}`);
    console.log(`✅ Symbol: ${symbol}`);
    console.log(`✅ Decimals: ${decimals}`);
    console.log(`✅ Max Supply: ${maxSupply.toString()}`);
    console.log(`✅ Version: ${version}`);

    // Check metadata
    console.log("\n📊 Checking token metadata...");
    try {
      const metadata = await tokenCore.getTokenMetadata();
      console.log("✅ Token metadata:", {
        tokenPrice: metadata.tokenPrice,
        bonusTiers: metadata.bonusTiers,
        tokenDetails: metadata.tokenDetails,
        tokenImageUrl: metadata.tokenImageUrl
      });
    } catch (metadataError) {
      console.log("⚠️ Could not retrieve metadata:", metadataError.message);
    }

    // Verify admin roles
    console.log("\n🔑 Verifying admin roles...");
    const DEFAULT_ADMIN_ROLE = await tokenCore.DEFAULT_ADMIN_ROLE();
    const MODULE_MANAGER_ROLE = await tokenCore.MODULE_MANAGER_ROLE();
    const AGENT_ROLE = await tokenCore.AGENT_ROLE();
    const TRANSFER_MANAGER_ROLE = await tokenCore.TRANSFER_MANAGER_ROLE();
    
    const hasDefaultAdmin = await tokenCore.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    const hasModuleManager = await tokenCore.hasRole(MODULE_MANAGER_ROLE, deployer.address);
    const hasAgent = await tokenCore.hasRole(AGENT_ROLE, deployer.address);
    const hasTransferManager = await tokenCore.hasRole(TRANSFER_MANAGER_ROLE, deployer.address);
    
    console.log(`✅ DEFAULT_ADMIN_ROLE: ${hasDefaultAdmin}`);
    console.log(`✅ MODULE_MANAGER_ROLE: ${hasModuleManager}`);
    console.log(`✅ AGENT_ROLE: ${hasAgent}`);
    console.log(`✅ TRANSFER_MANAGER_ROLE: ${hasTransferManager}`);

    // Test basic token functionality
    console.log("\n🧪 Testing basic token functionality...");
    const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
    
    try {
      // Test direct whitelist addition (should work with admin role)
      console.log(`Testing direct whitelist addition for ${TEST_USER}...`);
      const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
      await whitelistTx.wait();
      console.log("✅ Direct whitelist addition successful!");

      // Check if user is whitelisted
      const isWhitelisted = await tokenCore.isWhitelisted(TEST_USER);
      console.log(`✅ User whitelisted: ${isWhitelisted}`);

      // Test minting to whitelisted user
      console.log(`Testing token minting to whitelisted user...`);
      const mintTx = await tokenCore.mint(TEST_USER, 250);
      await mintTx.wait();
      console.log("✅ Token minting successful!");

      const balance = await tokenCore.balanceOf(TEST_USER);
      const totalSupply = await tokenCore.totalSupply();
      console.log(`✅ User balance: ${balance.toString()} tokens`);
      console.log(`✅ Total supply: ${totalSupply.toString()} tokens`);

    } catch (testError) {
      console.log("❌ Some basic tests failed:", testError.message);
    }

    console.log("\n🎉 Properly Configured Token deployment successful!");
    console.log("\n📋 Deployment Summary:");
    console.log("=" .repeat(70));
    console.log(`SecurityTokenCore: ${tokenCoreAddress}`);
    console.log(`Name: ${name}`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Decimals: ${decimals}`);
    console.log(`Max Supply: ${maxSupply.toString()}`);
    console.log(`Admin: ${deployer.address}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log("=" .repeat(70));

    console.log("\n💡 NEXT STEPS:");
    console.log("1. Update admin panel .env.local with new token address:");
    console.log(`   AMOY_SECURITY_TOKEN_CORE_ADDRESS=${tokenCoreAddress}`);
    console.log("2. Keep existing KYC Claims Module and ClaimRegistry addresses");
    console.log("3. Test token information display in admin panel");
    console.log("4. Token metadata should now show properly!");

    return {
      tokenCoreAddress,
      name,
      symbol,
      decimals: decimals.toString(),
      maxSupply: maxSupply.toString(),
      admin: deployer.address
    };

  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
