import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function removeFromWhitelist(address account) external",
  "function isWhitelisted(address account) external view returns (bool)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, userAddress } = await request.json();

    if (!tokenAddress || !userAddress) {
      return NextResponse.json(
        { error: 'Token address and user address are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(userAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Check current whitelist status
    const wasWhitelisted = await tokenContract.isWhitelisted(userAddress);
    console.log('User was whitelisted:', wasWhitelisted);

    if (!wasWhitelisted) {
      return NextResponse.json({
        success: true,
        message: 'User is already not whitelisted',
        wasWhitelisted: false,
        isWhitelisted: false,
        userAddress,
        tokenAddress
      });
    }

    // Remove from whitelist
    const tx = await tokenContract.removeFromWhitelist(userAddress);
    await tx.wait();

    // Verify whitelist removal
    const isWhitelisted = await tokenContract.isWhitelisted(userAddress);

    console.log('User removed from whitelist successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'User removed from whitelist successfully',
      txHash: tx.hash,
      wasWhitelisted: true,
      isWhitelisted: isWhitelisted,
      userAddress,
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error removing from whitelist:', error);
    return NextResponse.json(
      { error: `Failed to remove from whitelist: ${error.message}` },
      { status: 500 }
    );
  }
}
