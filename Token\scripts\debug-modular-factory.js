const { ethers } = require("hardhat");

async function main() {
  console.log("🔍 DEBUGGING MODULAR FACTORY ISSUE...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  const MODULAR_FACTORY_ADDRESS = "******************************************";

  try {
    // Get the factory contract
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(MODULAR_FACTORY_ADDRESS);

    console.log("📋 ModularTokenFactory address:", MODULAR_FACTORY_ADDRESS);

    // Test 1: Check if contract exists and is accessible
    console.log("\n1️⃣ Testing contract accessibility...");
    try {
      const deployer_role = await factory.DEPLOYER_ROLE();
      console.log("✅ Contract accessible, DEPLOYER_ROLE:", deployer_role);
    } catch (accessError) {
      console.log("❌ Contract not accessible:", accessError.message);
      return;
    }

    // Test 2: Check deployer role
    console.log("\n2️⃣ Checking deployer role...");
    try {
      const hasRole = await factory.hasRole(await factory.DEPLOYER_ROLE(), deployer.address);
      console.log(`✅ Has deployer role: ${hasRole}`);
      
      if (!hasRole) {
        console.log("🔧 Granting deployer role...");
        const grantTx = await factory.grantRole(await factory.DEPLOYER_ROLE(), deployer.address);
        await grantTx.wait();
        console.log("✅ Deployer role granted");
      }
    } catch (roleError) {
      console.log("❌ Role check failed:", roleError.message);
    }

    // Test 3: Check implementation addresses
    console.log("\n3️⃣ Checking implementation addresses...");
    try {
      const tokenImpl = await factory.securityTokenImplementation();
      const upgradeManager = await factory.upgradeManager();
      console.log("✅ SecurityToken implementation:", tokenImpl);
      console.log("✅ Upgrade manager:", upgradeManager);
    } catch (implError) {
      console.log("❌ Implementation check failed:", implError.message);
    }

    // Test 4: Try the exact same parameters that are failing
    console.log("\n4️⃣ Testing with EXACT failing parameters...");
    
    const failingParams = {
      name: "modul_03",
      symbol: "modul_03", 
      decimals: 0,
      maxSupply: ethers.parseUnits("108000", 0),
      admin: "******************************************",
      tokenPrice: "6 USD",
      bonusTiers: "Early Bird: 10%, Standard: 5%, Late: 0%",
      tokenDetails: "Security token with advanced compliance features",
      tokenImageUrl: "https://mnp.rs/wp-content/uploads/2024/11/IMG_8634.png"
    };

    console.log("Parameters:", failingParams);

    // Test gas estimation first
    console.log("\n⛽ Testing gas estimation...");
    try {
      const gasEstimate = await factory.deployToken.estimateGas(
        failingParams.name,
        failingParams.symbol,
        failingParams.decimals,
        failingParams.maxSupply,
        failingParams.admin,
        failingParams.tokenPrice,
        failingParams.bonusTiers,
        failingParams.tokenDetails,
        failingParams.tokenImageUrl
      );
      console.log("✅ Gas estimate:", gasEstimate.toString());

      // Try actual deployment with estimated gas
      console.log("\n🚀 Attempting deployment...");
      const gasLimit = (gasEstimate * BigInt(120)) / BigInt(100); // 20% buffer
      
      const deployTx = await factory.deployToken(
        failingParams.name,
        failingParams.symbol,
        failingParams.decimals,
        failingParams.maxSupply,
        failingParams.admin,
        failingParams.tokenPrice,
        failingParams.bonusTiers,
        failingParams.tokenDetails,
        failingParams.tokenImageUrl,
        { gasLimit }
      );

      console.log("✅ Transaction sent:", deployTx.hash);
      
      const receipt = await deployTx.wait();
      console.log("✅ Transaction mined in block:", receipt.blockNumber);

      // Get deployed token address
      const deployEvent = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed && parsed.name === 'TokenDeployed';
        } catch (e) {
          return false;
        }
      });
      
      if (deployEvent) {
        const parsed = factory.interface.parseLog(deployEvent);
        console.log("✅ Token deployed at:", parsed.args.tokenAddress);
      }

    } catch (gasError) {
      console.log("❌ Gas estimation failed:", gasError.message);
      console.log("Error details:", gasError);
      
      // Try with fixed gas
      console.log("\n🔄 Trying with fixed gas (3M)...");
      try {
        const deployTx = await factory.deployToken(
          failingParams.name,
          failingParams.symbol,
          failingParams.decimals,
          failingParams.maxSupply,
          failingParams.admin,
          failingParams.tokenPrice,
          failingParams.bonusTiers,
          failingParams.tokenDetails,
          failingParams.tokenImageUrl,
          { gasLimit: 3000000 }
        );

        console.log("✅ Fixed gas transaction sent:", deployTx.hash);
        const receipt = await deployTx.wait();
        console.log("✅ Transaction mined:", receipt.blockNumber);

      } catch (fixedGasError) {
        console.log("❌ Fixed gas also failed:", fixedGasError.message);
        console.log("Full error:", fixedGasError);
      }
    }

    // Test 5: Check existing tokens
    console.log("\n5️⃣ Checking existing deployed tokens...");
    try {
      const tokenCount = await factory.getDeployedTokensCount();
      console.log("✅ Total deployed tokens:", tokenCount.toString());
      
      if (tokenCount > 0) {
        const tokens = await factory.getDeployedTokens(0, Math.min(5, Number(tokenCount)));
        console.log("✅ Recent tokens:", tokens.tokens.slice(0, 3));
      }
    } catch (countError) {
      console.log("❌ Token count check failed:", countError.message);
    }

  } catch (error) {
    console.error("❌ Debug failed:", error);
    console.log("\n🔍 Full Error Details:");
    console.log("Type:", typeof error);
    console.log("Code:", error.code);
    console.log("Message:", error.message);
    if (error.data) console.log("Data:", error.data);
    if (error.transaction) console.log("Transaction:", error.transaction);
  }
}

// Execute debug
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
