const { ethers } = require("hardhat");

// Token proxy address to upgrade
const PROXY_ADDRESS = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();
  
  console.log("🚀 Upgrading token contract to support direct force transfers");
  console.log("Token Address:", PROXY_ADDRESS);
  console.log("Using deployer:", deployer.address);

  try {
    // Get the current contract
    const currentContract = await ethers.getContractAt("SecurityTokenCore", PROXY_ADDRESS);
    
    // Check current implementation
    const implementationSlot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
    const currentImplementation = await deployer.provider.getStorage(PROXY_ADDRESS, implementationSlot);
    const cleanCurrentImplementation = "0x" + currentImplementation.slice(-40);
    
    console.log("Current implementation:", cleanCurrentImplementation);

    // Check if user can upgrade
    const DEFAULT_ADMIN_ROLE = '0x0000000000000000000000000000000000000000000000000000000000000000';
    const hasAdminRole = await currentContract.hasRole(DEFAULT_ADMIN_ROLE, deployer.address);
    
    if (!hasAdminRole) {
      console.log("❌ Cannot upgrade - deployer doesn't have DEFAULT_ADMIN_ROLE");
      return;
    }
    
    console.log("✅ Deployer has admin role - can proceed with upgrade");

    // Deploy new SecurityTokenCore implementation
    console.log("\n🔧 Deploying new SecurityTokenCore implementation with direct force transfer...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    
    const newImplAddress = await newImplementation.getAddress();
    console.log("New implementation deployed at:", newImplAddress);

    // Upgrade the proxy to the new implementation
    console.log("\n📦 Upgrading proxy to new implementation...");
    const upgradeData = "0x"; // No initialization data needed
    const tx = await currentContract.upgradeToAndCall(newImplAddress, upgradeData);
    await tx.wait();
    
    console.log("✅ Upgrade successful! Transaction:", tx.hash);

    // Verify the upgrade
    const newImplementationStored = await deployer.provider.getStorage(PROXY_ADDRESS, implementationSlot);
    const cleanNewImplementation = "0x" + newImplementationStored.slice(-40);
    console.log("New implementation address:", cleanNewImplementation);

    if (cleanNewImplementation.toLowerCase() === newImplAddress.toLowerCase()) {
      console.log("✅ Implementation upgrade verified!");
    } else {
      console.log("❌ Implementation upgrade verification failed");
      return;
    }

    // Test the new force transfer functionality
    console.log("\n🧪 Testing new force transfer functionality...");
    
    // Create test addresses
    const user1 = ethers.Wallet.createRandom();
    const user2 = ethers.Wallet.createRandom();
    
    console.log("Test User1:", user1.address);
    console.log("Test User2:", user2.address);

    // Mint some tokens to user1 for testing
    console.log("\n🪙 Minting test tokens...");
    const decimals = await currentContract.decimals();
    const mintAmount = ethers.parseUnits("50", decimals);
    
    const mintTx = await currentContract.mint(user1.address, mintAmount);
    await mintTx.wait();
    console.log("✅ Minted 50 tokens to user1");

    // Check balances before force transfer
    const user1BalanceBefore = await currentContract.balanceOf(user1.address);
    const user2BalanceBefore = await currentContract.balanceOf(user2.address);
    
    console.log(`User1 balance before: ${ethers.formatUnits(user1BalanceBefore, decimals)} tokens`);
    console.log(`User2 balance before: ${ethers.formatUnits(user2BalanceBefore, decimals)} tokens`);

    // Test force transfer
    console.log("\n🔄 Testing force transfer...");
    const transferAmount = ethers.parseUnits("10", decimals);
    
    try {
      const forceTx = await currentContract.forcedTransfer(user1.address, user2.address, transferAmount);
      await forceTx.wait();
      
      console.log("✅ Force transfer successful!");
      console.log("Transaction hash:", forceTx.hash);
      
      // Check balances after force transfer
      const user1BalanceAfter = await currentContract.balanceOf(user1.address);
      const user2BalanceAfter = await currentContract.balanceOf(user2.address);
      
      console.log(`User1 balance after: ${ethers.formatUnits(user1BalanceAfter, decimals)} tokens`);
      console.log(`User2 balance after: ${ethers.formatUnits(user2BalanceAfter, decimals)} tokens`);
      
      // Verify the transfer worked correctly
      const expectedUser1Balance = user1BalanceBefore - transferAmount;
      const expectedUser2Balance = user2BalanceBefore + transferAmount;
      
      if (user1BalanceAfter === expectedUser1Balance && user2BalanceAfter === expectedUser2Balance) {
        console.log("✅ Force transfer amounts are correct!");
      } else {
        console.log("❌ Force transfer amounts don't match expected values");
      }
      
    } catch (forceError) {
      console.error("❌ Force transfer test failed:", forceError.message);
    }

    console.log("\n🎉 Upgrade and testing completed!");
    console.log(`🔗 View on Polygonscan: https://amoy.polygonscan.com/address/${PROXY_ADDRESS}`);

  } catch (error) {
    console.error("❌ Upgrade failed:", error.message);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
