import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

// Import ABIs
import SecurityTokenCoreABI from '@/contracts/SecurityTokenCore.json';
import UpgradeManagerABI from '@/contracts/UpgradeManager.json';

// Contract addresses from environment
const SECURITY_TOKEN_CORE_ADDRESS = process.env.AMOY_SECURITY_TOKEN_CORE_ADDRESS;
const UPGRADE_MANAGER_ADDRESS = process.env.AMOY_UPGRADE_MANAGER_ADDRESS;
const AMOY_RPC_URL = process.env.AMOY_RPC_URL;

// Initialize provider
const provider = new ethers.JsonRpcProvider(AMOY_RPC_URL);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (!SECURITY_TOKEN_CORE_ADDRESS || !UPGRADE_MANAGER_ADDRESS) {
      return NextResponse.json(
        { error: 'Contract addresses not configured' },
        { status: 500 }
      );
    }

    switch (action) {
      case 'token-info':
        return await getTokenInfo();
      
      case 'upgrade-info':
        return await getUpgradeInfo();
      
      case 'pending-upgrades':
        return await getPendingUpgrades();
      
      case 'upgrade-history':
        return await getUpgradeHistory();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getTokenInfo() {
  try {
    const contract = new ethers.Contract(SECURITY_TOKEN_CORE_ADDRESS!, SecurityTokenCoreABI, provider);
    
    const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([
      contract.name(),
      contract.symbol(),
      contract.version(),
      contract.totalSupply(),
      contract.maxSupply(),
      contract.decimals(),
      contract.paused(),
      contract.getTokenMetadata()
    ]);

    return NextResponse.json({
      name,
      symbol,
      version,
      totalSupply: ethers.formatUnits(totalSupply, decimals),
      maxSupply: ethers.formatUnits(maxSupply, decimals),
      decimals,
      paused,
      metadata: {
        tokenPrice: metadata[0],
        bonusTiers: metadata[1],
        tokenDetails: metadata[2],
        tokenImageUrl: metadata[3]
      },
      contractAddress: SECURITY_TOKEN_CORE_ADDRESS
    });
  } catch (error) {
    console.error('Error getting token info:', error);
    throw error;
  }
}

async function getUpgradeInfo() {
  try {
    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
    
    const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration] = await Promise.all([
      contract.isEmergencyModeActive(),
      contract.getRegisteredModules(),
      contract.UPGRADE_DELAY(),
      contract.EMERGENCY_MODE_DURATION()
    ]);

    return NextResponse.json({
      emergencyModeActive,
      registeredModules,
      upgradeDelay: Number(upgradeDelay),
      emergencyModeDuration: Number(emergencyModeDuration),
      contractAddress: UPGRADE_MANAGER_ADDRESS
    });
  } catch (error) {
    console.error('Error getting upgrade info:', error);
    throw error;
  }
}

async function getPendingUpgrades() {
  try {
    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
    
    const pendingUpgradeIds = await contract.getPendingUpgradeIds();
    
    const pendingUpgrades = await Promise.all(
      pendingUpgradeIds.map(async (id: string) => {
        const upgrade = await contract.pendingUpgrades(id);
        return {
          upgradeId: id,
          moduleId: upgrade.moduleId,
          proxy: upgrade.proxy,
          newImplementation: upgrade.newImplementation,
          executeTime: Number(upgrade.executeTime),
          executed: upgrade.executed,
          cancelled: upgrade.cancelled,
          description: upgrade.description
        };
      })
    );

    return NextResponse.json({ pendingUpgrades });
  } catch (error) {
    console.error('Error getting pending upgrades:', error);
    throw error;
  }
}

async function getUpgradeHistory() {
  try {
    const contract = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
    
    // Get upgrade history for SecurityTokenCore
    const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
    const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);
    
    const upgradeHistory = history.map((record: any) => ({
      oldImplementation: record.oldImplementation,
      newImplementation: record.newImplementation,
      timestamp: Number(record.timestamp),
      executor: record.executor,
      version: record.version,
      isEmergency: record.isEmergency,
      description: record.description
    }));

    return NextResponse.json({ upgradeHistory });
  } catch (error) {
    console.error('Error getting upgrade history:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...params } = body;

    // Note: POST operations require a private key to sign transactions
    // For security, these should be handled client-side with user's wallet
    // This endpoint can be used for validation or preparation

    switch (action) {
      case 'validate-mint':
        return await validateMint(params);
      
      case 'validate-upgrade':
        return await validateUpgrade(params);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action parameter' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function validateMint(params: { address: string; amount: string }) {
  try {
    const { address, amount } = params;
    
    // Validate address format
    if (!ethers.isAddress(address)) {
      return NextResponse.json(
        { valid: false, error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Validate amount
    const parsedAmount = parseFloat(amount);
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      return NextResponse.json(
        { valid: false, error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Check if minting would exceed max supply
    const contract = new ethers.Contract(SECURITY_TOKEN_CORE_ADDRESS!, SecurityTokenCoreABI, provider);
    const [totalSupply, maxSupply, decimals] = await Promise.all([
      contract.totalSupply(),
      contract.maxSupply(),
      contract.decimals()
    ]);

    const amountWei = ethers.parseUnits(amount, decimals);
    const newTotalSupply = totalSupply + amountWei;

    if (newTotalSupply > maxSupply) {
      return NextResponse.json(
        { 
          valid: false, 
          error: 'Minting would exceed maximum supply',
          currentSupply: ethers.formatUnits(totalSupply, decimals),
          maxSupply: ethers.formatUnits(maxSupply, decimals),
          requestedAmount: amount
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      valid: true,
      currentSupply: ethers.formatUnits(totalSupply, decimals),
      maxSupply: ethers.formatUnits(maxSupply, decimals),
      newTotalSupply: ethers.formatUnits(newTotalSupply, decimals)
    });
  } catch (error) {
    console.error('Error validating mint:', error);
    return NextResponse.json(
      { valid: false, error: 'Validation failed' },
      { status: 500 }
    );
  }
}

async function validateUpgrade(params: { implementationAddress: string; description: string }) {
  try {
    const { implementationAddress, description } = params;
    
    // Validate implementation address format
    if (!ethers.isAddress(implementationAddress)) {
      return NextResponse.json(
        { valid: false, error: 'Invalid implementation address format' },
        { status: 400 }
      );
    }

    // Validate description
    if (!description || description.trim().length < 10) {
      return NextResponse.json(
        { valid: false, error: 'Description must be at least 10 characters' },
        { status: 400 }
      );
    }

    // Check if implementation address has code
    const code = await provider.getCode(implementationAddress);
    if (code === '0x') {
      return NextResponse.json(
        { valid: false, error: 'Implementation address does not contain contract code' },
        { status: 400 }
      );
    }

    // Check if emergency mode is active
    const upgradeManager = new ethers.Contract(UPGRADE_MANAGER_ADDRESS!, UpgradeManagerABI, provider);
    const emergencyModeActive = await upgradeManager.isEmergencyModeActive();

    return NextResponse.json({
      valid: true,
      emergencyModeActive,
      upgradeDelay: emergencyModeActive ? 0 : await upgradeManager.UPGRADE_DELAY()
    });
  } catch (error) {
    console.error('Error validating upgrade:', error);
    return NextResponse.json(
      { valid: false, error: 'Validation failed' },
      { status: 500 }
    );
  }
}
