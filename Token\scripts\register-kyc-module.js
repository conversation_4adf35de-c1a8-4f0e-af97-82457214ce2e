const { ethers } = require("hardhat");

async function main() {
  console.log("🔗 Registering KYC Claims Module with SecurityTokenCore...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Registering with account:", deployer.address);

  const SECURITY_TOKEN_CORE_ADDRESS = "******************************************";
  const KYC_CLAIMS_MODULE_ADDRESS = "******************************************";

  try {
    // Get the SecurityTokenCore contract
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    const tokenCore = SecurityTokenCore.attach(SECURITY_TOKEN_CORE_ADDRESS);

    console.log("📋 Contract addresses:");
    console.log(`- SecurityTokenCore: ${SECURITY_TOKEN_CORE_ADDRESS}`);
    console.log(`- KYCClaimsModule: ${KYC_CLAIMS_MODULE_ADDRESS}`);

    // Get the module ID for KYC Claims Module
    const KYC_CLAIMS_MODULE_ID = ethers.keccak256(ethers.toUtf8Bytes("KYC_CLAIMS_MODULE"));
    console.log(`- Module ID: ${KYC_CLAIMS_MODULE_ID}`);

    // Check if module is already registered
    console.log("\n🔍 Checking current module registration...");
    const currentModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
    console.log(`Current registered module: ${currentModule}`);

    if (currentModule !== ethers.ZeroAddress) {
      console.log("⚠️ Module is already registered. Unregistering first...");
      const unregisterTx = await tokenCore.unregisterModule(KYC_CLAIMS_MODULE_ID);
      await unregisterTx.wait();
      console.log("✅ Module unregistered");
    }

    // Register the KYC Claims Module
    console.log("\n📝 Registering KYC Claims Module...");
    const registerTx = await tokenCore.registerModule(KYC_CLAIMS_MODULE_ID, KYC_CLAIMS_MODULE_ADDRESS);
    await registerTx.wait();
    console.log("✅ KYC Claims Module registered successfully!");

    // Verify registration
    console.log("\n🔍 Verifying registration...");
    const registeredModule = await tokenCore.getModule(KYC_CLAIMS_MODULE_ID);
    const isAuthorized = await tokenCore.isAuthorizedModule(KYC_CLAIMS_MODULE_ADDRESS);
    
    console.log(`✅ Registered module address: ${registeredModule}`);
    console.log(`✅ Is authorized module: ${isAuthorized}`);

    if (registeredModule === KYC_CLAIMS_MODULE_ADDRESS && isAuthorized) {
      console.log("\n🎉 KYC Claims Module successfully registered and authorized!");
      
      // Test the module integration
      console.log("\n🧪 Testing module integration...");
      const TEST_USER = "0x56f3726C92B8B92a6ab71983886F91718540d888";
      
      try {
        // Test KYC approval through the token
        console.log(`Testing KYC approval for ${TEST_USER}...`);
        const kycTx = await tokenCore.approveKYC(TEST_USER);
        await kycTx.wait();
        console.log("✅ KYC approval test successful!");

        // Test whitelist addition through the token
        console.log(`Testing whitelist addition for ${TEST_USER}...`);
        const whitelistTx = await tokenCore.addToWhitelist(TEST_USER);
        await whitelistTx.wait();
        console.log("✅ Whitelist addition test successful!");

        // Check verification status
        console.log("Checking verification status...");
        const status = await tokenCore.getVerificationStatus(TEST_USER);
        console.log("✅ Verification status:", {
          kycApproved: status[0],
          whitelisted: status[1],
          eligible: status[2],
          method: status[3]
        });

      } catch (testError) {
        console.log("⚠️ Module integration test failed:", testError.message);
        console.log("This might be expected if the module needs initialization or configuration.");
      }

    } else {
      console.log("❌ Registration verification failed!");
    }

    console.log("\n📋 Registration Summary:");
    console.log("=" .repeat(60));
    console.log(`SecurityTokenCore: ${SECURITY_TOKEN_CORE_ADDRESS}`);
    console.log(`KYCClaimsModule: ${KYC_CLAIMS_MODULE_ADDRESS}`);
    console.log(`Module ID: ${KYC_CLAIMS_MODULE_ID}`);
    console.log(`Registered: ${registeredModule === KYC_CLAIMS_MODULE_ADDRESS}`);
    console.log(`Authorized: ${isAuthorized}`);
    console.log(`Network: ${(await deployer.provider.getNetwork()).name}`);
    console.log(`Deployer: ${deployer.address}`);
    console.log("=" .repeat(60));

  } catch (error) {
    console.error("❌ Registration failed:", error);
    throw error;
  }
}

// Execute registration
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
