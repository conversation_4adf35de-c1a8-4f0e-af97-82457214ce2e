{"_format": "hh-sol-artifact-1", "contractName": "IClaimRegistry", "sourceName": "contracts/modules/KYCClaimsModule.sol", "abi": [{"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "expiresAt", "type": "uint256"}], "name": "issueClaim", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "subject", "type": "address"}, {"internalType": "uint256", "name": "claimType", "type": "uint256"}, {"internalType": "bytes32", "name": "claimId", "type": "bytes32"}], "name": "revokeClaim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}