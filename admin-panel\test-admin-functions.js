// Comprehensive API tests for all admin functions
const testUserAddress = "******************************************";
const tokenAddress = "******************************************";

async function testPriceUpdate() {
  console.log("💰 Testing Price Update API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/update-price', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        newPrice: "8.75 USD",
      }),
    });

    const result = await response.json();
    console.log("✅ Price Update Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Price Update Error:", error.message);
    return false;
  }
}

async function testBonusTiersUpdate() {
  console.log("🎯 Testing Bonus Tiers Update API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/update-bonus-tiers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        newBonusTiers: "Mega Early: 60%, Super Early: 45%, Early: 30%, Standard: 20%, Late: 10%",
      }),
    });

    const result = await response.json();
    console.log("✅ Bonus Tiers Update Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Bonus Tiers Update Error:", error.message);
    return false;
  }
}

async function testMaxSupplyUpdate() {
  console.log("📈 Testing Max Supply Update API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/update-max-supply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        newMaxSupply: "75000000",
      }),
    });

    const result = await response.json();
    console.log("✅ Max Supply Update Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Max Supply Update Error:", error.message);
    return false;
  }
}

async function testTokenMinting() {
  console.log("🪙 Testing Token Minting API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/mint-tokens', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        toAddress: testUserAddress,
        amount: "1000",
      }),
    });

    const result = await response.json();
    console.log("✅ Token Minting Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Token Minting Error:", error.message);
    return false;
  }
}

async function testPauseToken() {
  console.log("⏸️ Testing Pause Token API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/pause-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ Pause Token Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Pause Token Error:", error.message);
    return false;
  }
}

async function testUnpauseToken() {
  console.log("▶️ Testing Unpause Token API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/unpause-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ Unpause Token Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Unpause Token Error:", error.message);
    return false;
  }
}

async function testAddToWhitelist() {
  console.log("✅ Testing Add to Whitelist API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/add-to-whitelist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        userAddress: testUserAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ Add to Whitelist Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Add to Whitelist Error:", error.message);
    return false;
  }
}

async function testRemoveFromWhitelist() {
  console.log("❌ Testing Remove from Whitelist API...");
  
  try {
    const response = await fetch('http://localhost:6677/api/admin/remove-from-whitelist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tokenAddress: tokenAddress,
        userAddress: testUserAddress,
      }),
    });

    const result = await response.json();
    console.log("✅ Remove from Whitelist Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Remove from Whitelist Error:", error.message);
    return false;
  }
}

async function testGetTokenInfo() {
  console.log("📊 Testing Get Token Info API...");
  
  try {
    const response = await fetch(`http://localhost:6677/api/admin/token-info?tokenAddress=${tokenAddress}`);
    const result = await response.json();
    console.log("✅ Token Info Response:", result);
    return result.success;
  } catch (error) {
    console.error("❌ Token Info Error:", error.message);
    return false;
  }
}

// Run comprehensive admin function tests
async function runAllAdminTests() {
  console.log("🚀 COMPREHENSIVE ADMIN FUNCTIONS API TESTS");
  console.log("=" .repeat(70));
  console.log(`Test User: ${testUserAddress}`);
  console.log(`Token Address: ${tokenAddress}`);
  console.log(`Admin Panel: http://localhost:6677`);
  console.log("");

  const results = {};

  // Test 1: Get initial token info
  console.log("1️⃣ INITIAL TOKEN STATE");
  console.log("-" .repeat(30));
  results.initialInfo = await testGetTokenInfo();

  // Test 2: Price management
  console.log("\n2️⃣ PRICE MANAGEMENT");
  console.log("-" .repeat(30));
  results.priceUpdate = await testPriceUpdate();
  results.bonusTiersUpdate = await testBonusTiersUpdate();

  // Test 3: Supply management
  console.log("\n3️⃣ SUPPLY MANAGEMENT");
  console.log("-" .repeat(30));
  results.maxSupplyUpdate = await testMaxSupplyUpdate();
  results.tokenMinting = await testTokenMinting();

  // Test 4: Token controls
  console.log("\n4️⃣ TOKEN CONTROLS");
  console.log("-" .repeat(30));
  results.pauseToken = await testPauseToken();
  results.unpauseToken = await testUnpauseToken();

  // Test 5: Whitelist management
  console.log("\n5️⃣ WHITELIST MANAGEMENT");
  console.log("-" .repeat(30));
  results.addToWhitelist = await testAddToWhitelist();
  results.removeFromWhitelist = await testRemoveFromWhitelist();

  // Test 6: Final token state
  console.log("\n6️⃣ FINAL TOKEN STATE");
  console.log("-" .repeat(30));
  results.finalInfo = await testGetTokenInfo();

  // Summary
  console.log("\n🎉 ADMIN FUNCTIONS TEST SUMMARY");
  console.log("=" .repeat(70));
  
  const successCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`✅ Successful Tests: ${successCount}/${totalTests}`);
  console.log("");
  
  Object.entries(results).forEach(([test, success]) => {
    const status = success ? "✅ PASS" : "❌ FAIL";
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`${status} - ${testName}`);
  });

  console.log("\n💡 NEXT STEPS:");
  if (successCount === totalTests) {
    console.log("🎯 All admin functions are working perfectly!");
    console.log("🔗 You can now use the Admin Controls tab in the admin panel");
    console.log("📋 All functions are ready for production use");
  } else {
    console.log("⚠️ Some functions need API endpoints to be created");
    console.log("🔧 Create the missing API routes for full functionality");
  }

  console.log("=" .repeat(70));
  
  return results;
}

// Execute tests
runAllAdminTests().catch(console.error);
