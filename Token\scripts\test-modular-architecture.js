const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🧪 Testing Modular Architecture");
  console.log("===============================");

  const [deployer, user1, user2] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

  try {
    // Step 1: Deploy SecurityTokenCore
    console.log("\n📋 Step 1: Deploying SecurityTokenCore...");
    const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
    
    const securityTokenCore = await upgrades.deployProxy(
      SecurityTokenCore,
      [
        "Test Modular Token", // name
        "TMT", // symbol
        0, // decimals
        ethers.parseUnits("1000000", 0), // maxSupply
        deployer.address, // admin
        "5 USD", // tokenPrice
        "Early: 10%, Regular: 5%", // bonusTiers
        "Test token for modular architecture demonstration", // tokenDetails
        "https://example.com/test-logo.png" // tokenImageUrl
      ],
      { initializer: "initialize", kind: "uups" }
    );

    await securityTokenCore.waitForDeployment();
    const tokenAddress = await securityTokenCore.getAddress();
    console.log("✅ SecurityTokenCore deployed to:", tokenAddress);

    // Step 2: Deploy UpgradeManager
    console.log("\n📋 Step 2: Deploying UpgradeManager...");
    const UpgradeManager = await ethers.getContractFactory("UpgradeManager");
    
    const upgradeManager = await upgrades.deployProxy(
      UpgradeManager,
      [deployer.address],
      { initializer: "initialize", kind: "uups" }
    );

    await upgradeManager.waitForDeployment();
    const upgradeManagerAddress = await upgradeManager.getAddress();
    console.log("✅ UpgradeManager deployed to:", upgradeManagerAddress);

    // Step 3: Register SecurityTokenCore with UpgradeManager
    console.log("\n📋 Step 3: Registering SecurityTokenCore with UpgradeManager...");
    const SECURITY_TOKEN_CORE_ID = ethers.keccak256(ethers.toUtf8Bytes("SECURITY_TOKEN_CORE"));
    
    await upgradeManager.registerModule(SECURITY_TOKEN_CORE_ID, tokenAddress);
    console.log("✅ SecurityTokenCore registered with UpgradeManager");
    
    // Verify registration
    const registeredAddress = await upgradeManager.moduleProxies(SECURITY_TOKEN_CORE_ID);
    console.log("Registered address:", registeredAddress);
    console.log("Registration verified:", registeredAddress === tokenAddress);

    // Step 4: Test basic token functionality
    console.log("\n📋 Step 4: Testing basic token functionality...");
    
    // Test minting (should work since no modules are registered yet)
    console.log("Minting 1000 tokens to deployer...");
    await securityTokenCore.mint(deployer.address, ethers.parseUnits("1000", 0));
    console.log("Deployer balance:", ethers.formatUnits(await securityTokenCore.balanceOf(deployer.address), 0));
    
    // Test transfer validation
    console.log("Testing canTransfer...");
    const canTransfer = await securityTokenCore.canTransfer(
      deployer.address, 
      user1.address, 
      ethers.parseUnits("100", 0)
    );
    console.log("Can transfer 100 tokens:", canTransfer);
    
    // Test pause functionality
    console.log("Testing pause functionality...");
    await securityTokenCore.pause();
    console.log("Token paused:", await securityTokenCore.paused());
    
    const canTransferWhenPaused = await securityTokenCore.canTransfer(
      deployer.address, 
      user1.address, 
      ethers.parseUnits("100", 0)
    );
    console.log("Can transfer when paused:", canTransferWhenPaused);
    
    await securityTokenCore.unpause();
    console.log("Token unpaused:", await securityTokenCore.paused());

    // Step 5: Test module delegation (will fail gracefully since modules aren't deployed)
    console.log("\n📋 Step 5: Testing module delegation...");
    
    try {
      console.log("Testing whitelist delegation...");
      const isWhitelisted = await securityTokenCore.isWhitelisted(user1.address);
      console.log("User1 whitelisted:", isWhitelisted);
    } catch (error) {
      console.log("Whitelist check failed (expected - no module):", error.message.substring(0, 50) + "...");
    }
    
    try {
      console.log("Testing agent delegation...");
      const isAgent = await securityTokenCore.isAgent(user1.address);
      console.log("User1 is agent:", isAgent);
    } catch (error) {
      console.log("Agent check failed (expected - no module):", error.message.substring(0, 50) + "...");
    }

    // Step 6: Test upgrade workflow simulation
    console.log("\n📋 Step 6: Testing upgrade workflow simulation...");
    
    // Deploy a new implementation (same contract for testing)
    console.log("Deploying new implementation...");
    const newImplementation = await SecurityTokenCore.deploy();
    await newImplementation.waitForDeployment();
    const newImplAddress = await newImplementation.getAddress();
    console.log("New implementation deployed to:", newImplAddress);
    
    // Schedule an upgrade
    console.log("Scheduling upgrade...");
    const upgradeTx = await upgradeManager.scheduleUpgrade(
      SECURITY_TOKEN_CORE_ID,
      newImplAddress,
      "Test upgrade to demonstrate workflow"
    );
    
    const receipt = await upgradeTx.wait();
    console.log("Upgrade scheduled, gas used:", receipt.gasUsed.toString());
    
    // Find the upgrade ID from events
    const upgradeScheduledEvent = receipt.logs.find(log => {
      try {
        const parsed = upgradeManager.interface.parseLog(log);
        return parsed.name === "UpgradeScheduled";
      } catch {
        return false;
      }
    });
    
    if (upgradeScheduledEvent) {
      const parsedEvent = upgradeManager.interface.parseLog(upgradeScheduledEvent);
      const upgradeId = parsedEvent.args.upgradeId;
      const executeTime = parsedEvent.args.executeTime;
      
      console.log("Upgrade ID:", upgradeId);
      console.log("Execute time:", new Date(Number(executeTime) * 1000).toISOString());
      
      // Check pending upgrade
      const pendingUpgrade = await upgradeManager.pendingUpgrades(upgradeId);
      console.log("Pending upgrade details:");
      console.log("- Module ID:", pendingUpgrade.moduleId);
      console.log("- Proxy:", pendingUpgrade.proxy);
      console.log("- New Implementation:", pendingUpgrade.newImplementation);
      console.log("- Executed:", pendingUpgrade.executed);
      console.log("- Cancelled:", pendingUpgrade.cancelled);
      console.log("- Description:", pendingUpgrade.description);
    }

    // Step 7: Test emergency upgrade workflow
    console.log("\n📋 Step 7: Testing emergency upgrade workflow...");
    
    // Activate emergency mode
    console.log("Activating emergency mode...");
    await upgradeManager.activateEmergencyMode();
    console.log("Emergency mode active:", await upgradeManager.isEmergencyModeActive());
    
    // Deploy another implementation for emergency upgrade
    const emergencyImplementation = await SecurityTokenCore.deploy();
    await emergencyImplementation.waitForDeployment();
    const emergencyImplAddress = await emergencyImplementation.getAddress();
    console.log("Emergency implementation deployed to:", emergencyImplAddress);
    
    // Note: We won't actually execute the emergency upgrade as it would fail
    // in our test environment, but we can show the workflow
    console.log("Emergency upgrade would be executed with:");
    console.log(`await upgradeManager.emergencyUpgrade("${SECURITY_TOKEN_CORE_ID}", "${emergencyImplAddress}", "Emergency fix");`);
    
    // Deactivate emergency mode
    await upgradeManager.deactivateEmergencyMode();
    console.log("Emergency mode deactivated");

    // Step 8: Test module management
    console.log("\n📋 Step 8: Testing module management...");
    
    // Get registered modules
    const registeredModules = await upgradeManager.getRegisteredModules();
    console.log("Registered modules count:", registeredModules.length);
    console.log("Registered modules:", registeredModules);
    
    // Get pending upgrades
    const pendingUpgradeIds = await upgradeManager.getPendingUpgradeIds();
    console.log("Pending upgrades count:", pendingUpgradeIds.length);
    
    // Get upgrade history
    const upgradeHistory = await upgradeManager.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);
    console.log("Upgrade history count:", upgradeHistory.length);

    // Step 9: Summary and results
    console.log("\n📋 Step 9: Test Summary");
    console.log("======================");
    
    console.log("✅ SecurityTokenCore deployed and functional");
    console.log("✅ UpgradeManager deployed and functional");
    console.log("✅ Module registration working");
    console.log("✅ Basic token operations working");
    console.log("✅ Pause/unpause functionality working");
    console.log("✅ Module delegation architecture in place");
    console.log("✅ Upgrade scheduling working");
    console.log("✅ Emergency mode functionality working");
    console.log("✅ Module management working");

    console.log("\n🎯 Deployment Addresses:");
    console.log(`SecurityTokenCore: ${tokenAddress}`);
    console.log(`UpgradeManager: ${upgradeManagerAddress}`);
    
    console.log("\n📝 Environment Variables:");
    console.log(`SECURITY_TOKEN_CORE_ADDRESS=${tokenAddress}`);
    console.log(`UPGRADE_MANAGER_ADDRESS=${upgradeManagerAddress}`);
    
    console.log("\n🚀 Next Steps:");
    console.log("1. Deploy individual modules (IdentityManager, ComplianceEngine, etc.)");
    console.log("2. Register modules with UpgradeManager");
    console.log("3. Register modules with SecurityTokenCore");
    console.log("4. Test complete end-to-end workflows");
    console.log("5. Deploy to testnet for full testing");

    console.log("\n✅ Modular architecture test completed successfully!");

  } catch (error) {
    console.error("\n❌ Test failed:");
    console.error(error.message);
    
    if (error.data) {
      console.error("Error data:", error.data);
    }
    
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
