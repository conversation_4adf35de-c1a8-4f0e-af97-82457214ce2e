const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("ERC-3643 Integration Tests", function () {
    let deployer, user1, user2, user3, agent;
    let claimRegistry, identityRegistry, compliance, securityToken;
    
    const TOKEN_NAME = "Test Security Token";
    const TOKEN_SYMBOL = "TST";
    const TOKEN_DECIMALS = 0;
    const TOKEN_MAX_SUPPLY = ethers.parseUnits("1000000", TOKEN_DECIMALS);
    const USA_COUNTRY_CODE = 840;
    const CANADA_COUNTRY_CODE = 124;

    beforeEach(async function () {
        [deployer, user1, user2, user3, agent] = await ethers.getSigners();

        // Deploy ClaimRegistry
        const ClaimRegistry = await ethers.getContractFactory("ClaimRegistry");
        claimRegistry = await upgrades.deployProxy(
            ClaimRegistry,
            [deployer.address],
            { initializer: "initialize", kind: "uups" }
        );
        await claimRegistry.waitForDeployment();

        // Deploy IdentityRegistry
        const IdentityRegistry = await ethers.getContractFactory("IdentityRegistry");
        identityRegistry = await upgrades.deployProxy(
            IdentityRegistry,
            [deployer.address, await claimRegistry.getAddress()],
            { initializer: "initialize", kind: "uups" }
        );
        await identityRegistry.waitForDeployment();

        // Deploy Compliance
        const Compliance = await ethers.getContractFactory("Compliance");
        compliance = await upgrades.deployProxy(
            Compliance,
            [deployer.address, await identityRegistry.getAddress()],
            { initializer: "initialize", kind: "uups" }
        );
        await compliance.waitForDeployment();

        // Deploy SecurityToken
        const SecurityToken = await ethers.getContractFactory("SecurityToken");
        securityToken = await upgrades.deployProxy(
            SecurityToken,
            [
                TOKEN_NAME,
                TOKEN_SYMBOL,
                TOKEN_DECIMALS,
                TOKEN_MAX_SUPPLY,
                await identityRegistry.getAddress(),
                await compliance.getAddress(),
                deployer.address,
                "10 USD",
                "Tier 1: 5%",
                "Test token details",
                ""
            ],
            { initializer: "initialize", kind: "uups" }
        );
        await securityToken.waitForDeployment();

        // Grant agent role
        await securityToken.grantRole(await securityToken.AGENT_ROLE(), agent.address);
    });

    describe("Identity Registry", function () {
        it("Should register and verify identities", async function () {
            // Register identity
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            
            expect(await identityRegistry.isVerified(user1.address)).to.be.true;
            expect(await identityRegistry.investorCountry(user1.address)).to.equal(USA_COUNTRY_CODE);
            expect(await identityRegistry.isWhitelisted(user1.address)).to.be.false;
        });

        it("Should manage whitelist status", async function () {
            // Register and whitelist
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            await identityRegistry.addToWhitelist(user1.address);
            
            expect(await identityRegistry.isWhitelisted(user1.address)).to.be.true;
            
            // Remove from whitelist
            await identityRegistry.removeFromWhitelist(user1.address);
            expect(await identityRegistry.isWhitelisted(user1.address)).to.be.false;
        });

        it("Should manage KYC status", async function () {
            // Register and approve KYC
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            await identityRegistry.approveKyc(user1.address);
            
            expect(await identityRegistry.isKycApproved(user1.address)).to.be.true;
            
            // Revoke KYC
            await identityRegistry.revokeKyc(user1.address);
            expect(await identityRegistry.isKycApproved(user1.address)).to.be.false;
        });

        it("Should handle country restrictions", async function () {
            // Restrict a country
            await identityRegistry.restrictCountry(CANADA_COUNTRY_CODE);
            expect(await identityRegistry.isCountryRestricted(CANADA_COUNTRY_CODE)).to.be.true;
            
            // Should fail to register from restricted country
            await expect(
                identityRegistry.registerIdentity(user1.address, CANADA_COUNTRY_CODE)
            ).to.be.revertedWith("IdentityRegistry: country is restricted");
            
            // Unrestrict country
            await identityRegistry.unrestrictCountry(CANADA_COUNTRY_CODE);
            expect(await identityRegistry.isCountryRestricted(CANADA_COUNTRY_CODE)).to.be.false;
            
            // Should now work
            await identityRegistry.registerIdentity(user1.address, CANADA_COUNTRY_CODE);
            expect(await identityRegistry.isVerified(user1.address)).to.be.true;
        });

        it("Should handle batch operations", async function () {
            // Register multiple identities
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            await identityRegistry.registerIdentity(user2.address, USA_COUNTRY_CODE);
            
            // Batch add to whitelist
            await identityRegistry.batchAddToWhitelist([user1.address, user2.address]);
            
            expect(await identityRegistry.isWhitelisted(user1.address)).to.be.true;
            expect(await identityRegistry.isWhitelisted(user2.address)).to.be.true;
            
            // Batch approve KYC
            await identityRegistry.batchApproveKyc([user1.address, user2.address]);
            
            expect(await identityRegistry.isKycApproved(user1.address)).to.be.true;
            expect(await identityRegistry.isKycApproved(user2.address)).to.be.true;
        });
    });

    describe("Compliance", function () {
        beforeEach(async function () {
            // Set up test users
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            await identityRegistry.addToWhitelist(user1.address);
            await identityRegistry.approveKyc(user1.address);
            
            await identityRegistry.registerIdentity(user2.address, USA_COUNTRY_CODE);
            await identityRegistry.addToWhitelist(user2.address);
            await identityRegistry.approveKyc(user2.address);
        });

        it("Should check transfer compliance", async function () {
            // Should allow transfer between compliant addresses
            expect(await compliance.canTransfer(user1.address, user2.address, 100)).to.be.true;
            
            // Should reject transfer to non-verified address
            expect(await compliance.canTransfer(user1.address, user3.address, 100)).to.be.false;
        });

        it("Should manage compliance rules", async function () {
            const ruleId = ethers.keccak256(ethers.toUtf8Bytes("TEST_RULE"));
            
            // Add compliance rule
            await compliance.addComplianceRule(
                ruleId,
                "Test Rule",
                100, // max holders
                ethers.parseUnits("10000", TOKEN_DECIMALS), // max tokens per holder
                ethers.parseUnits("1000000", TOKEN_DECIMALS) // max total supply
            );
            
            const rule = await compliance.getComplianceRule(ruleId);
            expect(rule[0]).to.be.true; // isActive
            expect(rule[1]).to.equal(100); // maxHolders
        });

        it("Should track holder statistics", async function () {
            // Initially no holders
            expect(await compliance.getTotalHolders()).to.equal(0);
            
            // Simulate token creation (would be called by SecurityToken)
            await compliance.created(user1.address, 1000);
            expect(await compliance.getTotalHolders()).to.equal(1);
            
            // Simulate transfer
            await compliance.transferred(user1.address, user2.address, 500);
            expect(await compliance.getTotalHolders()).to.equal(2);
        });
    });

    describe("SecurityToken Integration", function () {
        beforeEach(async function () {
            // Set up compliant users
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            await identityRegistry.addToWhitelist(user1.address);
            await identityRegistry.approveKyc(user1.address);
            
            await identityRegistry.registerIdentity(user2.address, USA_COUNTRY_CODE);
            await identityRegistry.addToWhitelist(user2.address);
            await identityRegistry.approveKyc(user2.address);
        });

        it("Should mint tokens to compliant addresses", async function () {
            await securityToken.connect(agent).mint(user1.address, 1000);
            expect(await securityToken.balanceOf(user1.address)).to.equal(1000);
        });

        it("Should reject minting to non-compliant addresses", async function () {
            await expect(
                securityToken.connect(agent).mint(user3.address, 1000)
            ).to.be.revertedWith("SecurityToken: recipient not verified");
        });

        it("Should allow transfers between compliant addresses", async function () {
            // Mint tokens
            await securityToken.connect(agent).mint(user1.address, 1000);
            
            // Transfer should work
            await securityToken.connect(user1).transfer(user2.address, 500);
            
            expect(await securityToken.balanceOf(user1.address)).to.equal(500);
            expect(await securityToken.balanceOf(user2.address)).to.equal(500);
        });

        it("Should reject transfers to non-compliant addresses", async function () {
            // Mint tokens
            await securityToken.connect(agent).mint(user1.address, 1000);
            
            // Transfer to non-compliant address should fail
            await expect(
                securityToken.connect(user1).transfer(user3.address, 500)
            ).to.be.revertedWith("SecurityToken: transfer not compliant");
        });

        it("Should handle forced transfers", async function () {
            // Mint tokens
            await securityToken.connect(agent).mint(user1.address, 1000);
            
            // Freeze user1
            await identityRegistry.freezeAddress(user1.address);
            
            // Normal transfer should fail
            await expect(
                securityToken.connect(user1).transfer(user2.address, 500)
            ).to.be.revertedWith("SecurityToken: transfer not compliant");
            
            // Forced transfer should work
            await securityToken.forcedTransfer(user1.address, user2.address, 500);
            
            expect(await securityToken.balanceOf(user1.address)).to.equal(500);
            expect(await securityToken.balanceOf(user2.address)).to.equal(500);
        });

        it("Should integrate with claims system", async function () {
            // Issue claims
            await claimRegistry.issueClaim(
                user1.address,
                1, // KYC_CLAIM
                "0x", // empty signature
                ethers.AbiCoder.defaultAbiCoder().encode(["string"], ["KYC_APPROVED"]),
                "", // empty URI
                0 // never expires
            );
            
            // Check if user has valid claims
            expect(await identityRegistry.hasValidClaims(user1.address)).to.be.true;
        });
    });

    describe("End-to-End Compliance Flow", function () {
        it("Should handle complete investor onboarding", async function () {
            // 1. Register identity
            await identityRegistry.registerIdentity(user1.address, USA_COUNTRY_CODE);
            expect(await identityRegistry.isVerified(user1.address)).to.be.true;
            
            // 2. Issue KYC claim
            await claimRegistry.issueClaim(
                user1.address,
                1, // KYC_CLAIM
                "0x",
                ethers.AbiCoder.defaultAbiCoder().encode(["string"], ["KYC_APPROVED"]),
                "",
                0
            );
            
            // 3. Approve KYC
            await identityRegistry.approveKyc(user1.address);
            expect(await identityRegistry.isKycApproved(user1.address)).to.be.true;
            
            // 4. Add to whitelist
            await identityRegistry.addToWhitelist(user1.address);
            expect(await identityRegistry.isWhitelisted(user1.address)).to.be.true;
            
            // 5. Check compliance
            expect(await securityToken.canTransfer(deployer.address, user1.address, 1000)).to.be.true;
            
            // 6. Mint tokens
            await securityToken.connect(agent).mint(user1.address, 1000);
            expect(await securityToken.balanceOf(user1.address)).to.equal(1000);
        });
    });
});
