const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 Testing ModularTokenFactory...\n");

  const [deployer] = await ethers.getSigners();
  console.log("Testing with account:", deployer.address);

  const FACTORY_ADDRESS = "******************************************";

  try {
    // Get the factory contract
    const ModularTokenFactory = await ethers.getContractFactory("ModularTokenFactory");
    const factory = ModularTokenFactory.attach(FACTORY_ADDRESS);

    console.log("📋 Factory address:", FACTORY_ADDRESS);

    // Test basic factory functions
    console.log("\n🔍 Testing factory basic functions...");
    
    try {
      const hasRole = await factory.hasRole(await factory.DEPLOYER_ROLE(), deployer.address);
      console.log(`✅ Has deployer role: ${hasRole}`);
      
      if (!hasRole) {
        console.log("🔧 Granting deployer role...");
        const grantTx = await factory.grantRole(await factory.DEPLOYER_ROLE(), deployer.address);
        await grantTx.wait();
        console.log("✅ Deployer role granted");
      }
    } catch (roleError) {
      console.log("⚠️ Role check failed:", roleError.message);
    }

    // Test simple token deployment
    console.log("\n🚀 Testing simple token deployment...");
    
    const tokenParams = {
      name: "Test Token Simple",
      symbol: "TTS",
      decimals: 0,
      maxSupply: ethers.parseUnits("1000000", 0),
      admin: deployer.address,
      tokenPrice: "1.00 USD",
      bonusTiers: "Early: 10%, Standard: 5%",
      tokenDetails: "Simple test token",
      tokenImageUrl: "https://example.com/token.png"
    };

    console.log("Token parameters:", tokenParams);

    // Try to estimate gas first
    console.log("\n⛽ Estimating gas...");
    try {
      const gasEstimate = await factory.deployToken.estimateGas(
        tokenParams.name,
        tokenParams.symbol,
        tokenParams.decimals,
        tokenParams.maxSupply,
        tokenParams.admin,
        tokenParams.tokenPrice,
        tokenParams.bonusTiers,
        tokenParams.tokenDetails,
        tokenParams.tokenImageUrl
      );
      console.log("✅ Gas estimate:", gasEstimate.toString());

      // Deploy with estimated gas + buffer
      const gasLimit = (gasEstimate * BigInt(120)) / BigInt(100); // 20% buffer
      console.log("Using gas limit:", gasLimit.toString());

      const deployTx = await factory.deployToken(
        tokenParams.name,
        tokenParams.symbol,
        tokenParams.decimals,
        tokenParams.maxSupply,
        tokenParams.admin,
        tokenParams.tokenPrice,
        tokenParams.bonusTiers,
        tokenParams.tokenDetails,
        tokenParams.tokenImageUrl,
        { gasLimit }
      );

      console.log("✅ Deployment transaction sent:", deployTx.hash);
      
      const receipt = await deployTx.wait();
      console.log("✅ Transaction mined in block:", receipt.blockNumber);

      // Get the deployed token address from events
      const deployEvent = receipt.logs.find(log => {
        try {
          const parsed = factory.interface.parseLog(log);
          return parsed && parsed.name === 'TokenDeployed';
        } catch (e) {
          return false;
        }
      });

      let tokenAddress;
      if (deployEvent) {
        const parsed = factory.interface.parseLog(deployEvent);
        tokenAddress = parsed.args.tokenAddress;
      } else {
        // Fallback: get from factory storage
        const deployedTokens = await factory.getDeployedTokens(0, 100);
        tokenAddress = deployedTokens.tokens[deployedTokens.tokens.length - 1];
      }
      console.log("✅ Token deployed at:", tokenAddress);

      // Test the deployed token
      console.log("\n🔍 Testing deployed token...");
      const SecurityTokenCore = await ethers.getContractFactory("SecurityTokenCore");
      const token = SecurityTokenCore.attach(tokenAddress);

      const name = await token.name();
      const symbol = await token.symbol();
      const decimals = await token.decimals();
      const maxSupply = await token.maxSupply();

      console.log("✅ Token info:");
      console.log(`  Name: ${name}`);
      console.log(`  Symbol: ${symbol}`);
      console.log(`  Decimals: ${decimals}`);
      console.log(`  Max Supply: ${maxSupply.toString()}`);

    } catch (gasError) {
      console.log("❌ Gas estimation failed:", gasError.message);
      
      // Try with fixed gas limit
      console.log("\n🔄 Trying with fixed gas limit...");
      try {
        const deployTx = await factory.deployToken(
          tokenParams.name,
          tokenParams.symbol,
          tokenParams.decimals,
          tokenParams.maxSupply,
          tokenParams.admin,
          tokenParams.tokenPrice,
          tokenParams.bonusTiers,
          tokenParams.tokenDetails,
          tokenParams.tokenImageUrl,
          { gasLimit: 3000000 } // 3M gas
        );

        console.log("✅ Deployment transaction sent:", deployTx.hash);

        const receipt = await deployTx.wait();
        console.log("✅ Transaction mined in block:", receipt.blockNumber);

        // Get token address from events
        const deployEvent = receipt.logs.find(log => {
          try {
            const parsed = factory.interface.parseLog(log);
            return parsed && parsed.name === 'TokenDeployed';
          } catch (e) {
            return false;
          }
        });

        let tokenAddress;
        if (deployEvent) {
          const parsed = factory.interface.parseLog(deployEvent);
          tokenAddress = parsed.args.tokenAddress;
        } else {
          const deployedTokens = await factory.getDeployedTokens(0, 100);
          tokenAddress = deployedTokens.tokens[deployedTokens.tokens.length - 1];
        }

        console.log("✅ Token deployed at:", tokenAddress);

      } catch (fixedGasError) {
        console.log("❌ Fixed gas deployment failed:", fixedGasError.message);
      }
    }

  } catch (error) {
    console.error("❌ Factory test failed:", error);
    
    // Provide detailed error analysis
    console.log("\n🔍 Error Analysis:");
    console.log("Error type:", typeof error);
    console.log("Error code:", error.code);
    console.log("Error message:", error.message);
    
    if (error.data) {
      console.log("Error data:", error.data);
    }
    
    if (error.transaction) {
      console.log("Transaction data:", error.transaction);
    }
  }
}

// Execute test
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
