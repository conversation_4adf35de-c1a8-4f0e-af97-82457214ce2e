// Test script to verify admin functions work with updated ABI
const { ethers } = require('ethers');
const SecurityTokenCoreABI = require('./src/contracts/SecurityTokenCore.json');

async function testAdminFunctions() {
  console.log("🧪 Testing Admin Functions with Updated ABI...\n");

  const tokenAddress = "******************************************";
  const rpcUrl = "https://rpc-amoy.polygon.technology";
  const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

  if (!privateKey) {
    console.log("❌ CONTRACT_ADMIN_PRIVATE_KEY not found in environment");
    return;
  }

  try {
    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    console.log("📋 Testing with:");
    console.log(`Token Address: ${tokenAddress}`);
    console.log(`Admin Address: ${signer.address}`);
    console.log("");

    // Create contract instance
    const contract = new ethers.Contract(tokenAddress, SecurityTokenCoreABI.abi, signer);

    // Test 1: Check if functions exist in ABI
    console.log("1️⃣ Checking if admin functions exist in ABI...");
    const hasUpdatePrice = contract.interface.fragments.some(f => f.name === 'updateTokenPrice');
    const hasUpdateTiers = contract.interface.fragments.some(f => f.name === 'updateBonusTiers');
    const hasUpdateSupply = contract.interface.fragments.some(f => f.name === 'updateMaxSupply');
    
    console.log(`✅ updateTokenPrice: ${hasUpdatePrice}`);
    console.log(`✅ updateBonusTiers: ${hasUpdateTiers}`);
    console.log(`✅ updateMaxSupply: ${hasUpdateSupply}`);

    if (!hasUpdatePrice || !hasUpdateTiers || !hasUpdateSupply) {
      console.log("❌ Some admin functions are missing from ABI!");
      return;
    }

    // Test 2: Get current token info
    console.log("\n2️⃣ Getting current token information...");
    const name = await contract.name();
    const symbol = await contract.symbol();
    const currentMetadata = await contract.getTokenMetadata();
    const currentMaxSupply = await contract.maxSupply();

    console.log(`✅ Token: ${name} (${symbol})`);
    console.log(`✅ Current Price: ${currentMetadata[0]}`);
    console.log(`✅ Current Bonus Tiers: ${currentMetadata[1]}`);
    console.log(`✅ Current Max Supply: ${currentMaxSupply.toString()}`);

    // Test 3: Test price update function
    console.log("\n3️⃣ Testing price update function...");
    try {
      const newPrice = "9.99 USD";
      console.log(`Updating price to: ${newPrice}`);
      
      const priceTx = await contract.updateTokenPrice(newPrice);
      console.log(`✅ Transaction sent: ${priceTx.hash}`);
      
      const receipt = await priceTx.wait();
      console.log(`✅ Transaction confirmed in block: ${receipt.blockNumber}`);

      // Verify the update
      const updatedMetadata = await contract.getTokenMetadata();
      console.log(`✅ New price: ${updatedMetadata[0]}`);

    } catch (priceError) {
      console.log(`❌ Price update failed: ${priceError.message}`);
    }

    // Test 4: Test bonus tiers update function
    console.log("\n4️⃣ Testing bonus tiers update function...");
    try {
      const newTiers = "Ultra Early: 70%, Super Early: 50%, Early: 35%, Standard: 25%, Late: 15%";
      console.log(`Updating bonus tiers to: ${newTiers}`);
      
      const tiersTx = await contract.updateBonusTiers(newTiers);
      console.log(`✅ Transaction sent: ${tiersTx.hash}`);
      
      const receipt = await tiersTx.wait();
      console.log(`✅ Transaction confirmed in block: ${receipt.blockNumber}`);

      // Verify the update
      const updatedMetadata = await contract.getTokenMetadata();
      console.log(`✅ New bonus tiers: ${updatedMetadata[1]}`);

    } catch (tiersError) {
      console.log(`❌ Bonus tiers update failed: ${tiersError.message}`);
    }

    // Test 5: Test max supply update function
    console.log("\n5️⃣ Testing max supply update function...");
    try {
      const newMaxSupply = ethers.parseUnits("100000000", 0); // 100M tokens
      console.log(`Updating max supply to: ${newMaxSupply.toString()}`);
      
      const supplyTx = await contract.updateMaxSupply(newMaxSupply);
      console.log(`✅ Transaction sent: ${supplyTx.hash}`);
      
      const receipt = await supplyTx.wait();
      console.log(`✅ Transaction confirmed in block: ${receipt.blockNumber}`);

      // Verify the update
      const updatedMaxSupply = await contract.maxSupply();
      console.log(`✅ New max supply: ${updatedMaxSupply.toString()}`);

    } catch (supplyError) {
      console.log(`❌ Max supply update failed: ${supplyError.message}`);
    }

    console.log("\n🎉 Admin Functions Test Complete!");
    console.log("=" .repeat(50));
    console.log("✅ All admin functions are now available in the ABI");
    console.log("✅ Functions can be called successfully");
    console.log("✅ The modular-tokens page should now work properly");
    console.log("=" .repeat(50));

  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

// Run the test
testAdminFunctions().catch(console.error);
