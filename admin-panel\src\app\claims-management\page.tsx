'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface ClaimType {
  id: string;
  name: string;
  description: string;
  creator: string;
  createdAt: string;
  active: boolean;
}

interface Client {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  walletAddress: string;
  kycStatus: string;
}

const ClaimsManagementPage = () => {
  const [claimTypes, setClaimTypes] = useState<ClaimType[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedClaimType, setSelectedClaimType] = useState<string>('');
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [issuingClaim, setIssuingClaim] = useState(false);

  // Fetch claim types and clients
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [claimTypesRes, clientsRes] = await Promise.all([
          fetch('/api/claim-types?limit=100'),
          fetch('/api/clients')
        ]);

        if (claimTypesRes.ok) {
          const claimTypesData = await claimTypesRes.json();
          setClaimTypes(claimTypesData.claimTypes || []);
        }

        if (clientsRes.ok) {
          const clientsData = await clientsRes.json();
          setClients(clientsData.clients || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Issue claim to client
  const handleIssueClaim = async () => {
    if (!selectedClaimType || !selectedClient) {
      alert('Please select both a claim type and a client');
      return;
    }

    try {
      setIssuingClaim(true);
      const client = clients.find(c => c.id === selectedClient);
      
      const response = await fetch('/api/claims', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: client?.walletAddress,
          claimType: selectedClaimType,
          data: JSON.stringify({
            clientId: selectedClient,
            issuedAt: new Date().toISOString(),
            issuer: 'admin'
          })
        }),
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Claim issued successfully! Transaction: ${result.transactionHash}`);
        setSelectedClaimType('');
        setSelectedClient('');
      } else {
        const errorData = await response.json();
        alert(`Failed to issue claim: ${errorData.error}`);
      }
    } catch (error) {
      console.error('Error issuing claim:', error);
      alert('Error issuing claim');
    } finally {
      setIssuingClaim(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Claims Management</h1>
              <p className="text-gray-600 mt-2">
                Manage custom claim types and issue claims to qualified investors
              </p>
            </div>
            <Link
              href="/modular-tokens"
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Back to Token Creation
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Available Claim Types */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Claim Types</h2>
            
            {claimTypes.length > 0 ? (
              <div className="space-y-3">
                {claimTypes.map((claimType) => (
                  <div
                    key={claimType.id}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {claimType.name}
                          <span className="text-sm text-gray-500 ml-2">(ID: {claimType.id})</span>
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">{claimType.description}</p>
                        <p className="text-xs text-gray-400 mt-2">
                          Created: {new Date(claimType.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        claimType.active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {claimType.active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No claim types available.</p>
                <p className="text-sm mt-1">Create claim types in the token creation form.</p>
              </div>
            )}
          </div>

          {/* Issue Claims */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Issue Claims to Investors</h2>
            
            <div className="space-y-4">
              {/* Select Claim Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Claim Type
                </label>
                <select
                  value={selectedClaimType}
                  onChange={(e) => setSelectedClaimType(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  disabled={issuingClaim}
                >
                  <option value="">Choose a claim type...</option>
                  {claimTypes.filter(ct => ct.active).map((claimType) => (
                    <option key={claimType.id} value={claimType.id}>
                      {claimType.name} (ID: {claimType.id})
                    </option>
                  ))}
                </select>
              </div>

              {/* Select Client */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Investor
                </label>
                <select
                  value={selectedClient}
                  onChange={(e) => setSelectedClient(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  disabled={issuingClaim}
                >
                  <option value="">Choose an investor...</option>
                  {clients.filter(client => client.kycStatus === 'APPROVED' && client.walletAddress).map((client) => (
                    <option key={client.id} value={client.id}>
                      {client.firstName} {client.lastName} ({client.email})
                    </option>
                  ))}
                </select>
              </div>

              {/* Issue Button */}
              <button
                onClick={handleIssueClaim}
                disabled={!selectedClaimType || !selectedClient || issuingClaim}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {issuingClaim ? 'Issuing Claim...' : 'Issue Claim'}
              </button>

              {/* Info */}
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>How it works:</strong> Issue claims to qualified investors.
                  Once an investor has a claim, they can automatically access all tokens requiring that claim type.
                </p>
              </div>

              {/* KYC Status Info */}
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> Only showing investors with APPROVED KYC status and connected wallets.
                  Claims are separate from KYC - they represent investment eligibility categories.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl font-bold text-blue-600">{claimTypes.length}</div>
            <div className="text-sm text-gray-600">Total Claim Types</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl font-bold text-green-600">
              {claimTypes.filter(ct => ct.active).length}
            </div>
            <div className="text-sm text-gray-600">Active Claim Types</div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="text-3xl font-bold text-purple-600">
              {clients.filter(c => c.kycStatus === 'APPROVED' && c.walletAddress).length}
            </div>
            <div className="text-sm text-gray-600">Qualified Investors</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClaimsManagementPage;
